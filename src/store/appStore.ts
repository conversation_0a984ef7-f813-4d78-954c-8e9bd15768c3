import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

// Types
export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  imageUrl?: string
}

export interface Entry {
  id: string
  type: 'calendar' | 'todo' | 'text'
  title: string
  content: string
  metadata: {
    priority?: 'low' | 'medium' | 'high'
    dueDate?: string
    time?: string
    tags?: string[]
    category?: string
    completed?: boolean
    favorited?: boolean
  }
  createdAt: number
  updatedAt: number
}

export interface Note {
  id: string
  userId: string
  createdAt: number
  updatedAt: number
  rawText?: string
  blocks: Block[]
  tags?: string[]
  category?: string
}

export interface Block {
  id: string
  type: 'text' | 'todo' | 'calendar' | 'image'
  content: string
  metadata?: Record<string, any>
}

export interface OfflineAction {
  id: string
  type: 'CREATE' | 'UPDATE' | 'DELETE'
  endpoint: string
  data: any
  timestamp: number
}

export interface AppState {
  // User state
  user: User | null
  isAuthenticated: boolean

  // App state
  isLoading: boolean
  isRefreshing: boolean
  error: string | null
  isOnline: boolean

  // Data state
  entries: Entry[]
  notes: Note[]
  currentNote: Note | null
  lastRefreshTime: number | null

  // Offline state
  offlineQueue: OfflineAction[]

  // UI state
  activeTab: 'dashboard' | 'notes' | 'calendar' | 'todos'
  theme: 'light' | 'dark'
  magicInputOpen: boolean
  magicInputContext: 'general' | 'calendar' | 'todos' | 'notes'
  
  // Actions
  setUser: (user: User | null) => void
  setAuthenticated: (authenticated: boolean) => void
  setLoading: (loading: boolean) => void
  setRefreshing: (refreshing: boolean) => void
  setError: (error: string | null) => void
  setOnline: (online: boolean) => void
  setLastRefreshTime: (time: number) => void

  // Entries actions
  setEntries: (entries: Entry[]) => void
  addEntry: (entry: Entry) => void
  updateEntry: (id: string, updates: Partial<Entry>) => void
  deleteEntry: (id: string) => void

  // Notes actions
  setNotes: (notes: Note[]) => void
  addNote: (note: Note) => void
  updateNote: (id: string, updates: Partial<Note>) => void
  deleteNote: (id: string) => void
  setCurrentNote: (note: Note | null) => void
  
  // Offline actions
  addOfflineAction: (action: Omit<OfflineAction, 'id' | 'timestamp'>) => void
  removeOfflineAction: (id: string) => void
  clearOfflineQueue: () => void
  
  // UI actions
  setActiveTab: (tab: 'dashboard' | 'notes' | 'calendar' | 'todos') => void
  setTheme: (theme: 'light' | 'dark') => void
  toggleTheme: () => void
  setMagicInputOpen: (open: boolean) => void
  setMagicInputContext: (context: 'general' | 'calendar' | 'todos' | 'notes') => void
  openMagicInputWithContext: (context: 'general' | 'calendar' | 'todos' | 'notes') => void
  
  // App initialization
  initializeApp: () => void
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      isRefreshing: false,
      error: null,
      isOnline: navigator.onLine,
      entries: [],
      notes: [],
      currentNote: null,
      lastRefreshTime: null,
      offlineQueue: [],
      activeTab: 'dashboard',
      theme: 'light',
      magicInputOpen: false,
      magicInputContext: 'general',

      // User actions
      setUser: (user) => set({ user }),
      setAuthenticated: (authenticated) => set({ isAuthenticated: authenticated }),
      setLoading: (loading) => set({ isLoading: loading }),
      setRefreshing: (refreshing) => set({ isRefreshing: refreshing }),
      setError: (error) => set({ error }),
      setOnline: (online) => set({ isOnline: online }),
      setLastRefreshTime: (time) => set({ lastRefreshTime: time }),

      // Entries actions
      setEntries: (entries) => set({ entries }),
      addEntry: (entry) => set((state) => ({ entries: [entry, ...state.entries] })),
      updateEntry: (id, updates) => set((state) => ({
        entries: state.entries.map(entry =>
          entry.id === id ? { ...entry, ...updates } : entry
        )
      })),
      deleteEntry: (id) => set((state) => ({
        entries: state.entries.filter(entry => entry.id !== id)
      })),

      // Notes actions
      setNotes: (notes) => set({ notes }),
      addNote: (note) => set((state) => ({ notes: [note, ...state.notes] })),
      updateNote: (id, updates) => set((state) => ({
        notes: state.notes.map(note =>
          note.id === id ? { ...note, ...updates } : note
        )
      })),
      deleteNote: (id) => set((state) => ({
        notes: state.notes.filter(note => note.id !== id)
      })),
      setCurrentNote: (note) => set({ currentNote: note }),

      // Offline actions
      addOfflineAction: (action) => set((state) => ({
        offlineQueue: [...state.offlineQueue, {
          ...action,
          id: crypto.randomUUID(),
          timestamp: Date.now()
        }]
      })),
      removeOfflineAction: (id) => set((state) => ({
        offlineQueue: state.offlineQueue.filter(action => action.id !== id)
      })),
      clearOfflineQueue: () => set({ offlineQueue: [] }),

      // UI actions
      setActiveTab: (tab) => set({ activeTab: tab }),
      setTheme: (theme) => {
        set({ theme })
        // Apply theme to document
        if (theme === 'dark') {
          document.documentElement.classList.add('dark')
        } else {
          document.documentElement.classList.remove('dark')
        }
      },
      toggleTheme: () => {
        const currentTheme = get().theme
        const newTheme = currentTheme === 'light' ? 'dark' : 'light'
        get().setTheme(newTheme)
      },
      setMagicInputOpen: (open) => set({ magicInputOpen: open }),
      setMagicInputContext: (context) => set({ magicInputContext: context }),
      openMagicInputWithContext: (context) => set({
        magicInputOpen: true,
        magicInputContext: context
      }),

      // App initialization
      initializeApp: () => {
        const state = get()
        
        // Apply saved theme
        if (state.theme === 'dark') {
          document.documentElement.classList.add('dark')
        }
        
        // Set up online/offline listeners
        const handleOnline = () => state.setOnline(true)
        const handleOffline = () => state.setOnline(false)
        
        window.addEventListener('online', handleOnline)
        window.addEventListener('offline', handleOffline)
        
        // Process offline queue if online
        if (state.isOnline && state.offlineQueue.length > 0) {
          // This would be handled by the API service
          console.log('Processing offline queue:', state.offlineQueue)
        }
      }
    }),
    {
      name: 'synapse-app-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        theme: state.theme,
        activeTab: state.activeTab,
        notes: state.notes,
        offlineQueue: state.offlineQueue
      })
    }
  )
)
