import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(timestamp: number): string {
  const now = Date.now()
  const diff = now - timestamp
  
  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour
  const week = 7 * day
  const month = 30 * day
  
  if (diff < hour) {
    const minutes = Math.floor(diff / minute)
    return minutes <= 1 ? '1 minute ago' : `${minutes} minutes ago`
  } else if (diff < day) {
    const hours = Math.floor(diff / hour)
    return hours === 1 ? '1 hour ago' : `${hours} hours ago`
  } else if (diff < week) {
    const days = Math.floor(diff / day)
    return days === 1 ? '1 day ago' : `${days} days ago`
  } else if (diff < month) {
    const weeks = Math.floor(diff / week)
    return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`
  } else {
    const months = Math.floor(diff / month)
    return months === 1 ? '1 month ago' : `${months} months ago`
  }
}

export function generateId(): string {
  return crypto.randomUUID()
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: ReturnType<typeof setTimeout>
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// Dashboard-specific date utilities
export function isToday(timestamp: number | string): boolean {
  const date = new Date(timestamp)
  const today = new Date()
  return date.toDateString() === today.toDateString()
}

export function isTomorrow(timestamp: number | string): boolean {
  const date = new Date(timestamp)
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  return date.toDateString() === tomorrow.toDateString()
}

export function isThisWeek(timestamp: number | string): boolean {
  const date = new Date(timestamp)
  const now = new Date()
  const weekStart = new Date(now)
  weekStart.setDate(now.getDate() - now.getDay())
  weekStart.setHours(0, 0, 0, 0)

  const weekEnd = new Date(weekStart)
  weekEnd.setDate(weekStart.getDate() + 6)
  weekEnd.setHours(23, 59, 59, 999)

  return date >= weekStart && date <= weekEnd
}

export function getDaysOverdue(dueDate: number | string): number {
  const due = new Date(dueDate)
  const now = new Date()
  const diffTime = now.getTime() - due.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return Math.max(0, diffDays)
}

export function formatTodayDate(): string {
  const today = new Date()
  const options: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  return today.toLocaleDateString('en-US', options)
}

export function formatTime(timestamp: number | string): string {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
}

export function isTodayOrFuture(timestamp: number | string): boolean {
  const date = new Date(timestamp)
  const today = new Date()
  today.setHours(0, 0, 0, 0) // Start of today
  return date >= today
}
