// Development configuration
import { env } from './environment'

export const isDevelopment = env.isDevelopment
export const isProduction = env.isProduction

// Development flags - now based on environment configuration
export const DEV_CONFIG = {
  // Skip authentication in development (opposite of enableAuth)
  SKIP_AUTH: !env.clerk.enableAuth,

  // Use mock API instead of real API (opposite of enableAI)
  USE_MOCK_API: !env.ai.enableAI,

  // Enable debug logging
  DEBUG_LOGGING: env.features.debugLogging,

  // Mock user for development
  MOCK_USER: {
    id: 'dev-user-123',
    email: '<EMAIL>',
    firstName: 'Dev',
    lastName: 'User',
    imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
  }
}

// Log development mode
if (isDevelopment && env.features.debugLogging) {
  console.log('🚀 Running in development mode')
  console.log('📝 Development Configuration:', DEV_CONFIG)
  console.log('🔧 Environment Settings:', {
    authEnabled: env.clerk.enableAuth,
    aiEnabled: env.ai.enableAI,
    apiBaseUrl: env.api.baseUrl
  })
}
