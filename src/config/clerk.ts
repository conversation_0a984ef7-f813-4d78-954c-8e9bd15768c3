// ===========================================
// CLERK AUTHENTICATION CONFIGURATION
// ===========================================

import { env } from './environment'

// Clerk configuration
export const clerkConfig = {
  publishableKey: env.clerk.publishableKey,
  
  // Clerk appearance customization
  appearance: {
    baseTheme: undefined, // Will be set dynamically based on app theme
    variables: {
      colorPrimary: '#3B82F6', // Match app's primary color
      colorBackground: '#F9FAFB',
      colorInputBackground: '#FFFFFF',
      colorInputText: '#1F2937',
      borderRadius: '0.5rem'
    },
    elements: {
      formButtonPrimary: 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700',
      card: 'shadow-xl border-0',
      headerTitle: 'text-2xl font-bold text-gray-900',
      headerSubtitle: 'text-gray-600',
      socialButtonsBlockButton: 'border border-gray-300 hover:bg-gray-50',
      formFieldInput: 'border border-gray-300 focus:border-blue-500 focus:ring-blue-500',
      footerActionLink: 'text-blue-600 hover:text-blue-700'
    }
  },
  
  // Localization
  localization: {
    signIn: {
      start: {
        title: 'Welcome to Synapse',
        subtitle: 'Sign in to access your AI-powered notes'
      }
    },
    signUp: {
      start: {
        title: 'Join Synapse',
        subtitle: 'Create your account to start organizing your thoughts with AI'
      }
    }
  }
}

// Helper function to check if Clerk is properly configured
export const isClerkConfigured = (): boolean => {
  return Boolean(env.clerk.publishableKey && env.clerk.publishableKey !== 'your_clerk_publishable_key')
}

// Helper function to get Clerk configuration with theme
export const getClerkConfig = (theme: 'light' | 'dark' = 'light') => {
  return {
    ...clerkConfig,
    appearance: {
      ...clerkConfig.appearance,
      baseTheme: theme === 'dark' ? 'dark' : 'light',
      variables: {
        ...clerkConfig.appearance.variables,
        colorBackground: theme === 'dark' ? '#111827' : '#F9FAFB',
        colorInputBackground: theme === 'dark' ? '#1F2937' : '#FFFFFF',
        colorInputText: theme === 'dark' ? '#F9FAFB' : '#1F2937'
      }
    }
  }
}

// Development helper
if (env.isDevelopment && env.features.debugLogging) {
  console.log('🔐 Clerk Configuration:', {
    configured: isClerkConfigured(),
    publishableKey: env.clerk.publishableKey ? `${env.clerk.publishableKey.substring(0, 20)}...` : 'Not set',
    authEnabled: env.clerk.enableAuth
  })
}
