// ===========================================
// GOOGLE GEMINI AI CONFIGURATION
// ===========================================

import { env } from './environment'

// Gemini AI configuration
export const geminiConfig = {
  apiKey: env.ai.geminiApiKey,
  baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
  model: 'gemini-1.5-flash', // Using stable model with wider availability
  
  // Default generation parameters
  generationConfig: {
    temperature: 0.7,
    topK: 40,
    topP: 0.95,
    maxOutputTokens: 1024,
    responseMimeType: 'application/json'
  },
  
  // Safety settings
  safetySettings: [
    {
      category: 'HARM_CATEGORY_HARASSMENT',
      threshold: 'BLOCK_MEDIUM_AND_ABOVE'
    },
    {
      category: 'HARM_CATEGORY_HATE_SPEECH',
      threshold: 'BLOCK_MEDIUM_AND_ABOVE'
    },
    {
      category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
      threshold: 'BLOCK_MEDIUM_AND_ABOVE'
    },
    {
      category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
      threshold: 'BLOCK_MEDIUM_AND_ABOVE'
    }
  ]
}

// Content analysis prompt templates
export const promptTemplates = {
  contentAnalysis: `
You are a content analysis AI. Analyze the following text and determine its type and extract relevant information.

Text: "{content}"

You must respond with ONLY a valid JSON object in this exact format (no additional text or explanation):
{
  "type": "text|todo|calendar",
  "title": "extracted or generated title (max 50 chars)",
  "content": "cleaned and formatted content",
  "metadata": {
    "priority": "low|medium|high" (for todos),
    "dueDate": "YYYY-MM-DD" (for todos/calendar, if mentioned),
    "time": "HH:MM" (for calendar events, if mentioned),
    "tags": ["tag1", "tag2"] (relevant tags),
    "category": "work|personal|health|finance|other"
  }
}

IMPORTANT RULES:
- Respond with ONLY valid JSON, no markdown formatting or additional text
- If the text mentions tasks, todos, or things to do, set type to "todo"
- If the text mentions meetings, appointments, events, or dates, set type to "calendar"
- Otherwise, set type to "text"
- Extract or generate a meaningful title (max 50 characters)
- Clean up the content (fix typos, improve formatting)
- Add relevant metadata based on the content
- Ensure all JSON strings are properly escaped
`,

  contextualAnalysis: `
You are a content analysis AI. Analyze the following text with the given context and extract relevant information.

Context: {context}
Text: "{content}"

You must respond with ONLY a valid JSON object in this exact format (no additional text or explanation):
{
  "type": "{context}",
  "title": "extracted or generated title (max 50 chars)",
  "content": "cleaned and formatted content",
  "metadata": {
    "priority": "low|medium|high" (for todos),
    "dueDate": "YYYY-MM-DD" (if mentioned),
    "time": "HH:MM" (for calendar events, if mentioned),
    "tags": ["tag1", "tag2"] (relevant tags),
    "category": "work|personal|health|finance|other"
  }
}

IMPORTANT RULES:
- Respond with ONLY valid JSON, no markdown formatting or additional text
- Use the provided context as the type
- Extract or generate a meaningful title (max 50 characters)
- Clean up the content (fix typos, improve formatting)
- Add relevant metadata based on the content and context
- Ensure all JSON strings are properly escaped
`
}

// Helper function to check if Gemini is properly configured
export const isGeminiConfigured = (): boolean => {
  return Boolean(env.ai.geminiApiKey && env.ai.geminiApiKey !== 'your_gemini_api_key')
}

// Helper function to get API endpoint (without API key in URL)
export const getGeminiEndpoint = (): string => {
  return `${geminiConfig.baseUrl}/models/${geminiConfig.model}:generateContent`
}

// Helper function to get API headers with proper authentication
export const getGeminiHeaders = (): Record<string, string> => {
  return {
    'Content-Type': 'application/json',
    'X-goog-api-key': geminiConfig.apiKey
  }
}

// Helper function to create request payload
export const createGeminiPayload = (prompt: string) => {
  return {
    contents: [
      {
        parts: [
          {
            text: prompt
          }
        ]
      }
    ],
    generationConfig: geminiConfig.generationConfig,
    safetySettings: geminiConfig.safetySettings
  }
}

// Development helper
if (env.isDevelopment && env.features.debugLogging) {
  console.log('🤖 Gemini AI Configuration:', {
    configured: isGeminiConfigured(),
    apiKey: env.ai.geminiApiKey ? `${env.ai.geminiApiKey.substring(0, 20)}...` : 'Not set',
    aiEnabled: env.ai.enableAI,
    model: geminiConfig.model
  })
}
