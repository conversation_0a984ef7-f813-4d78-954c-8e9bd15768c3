// ===========================================
// ENVIRONMENT CONFIGURATION MANAGEMENT
// ===========================================

export interface EnvironmentConfig {
  // Environment info
  isDevelopment: boolean
  isProduction: boolean
  environment: string
  
  // Authentication
  clerk: {
    publishableKey: string
    enableAuth: boolean
  }
  
  // AI Services
  ai: {
    geminiApiKey: string
    enableAI: boolean
  }
  
  // API Configuration
  api: {
    baseUrl: string
  }
  
  // Feature flags
  features: {
    debugLogging: boolean
    devMode: boolean
  }
}

// Get environment variables with fallbacks
const getEnvVar = (key: string, fallback: string = ''): string => {
  return import.meta.env[key] || fallback
}

const getBooleanEnvVar = (key: string, fallback: boolean = false): boolean => {
  const value = import.meta.env[key]
  if (value === undefined) return fallback
  return value === 'true' || value === '1'
}

// Create environment configuration
export const createEnvironmentConfig = (): EnvironmentConfig => {
  const isDevelopment = import.meta.env.DEV
  const isProduction = import.meta.env.PROD
  
  return {
    // Environment info
    isDevelopment,
    isProduction,
    environment: getEnvVar('VITE_ENVIRONMENT', isDevelopment ? 'development' : 'production'),
    
    // Authentication
    clerk: {
      publishableKey: getEnvVar('VITE_CLERK_PUBLISHABLE_KEY'),
      enableAuth: getBooleanEnvVar('VITE_ENABLE_AUTH', false)
    },
    
    // AI Services
    ai: {
      geminiApiKey: getEnvVar('VITE_GEMINI_API_KEY'),
      enableAI: getBooleanEnvVar('VITE_ENABLE_AI', false)
    },
    
    // API Configuration
    api: {
      baseUrl: getEnvVar('VITE_API_BASE_URL', 'http://localhost:5173')
    },
    
    // Feature flags
    features: {
      debugLogging: getBooleanEnvVar('VITE_DEBUG_LOGGING', isDevelopment),
      devMode: getBooleanEnvVar('VITE_DEV_MODE', isDevelopment)
    }
  }
}

// Export the configuration
export const env = createEnvironmentConfig()

// Validation function
export const validateEnvironment = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []
  
  // Check required environment variables
  if (env.clerk.enableAuth && !env.clerk.publishableKey) {
    errors.push('VITE_CLERK_PUBLISHABLE_KEY is required when authentication is enabled')
  }
  
  if (env.ai.enableAI && !env.ai.geminiApiKey) {
    errors.push('VITE_GEMINI_API_KEY is required when AI processing is enabled')
  }
  
  if (!env.api.baseUrl) {
    errors.push('VITE_API_BASE_URL is required')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

// Log environment configuration (development only)
if (env.isDevelopment && env.features.debugLogging) {
  console.log('🌍 Environment Configuration:', {
    environment: env.environment,
    authEnabled: env.clerk.enableAuth,
    aiEnabled: env.ai.enableAI,
    apiBaseUrl: env.api.baseUrl,
    devMode: env.features.devMode
  })
  
  const validation = validateEnvironment()
  if (!validation.isValid) {
    console.warn('⚠️ Environment validation errors:', validation.errors)
  } else {
    console.log('✅ Environment configuration is valid')
  }
}
