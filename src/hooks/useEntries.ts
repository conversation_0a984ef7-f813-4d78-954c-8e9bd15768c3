import { useMemo } from 'react'
import { useAppStore } from '@/store/appStore'
import type { Entry } from '@/store/appStore'

export interface UseEntriesOptions {
  type?: 'calendar' | 'todo' | 'text'
  limit?: number
  sortBy?: 'createdAt' | 'updatedAt' | 'title'
  sortOrder?: 'asc' | 'desc'
}

export function useEntries(options: UseEntriesOptions = {}) {
  const { entries } = useAppStore()
  const {
    type,
    limit,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = options

  const filteredEntries = useMemo(() => {
    let filtered = [...entries]

    // Filter by type
    if (type) {
      filtered = filtered.filter(entry => entry.type === type)
    }

    // Sort entries
    filtered.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortBy) {
        case 'title':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'updatedAt':
          aValue = a.updatedAt
          bValue = b.updatedAt
          break
        case 'createdAt':
        default:
          aValue = a.createdAt
          bValue = b.createdAt
          break
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    // Apply limit
    if (limit) {
      filtered = filtered.slice(0, limit)
    }

    return filtered
  }, [entries, type, limit, sortBy, sortOrder])

  // Helper functions for specific entry types
  const calendarEntries = useMemo(() => 
    entries.filter(entry => entry.type === 'calendar'),
    [entries]
  )

  const todoEntries = useMemo(() => 
    entries.filter(entry => entry.type === 'todo'),
    [entries]
  )

  const textEntries = useMemo(() => 
    entries.filter(entry => entry.type === 'text'),
    [entries]
  )

  // Statistics
  const stats = useMemo(() => {
    const total = entries.length
    const calendar = calendarEntries.length
    const todos = todoEntries.length
    const completed = todoEntries.filter(entry => entry.metadata.completed).length
    const pending = todos - completed
    const notes = textEntries.length

    return {
      total,
      calendar,
      todos,
      completed,
      pending,
      notes
    }
  }, [entries, calendarEntries, todoEntries, textEntries])

  // Recent entries (last 7 days)
  const recentEntries = useMemo(() => {
    const sevenDaysAgo = Date.now() - 7 * 24 * 60 * 60 * 1000
    return entries.filter(entry => entry.createdAt > sevenDaysAgo)
  }, [entries])

  // Upcoming calendar events (next 7 days including today)
  const upcomingEvents = useMemo(() => {
    const today = new Date()
    today.setHours(0, 0, 0, 0) // Start of today
    const sevenDaysFromNow = new Date(today)
    sevenDaysFromNow.setDate(today.getDate() + 7)

    return calendarEntries
      .filter(entry => {
        const dueDate = entry.metadata.dueDate
        if (!dueDate) return false
        const eventDate = new Date(dueDate)
        eventDate.setHours(0, 0, 0, 0) // Start of event day
        return eventDate >= today && eventDate < sevenDaysFromNow
      })
      .sort((a, b) => {
        const aDate = new Date(a.metadata.dueDate!).getTime()
        const bDate = new Date(b.metadata.dueDate!).getTime()
        return aDate - bDate
      })
  }, [calendarEntries])

  // Overdue todos
  const overdueTodos = useMemo(() => {
    const now = Date.now()
    return todoEntries.filter(entry => {
      if (entry.metadata.completed) return false
      const dueDate = entry.metadata.dueDate
      if (!dueDate) return false
      return new Date(dueDate).getTime() < now
    })
  }, [todoEntries])

  return {
    // Filtered entries based on options
    entries: filteredEntries,
    
    // Specific entry types
    calendarEntries,
    todoEntries,
    textEntries,
    
    // Statistics
    stats,
    
    // Special filtered lists
    recentEntries,
    upcomingEvents,
    overdueTodos,
    
    // Helper functions
    getEntriesByType: (entryType: Entry['type']) => 
      entries.filter(entry => entry.type === entryType),
    
    getEntriesByDate: (date: Date) => 
      entries.filter(entry => {
        const entryDate = new Date(entry.createdAt)
        return entryDate.toDateString() === date.toDateString()
      }),
    
    searchEntries: (query: string) => 
      entries.filter(entry => 
        entry.title.toLowerCase().includes(query.toLowerCase()) ||
        entry.content.toLowerCase().includes(query.toLowerCase()) ||
        entry.metadata.tags?.some(tag => 
          tag.toLowerCase().includes(query.toLowerCase())
        )
      )
  }
}

export default useEntries
