import { useEffect, useCallback, useState } from 'react'
import { useSearchNavigation } from '@/services/searchNavigation'

export interface HighlightOptions {
  duration?: number // Duration of highlight effect in ms
  scrollOffset?: number // Offset from top when scrolling to item
  highlightClass?: string // Custom CSS class for highlighting
  pulseCount?: number // Number of pulse animations
}

const DEFAULT_OPTIONS: Required<HighlightOptions> = {
  duration: 3000,
  scrollOffset: 100,
  highlightClass: 'search-highlight',
  pulseCount: 3
}

export function useItemHighlight(pageType: string, options: HighlightOptions = {}) {
  const { getAndClearSearchContext } = useSearchNavigation()
  const [highlightedItemId, setHighlightedItemId] = useState<string | null>(null)
  const [isHighlighting, setIsHighlighting] = useState(false)
  
  const finalOptions = { ...DEFAULT_OPTIONS, ...options }

  // Function to highlight a specific item
  const highlightItem = useCallback((itemId: string, customOptions?: HighlightOptions) => {
    const opts = { ...finalOptions, ...customOptions }
    
    // Find the element to highlight
    const selector = getElementSelector(pageType, itemId)
    const element = document.querySelector(selector)
    
    if (!element) {
      console.warn(`Item with ID ${itemId} not found for highlighting`)
      showFallbackNotification(itemId, pageType)
      return false
    }

    // Set highlighting state
    setHighlightedItemId(itemId)
    setIsHighlighting(true)

    // Add highlight class
    element.classList.add(opts.highlightClass)
    element.classList.add('search-highlight-active')

    // Scroll to element with page type context
    scrollToElement(element, opts.scrollOffset, pageType)

    // Add pulse animation
    addPulseAnimation(element, opts.pulseCount)

    // Remove highlight after duration
    setTimeout(() => {
      element.classList.remove(opts.highlightClass)
      element.classList.remove('search-highlight-active')
      setHighlightedItemId(null)
      setIsHighlighting(false)
    }, opts.duration)

    return true
  }, [pageType, finalOptions])

  // Check for search context on page load
  useEffect(() => {
    const searchContext = getAndClearSearchContext()

    if (searchContext && searchContext.itemId && searchContext.action === 'highlight') {
      console.log('🎯 Search context found:', searchContext)

      // Longer delay to ensure DOM is fully rendered and React has updated
      const attemptHighlight = (attempts = 0) => {
        const maxAttempts = 10
        const delay = 200 + (attempts * 100) // Increasing delay

        setTimeout(() => {
          const success = highlightItem(searchContext.itemId)

          if (!success && attempts < maxAttempts) {
            console.log(`🔄 Highlight attempt ${attempts + 1} failed, retrying...`)
            attemptHighlight(attempts + 1)
          } else if (success) {
            console.log('✅ Highlight successful!')
          } else {
            console.warn('❌ All highlight attempts failed')
          }
        }, delay)
      }

      attemptHighlight()
    }
  }, [getAndClearSearchContext, highlightItem])

  return {
    highlightItem,
    highlightedItemId,
    isHighlighting,
    clearHighlight: () => {
      if (highlightedItemId) {
        const selector = getElementSelector(pageType, highlightedItemId)
        const element = document.querySelector(selector)
        if (element) {
          element.classList.remove(finalOptions.highlightClass)
          element.classList.remove('search-highlight-active')
        }
        setHighlightedItemId(null)
        setIsHighlighting(false)
      }
    }
  }
}

// Helper function to get the correct selector for each page type
function getElementSelector(pageType: string, itemId: string): string {
  switch (pageType) {
    case 'notes':
      return `[data-note-id="${itemId}"]`
    case 'todos':
      return `[data-todo-id="${itemId}"]`
    case 'calendar':
      return `[data-event-id="${itemId}"]`
    case 'dashboard':
      return `[data-item-id="${itemId}"], [data-note-id="${itemId}"], [data-todo-id="${itemId}"], [data-event-id="${itemId}"]`
    default:
      return `[data-item-id="${itemId}"]`
  }
}

// Helper function to scroll to element smoothly
function scrollToElement(element: Element, offset: number, pageType?: string) {
  console.log('📍 Scrolling to element:', element, 'Page type:', pageType)

  // Special handling for calendar week view
  if (pageType === 'calendar') {
    scrollToCalendarEvent(element, offset)
    return
  }

  // Use scrollIntoView for better browser compatibility and reliability
  element.scrollIntoView({
    behavior: 'smooth',
    block: 'center',
    inline: 'nearest'
  })

  // Additional fallback using window.scrollTo
  setTimeout(() => {
    const elementRect = element.getBoundingClientRect()
    const absoluteElementTop = elementRect.top + window.pageYOffset
    const targetScrollTop = Math.max(0, absoluteElementTop - offset)

    if (Math.abs(window.pageYOffset - targetScrollTop) > 50) {
      window.scrollTo({
        top: targetScrollTop,
        behavior: 'smooth'
      })
    }
  }, 100)
}

// Special function for calendar event positioning
function scrollToCalendarEvent(element: Element, offset: number) {
  console.log('📅 Calendar-specific scrolling for element:', element)

  // Get the calendar grid container
  const calendarGrid = document.getElementById('calendar-grid')
  if (!calendarGrid) {
    console.warn('❌ Calendar grid not found')
    return
  }

  // Get element position relative to calendar grid
  const elementRect = element.getBoundingClientRect()
  const gridRect = calendarGrid.getBoundingClientRect()

  // Calculate vertical scroll position (time-based)
  const relativeTop = elementRect.top - gridRect.top + calendarGrid.scrollTop
  const targetVerticalScroll = Math.max(0, relativeTop - offset)

  // Calculate horizontal positioning for week view
  const eventDate = getEventDateFromElement(element)
  if (eventDate) {
    adjustWeekViewForDate(eventDate)
  }

  // Scroll vertically to the event time
  setTimeout(() => {
    calendarGrid.scrollTo({
      top: targetVerticalScroll,
      behavior: 'smooth'
    })
    console.log('📍 Calendar vertical scroll to:', targetVerticalScroll)
  }, 200) // Delay to allow horizontal adjustment first
}

// Helper function to extract event date from DOM element
function getEventDateFromElement(element: Element): Date | null {
  try {
    // Find the parent day column
    let dayColumn = element.closest('[data-day-date]') as HTMLElement
    if (!dayColumn) {
      // Alternative: traverse up to find day column by structure
      let parent = element.parentElement
      while (parent && !parent.hasAttribute('data-day-date')) {
        parent = parent.parentElement
      }
      dayColumn = parent as HTMLElement
    }

    if (dayColumn) {
      const dateStr = dayColumn.getAttribute('data-day-date')
      if (dateStr) {
        return new Date(dateStr)
      }
    }

    console.warn('❌ Could not extract date from calendar event element')
    return null
  } catch (error) {
    console.error('❌ Error extracting event date:', error)
    return null
  }
}

// Helper function to adjust week view to show specific date
function adjustWeekViewForDate(targetDate: Date) {
  console.log('📅 Adjusting week view for date:', targetDate)

  // This function will be called from the calendar page context
  // We'll dispatch a custom event that the calendar page can listen to
  const adjustEvent = new CustomEvent('adjustWeekView', {
    detail: { targetDate }
  })
  window.dispatchEvent(adjustEvent)
}

// Helper function to add pulse animation
function addPulseAnimation(element: Element, pulseCount: number) {
  console.log('💫 Adding pulse animation to element:', element)

  // Add pulse class with a slight delay to ensure highlighting is visible first
  setTimeout(() => {
    element.classList.add('search-pulse')
    console.log('✨ Pulse animation started')

    // Remove pulse class after animation completes
    setTimeout(() => {
      element.classList.remove('search-pulse')
      console.log('🏁 Pulse animation completed')
    }, pulseCount * 800) // Each pulse is ~800ms for better visibility
  }, 300) // 300ms delay to let highlight settle
}

// Helper function to show fallback notification
function showFallbackNotification(itemId: string, pageType: string) {
  // Create a temporary toast notification
  const toast = document.createElement('div')
  toast.className = 'search-fallback-toast'
  toast.innerHTML = `
    <div class="flex items-center gap-2 bg-bg-primary border border-border-primary rounded-lg px-4 py-3 shadow-lg">
      <svg class="w-5 h-5 text-accent-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
      <span class="text-sm text-text-primary">Found item on ${pageType} page</span>
    </div>
  `
  
  // Style the toast
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
  `
  
  document.body.appendChild(toast)
  
  // Remove toast after 3 seconds
  setTimeout(() => {
    toast.style.animation = 'slideOutRight 0.3s ease-in'
    setTimeout(() => {
      document.body.removeChild(toast)
    }, 300)
  }, 3000)
}

export default useItemHighlight
