import { useMemo } from 'react'
import useEntries from './useEntries'
import { isToday, isThisWeek, getDaysOverdue, isTodayOrFuture } from '@/lib/utils'
import type { Entry } from '@/store/appStore'

export interface DashboardStats {
  totalNotes: number
  totalTodos: number
  pendingTodos: number
  completedTodos: number
  totalEvents: number
  todaysEvents: number
  overdueItems: number
  todaysNotes: number
}

export interface PriorityItem {
  id: string
  title: string
  type: 'todo' | 'calendar'
  daysOverdue?: number
  time?: string
  priority?: 'low' | 'medium' | 'high'
  completed?: boolean
}

export interface WeeklyProgress {
  completedTasks: number
  totalTasks: number
  notesCreated: number
  eventsAttended: number
  totalEvents: number
  completionRate: number
}

export function useDashboardData() {
  const { 
    entries, 
    stats, 
    textEntries, 
    todoEntries, 
    calendarEntries,
    overdueTodos,
    recentEntries 
  } = useEntries()

  // Dashboard statistics
  const dashboardStats: DashboardStats = useMemo(() => {
    const todaysEntries = entries.filter(entry => isToday(entry.createdAt))
    const todaysEvents = calendarEntries.filter(entry =>
      entry.metadata.dueDate && isToday(entry.metadata.dueDate)
    )

    // Filter events to only include today onwards (exclude past events)
    const currentAndFutureEvents = calendarEntries.filter(entry =>
      entry.metadata.dueDate && isTodayOrFuture(entry.metadata.dueDate)
    )

    return {
      totalNotes: stats.notes,
      totalTodos: stats.todos,
      pendingTodos: stats.pending,
      completedTodos: stats.completed,
      totalEvents: currentAndFutureEvents.length, // Only current and future events
      todaysEvents: todaysEvents.length,
      overdueItems: overdueTodos.length,
      todaysNotes: todaysEntries.filter(entry => entry.type === 'text').length
    }
  }, [entries, stats, calendarEntries, overdueTodos])

  // Priority items (overdue todos + today's events)
  const priorityItems: PriorityItem[] = useMemo(() => {
    const items: PriorityItem[] = []

    // Add overdue todos (limit to 3 most urgent)
    const urgentOverdue = overdueTodos
      .slice(0, 3)
      .map(todo => ({
        id: todo.id,
        title: todo.title,
        type: 'todo' as const,
        daysOverdue: todo.metadata.dueDate ? getDaysOverdue(todo.metadata.dueDate) : 0,
        priority: todo.metadata.priority || 'medium',
        completed: todo.metadata.completed || false
      }))

    items.push(...urgentOverdue)

    // Add today's calendar events
    const todaysEvents = calendarEntries
      .filter(entry => entry.metadata.dueDate && isToday(entry.metadata.dueDate))
      .slice(0, 3)
      .map(event => ({
        id: event.id,
        title: event.title,
        type: 'calendar' as const,
        time: event.metadata.time || 'All day'
      }))

    items.push(...todaysEvents)

    // Sort by urgency: overdue first, then by time for events
    return items.sort((a, b) => {
      if (a.type === 'todo' && b.type === 'calendar') return -1
      if (a.type === 'calendar' && b.type === 'todo') return 1
      if (a.daysOverdue && b.daysOverdue) return b.daysOverdue - a.daysOverdue
      return 0
    })
  }, [overdueTodos, calendarEntries])

  // Recent notes (last 4 entries)
  const recentNotes = useMemo(() => {
    return textEntries.slice(0, 4)
  }, [textEntries])

  // Weekly progress calculation
  const weeklyProgress: WeeklyProgress = useMemo(() => {
    const thisWeekEntries = entries.filter(entry => isThisWeek(entry.createdAt))
    const thisWeekTodos = todoEntries.filter(entry => isThisWeek(entry.createdAt))
    const thisWeekEvents = calendarEntries.filter(entry => 
      entry.metadata.dueDate && isThisWeek(entry.metadata.dueDate)
    )

    const completedThisWeek = thisWeekTodos.filter(todo => todo.metadata.completed).length
    const totalThisWeekTodos = thisWeekTodos.length
    const notesThisWeek = thisWeekEntries.filter(entry => entry.type === 'text').length

    const completionRate = totalThisWeekTodos > 0 
      ? Math.round((completedThisWeek / totalThisWeekTodos) * 100)
      : 0

    return {
      completedTasks: completedThisWeek,
      totalTasks: totalThisWeekTodos,
      notesCreated: notesThisWeek,
      eventsAttended: completedThisWeek, // Simplified - using completed todos as proxy
      totalEvents: thisWeekEvents.length,
      completionRate
    }
  }, [entries, todoEntries, calendarEntries])

  // Today's summary for quick overview
  const todaysSummary = useMemo(() => {
    const todaysEntries = entries.filter(entry => isToday(entry.createdAt))
    const todaysTodos = todoEntries.filter(entry => 
      entry.metadata.dueDate && isToday(entry.metadata.dueDate)
    )
    const todaysEvents = calendarEntries.filter(entry => 
      entry.metadata.dueDate && isToday(entry.metadata.dueDate)
    )

    return {
      totalItems: todaysEntries.length,
      todos: todaysTodos.length,
      events: todaysEvents.length,
      notes: todaysEntries.filter(entry => entry.type === 'text').length
    }
  }, [entries, todoEntries, calendarEntries])

  return {
    dashboardStats,
    priorityItems,
    recentNotes,
    weeklyProgress,
    todaysSummary,
    // Pass through some useful data for components
    hasOverdueItems: overdueTodos.length > 0,
    hasTodaysEvents: dashboardStats.todaysEvents > 0,
    hasRecentNotes: recentNotes.length > 0
  }
}

export default useDashboardData
