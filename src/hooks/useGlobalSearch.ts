import { useMemo, useState, useCallback } from 'react'
import { useAppStore } from '@/store/appStore'
import type { Entry } from '@/store/appStore'

export interface SearchResult {
  id: string
  type: 'notes' | 'todos' | 'calendar' | 'inbox'
  title: string
  content: string
  snippet: string
  highlightedTitle: string
  highlightedSnippet: string
  metadata: {
    priority?: 'low' | 'medium' | 'high'
    dueDate?: string
    time?: string
    tags?: string[]
    category?: string
    completed?: boolean
  }
  createdAt: number
  updatedAt: number
  relevanceScore: number
}

export interface GroupedSearchResults {
  notes: SearchResult[]
  todos: SearchResult[]
  calendar: SearchResult[]
  inbox: SearchResult[]
  total: number
}

export interface SearchState {
  query: string
  isSearching: boolean
  results: GroupedSearchResults
  hasSearched: boolean
  recentSearches: string[]
}

const SEARCH_DEBOUNCE_MS = 300
const MAX_SNIPPET_LENGTH = 100
const MAX_RESULTS_PER_TYPE = 5
const MAX_RECENT_SEARCHES = 10

export function useGlobalSearch() {
  const { entries } = useAppStore()
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    isSearching: false,
    results: {
      notes: [],
      todos: [],
      calendar: [],
      inbox: [],
      total: 0
    },
    hasSearched: false,
    recentSearches: []
  })

  // Highlight search terms in text
  const highlightText = useCallback((text: string, query: string): string => {
    if (!query.trim()) return text
    
    const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
    return text.replace(regex, '<mark class="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">$1</mark>')
  }, [])

  // Create snippet from content
  const createSnippet = useCallback((content: string, query: string): string => {
    if (!content) return ''
    
    const queryLower = query.toLowerCase()
    const contentLower = content.toLowerCase()
    const queryIndex = contentLower.indexOf(queryLower)
    
    if (queryIndex === -1) {
      // If query not found in content, return first part
      return content.length > MAX_SNIPPET_LENGTH 
        ? content.substring(0, MAX_SNIPPET_LENGTH) + '...'
        : content
    }
    
    // Center the snippet around the query
    const start = Math.max(0, queryIndex - 30)
    const end = Math.min(content.length, start + MAX_SNIPPET_LENGTH)
    
    let snippet = content.substring(start, end)
    if (start > 0) snippet = '...' + snippet
    if (end < content.length) snippet = snippet + '...'
    
    return snippet
  }, [])

  // Calculate relevance score
  const calculateRelevanceScore = useCallback((entry: Entry, query: string): number => {
    const queryLower = query.toLowerCase()
    let score = 0
    
    // Title match (highest weight)
    const titleLower = entry.title.toLowerCase()
    if (titleLower === queryLower) score += 100
    else if (titleLower.startsWith(queryLower)) score += 80
    else if (titleLower.includes(queryLower)) score += 60
    
    // Content match
    const contentLower = entry.content.toLowerCase()
    if (contentLower.includes(queryLower)) score += 40
    
    // Tags match
    if (entry.metadata.tags) {
      for (const tag of entry.metadata.tags) {
        if (tag.toLowerCase().includes(queryLower)) score += 30
      }
    }
    
    // Category match
    if (entry.metadata.category?.toLowerCase().includes(queryLower)) score += 20
    
    // Recency bonus (newer items get slight boost)
    const daysSinceUpdate = (Date.now() - entry.updatedAt) / (1000 * 60 * 60 * 24)
    if (daysSinceUpdate < 1) score += 10
    else if (daysSinceUpdate < 7) score += 5
    
    // Priority bonus for todos and calendar
    if (entry.metadata.priority === 'high') score += 5
    else if (entry.metadata.priority === 'medium') score += 2
    
    return score
  }, [])

  // Convert entry to search result
  const entryToSearchResult = useCallback((entry: Entry, query: string): SearchResult => {
    const snippet = createSnippet(entry.content, query)
    const relevanceScore = calculateRelevanceScore(entry, query)
    
    // Map entry type to search result type
    let type: SearchResult['type']
    switch (entry.type) {
      case 'text':
        type = 'notes'
        break
      case 'todo':
        type = 'todos'
        break
      case 'calendar':
        type = 'calendar'
        break
      case 'inbox':
      default:
        type = 'inbox'
        break
    }
    
    return {
      id: entry.id,
      type,
      title: entry.title,
      content: entry.content,
      snippet,
      highlightedTitle: highlightText(entry.title, query),
      highlightedSnippet: highlightText(snippet, query),
      metadata: entry.metadata,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt,
      relevanceScore
    }
  }, [createSnippet, calculateRelevanceScore, highlightText])

  // Perform search
  const performSearch = useCallback((query: string): GroupedSearchResults => {
    if (!query.trim()) {
      return {
        notes: [],
        todos: [],
        calendar: [],
        inbox: [],
        total: 0
      }
    }

    const queryLower = query.toLowerCase()
    
    // Filter and convert entries to search results
    const allResults = entries
      .filter(entry => {
        return (
          entry.title.toLowerCase().includes(queryLower) ||
          entry.content.toLowerCase().includes(queryLower) ||
          entry.metadata.tags?.some(tag => tag.toLowerCase().includes(queryLower)) ||
          entry.metadata.category?.toLowerCase().includes(queryLower)
        )
      })
      .map(entry => entryToSearchResult(entry, query))
      .sort((a, b) => b.relevanceScore - a.relevanceScore)

    // Group results by type
    const groupedResults: GroupedSearchResults = {
      notes: allResults.filter(r => r.type === 'notes').slice(0, MAX_RESULTS_PER_TYPE),
      todos: allResults.filter(r => r.type === 'todos').slice(0, MAX_RESULTS_PER_TYPE),
      calendar: allResults.filter(r => r.type === 'calendar').slice(0, MAX_RESULTS_PER_TYPE),
      inbox: allResults.filter(r => r.type === 'inbox').slice(0, MAX_RESULTS_PER_TYPE),
      total: allResults.length
    }

    return groupedResults
  }, [entries, entryToSearchResult])

  // Debounced search
  const debouncedSearch = useMemo(() => {
    let timeoutId: NodeJS.Timeout
    
    return (query: string) => {
      clearTimeout(timeoutId)
      
      if (!query.trim()) {
        setSearchState(prev => ({
          ...prev,
          query,
          results: {
            notes: [],
            todos: [],
            calendar: [],
            inbox: [],
            total: 0
          },
          hasSearched: false,
          isSearching: false
        }))
        return
      }

      setSearchState(prev => ({ ...prev, query, isSearching: true }))
      
      timeoutId = setTimeout(() => {
        const results = performSearch(query)
        setSearchState(prev => ({
          ...prev,
          results,
          hasSearched: true,
          isSearching: false
        }))
      }, SEARCH_DEBOUNCE_MS)
    }
  }, [performSearch])

  // Add to recent searches
  const addToRecentSearches = useCallback((query: string) => {
    if (!query.trim()) return
    
    setSearchState(prev => {
      const filtered = prev.recentSearches.filter(s => s !== query)
      const updated = [query, ...filtered].slice(0, MAX_RECENT_SEARCHES)
      return { ...prev, recentSearches: updated }
    })
  }, [])

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchState(prev => ({
      ...prev,
      query: '',
      results: {
        notes: [],
        todos: [],
        calendar: [],
        inbox: [],
        total: 0
      },
      hasSearched: false,
      isSearching: false
    }))
  }, [])

  return {
    searchState,
    search: debouncedSearch,
    clearSearch,
    addToRecentSearches,
    performSearch
  }
}

export default useGlobalSearch
