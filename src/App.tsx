import { Routes, Route, Navigate } from 'react-router-dom'
import AuthGuard from './components/Auth/AuthGuard'
import Layout from './components/Layout/Layout'
import MockAuthPage from './pages/MockAuthPage'
import DashboardPage from './pages/DashboardPage'
import NotesPage from './pages/NotesPage'
import CalendarPage from './pages/CalendarPage'
import TodosPage from './pages/TodosPage'

function App() {
  return (
    <AuthGuard>
      <Layout>
        <Routes>
          {/* Dashboard as the new default landing page */}
          <Route path="/" element={<DashboardPage />} />
          {/* Redirect old inbox routes to Dashboard for backward compatibility */}
          <Route path="/inbox" element={<Navigate to="/" replace />} />
          <Route path="/dashboard" element={<DashboardPage />} />
          <Route path="/notes" element={<NotesPage />} />
          <Route path="/calendar" element={<CalendarPage />} />
          <Route path="/todos" element={<TodosPage />} />
          <Route path="/test-auth" element={<MockAuthPage />} />
        </Routes>
      </Layout>
    </AuthGuard>
  )
}

export default App
