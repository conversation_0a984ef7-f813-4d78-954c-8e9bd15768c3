// ===========================================
// GEMINI API DIAGNOSTIC TOOL
// ===========================================

import { env } from '@/config/environment'

export interface DiagnosticResult {
  success: boolean
  error?: string
  details?: any
  suggestions?: string[]
}

// Test basic API connectivity
export async function testGeminiConnectivity(): Promise<DiagnosticResult> {
  const apiKey = env.ai.geminiApiKey
  
  if (!apiKey) {
    return {
      success: false,
      error: 'No API key configured',
      suggestions: ['Set VITE_GEMINI_API_KEY in your .env.local file']
    }
  }

  // Test with a very simple request
  const endpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent'
  
  const payload = {
    contents: [
      {
        parts: [
          {
            text: 'Hello'
          }
        ]
      }
    ]
  }

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-goog-api-key': apiKey
      },
      body: JSON.stringify(payload)
    })

    const data = await response.json()

    if (!response.ok) {
      return {
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        details: data,
        suggestions: [
          'Check if your API key is valid',
          'Verify API key has Gemini API access enabled',
          'Check if you have quota remaining',
          'Try using a different model (gemini-pro instead of gemini-1.5-flash)'
        ]
      }
    }

    return {
      success: true,
      details: {
        model: 'gemini-1.5-flash',
        responseLength: JSON.stringify(data).length,
        hasContent: !!data.candidates?.[0]?.content
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      suggestions: [
        'Check your internet connection',
        'Verify the API endpoint is accessible',
        'Check for CORS issues'
      ]
    }
  }
}

// Test with different models
export async function testDifferentModels(): Promise<Record<string, DiagnosticResult>> {
  const models = [
    'gemini-1.5-flash',
    'gemini-1.5-pro',
    'gemini-pro'
  ]

  const results: Record<string, DiagnosticResult> = {}
  const apiKey = env.ai.geminiApiKey

  for (const model of models) {
    const endpoint = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`
    
    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-goog-api-key': apiKey
        },
        body: JSON.stringify({
          contents: [{ parts: [{ text: 'Test' }] }]
        })
      })

      const data = await response.json()

      results[model] = {
        success: response.ok,
        error: response.ok ? undefined : `${response.status}: ${data.error?.message || response.statusText}`,
        details: data
      }
    } catch (error) {
      results[model] = {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  return results
}

// Run comprehensive diagnostic
export async function runGeminiDiagnostic(): Promise<{
  connectivity: DiagnosticResult
  models: Record<string, DiagnosticResult>
  recommendations: string[]
}> {
  console.log('🔍 Running Gemini API Diagnostic...')
  
  const connectivity = await testGeminiConnectivity()
  const models = await testDifferentModels()
  
  const recommendations: string[] = []
  
  if (!connectivity.success) {
    recommendations.push('Fix basic connectivity issues first')
    if (connectivity.suggestions) {
      recommendations.push(...connectivity.suggestions)
    }
  }
  
  const workingModels = Object.entries(models).filter(([_, result]) => result.success)
  if (workingModels.length > 0) {
    recommendations.push(`Working models found: ${workingModels.map(([model]) => model).join(', ')}`)
  } else {
    recommendations.push('No working models found - check API key permissions')
  }
  
  return {
    connectivity,
    models,
    recommendations
  }
}

// Development helper - run diagnostic in console
if (env.isDevelopment && env.features.debugLogging) {
  // Make diagnostic available globally for manual testing
  (window as any).testGemini = runGeminiDiagnostic
  console.log('🔧 Gemini diagnostic available: window.testGemini()')
}
