import { useAuth } from '@clerk/clerk-react'
import { useAppStore } from '@/store/appStore'
import { DEV_CONFIG } from '@/config/development'
import { env } from '@/config/environment'
import { mockApiService } from './mockApi'
import { dataRouter } from './dataRouter'
import type { Note, Entry, OfflineAction } from '@/store/appStore'

const API_BASE_URL = env.api.baseUrl

class APIError extends Error {
  constructor(public status: number, message: string, public code?: string) {
    super(message)
    this.name = 'APIError'
    this.code = code
  }
}

class APIService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {},
    token?: string
  ): Promise<T> {
    const url = `${API_BASE_URL}/api${endpoint}`

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string> || {})
    }

    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers
      })

      if (!response.ok) {
        let errorMessage = `API Error: ${response.statusText}`
        let errorCode = 'API_ERROR'

        try {
          const errorData = await response.json()
          if (errorData.error) {
            errorMessage = errorData.error
          }
          if (errorData.code) {
            errorCode = errorData.code
          }
        } catch {
          // If we can't parse error response, use default message
        }

        // Handle specific error cases
        if (response.status === 401) {
          errorCode = 'AUTHENTICATION_ERROR'
          errorMessage = 'Authentication failed. Please sign in again.'
        } else if (response.status === 403) {
          errorCode = 'AUTHORIZATION_ERROR'
          errorMessage = 'You do not have permission to perform this action.'
        } else if (response.status === 429) {
          errorCode = 'RATE_LIMIT_EXCEEDED'
          errorMessage = 'Too many requests. Please try again later.'
        }

        throw new APIError(response.status, errorMessage, errorCode)
      }

      const data = await response.json()

      // Handle API response format
      if (data.success === false) {
        throw new APIError(400, data.error || 'API request failed', data.code)
      }

      // Return the data field if it exists, otherwise return the whole response
      return data.data || data
    } catch (error) {
      if (error instanceof APIError) {
        throw error
      }

      // Network error or other issues
      console.error('API request failed:', error)
      throw new APIError(0, 'Network error. Please check your connection.', 'NETWORK_ERROR')
    }
  }

  // Health check
  async healthCheck(): Promise<any> {
    return this.makeRequest('/health', { method: 'GET' })
  }

  // Analyze content using AI
  async analyzeContent(content: string, context: string = 'general', token?: string): Promise<any> {
    return this.makeRequest('/analyze', {
      method: 'POST',
      body: JSON.stringify({ content, context })
    }, token)
  }

  // Get all entries for the authenticated user
  async getEntries(token: string, limit?: number, offset?: number): Promise<any[]> {
    const params = new URLSearchParams()
    if (limit) params.append('limit', limit.toString())
    if (offset) params.append('offset', offset.toString())

    const queryString = params.toString()
    const endpoint = queryString ? `/entries?${queryString}` : '/entries'

    return this.makeRequest<any[]>(endpoint, {
      method: 'GET'
    }, token)
  }

  // Update an existing entry
  async updateEntry(id: string, updates: Partial<{
    title: string
    content: string
    metadata: Record<string, any>
  }>, token: string): Promise<any> {
    return this.makeRequest(`/entries/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates)
    }, token)
  }

  // Delete an entry
  async deleteEntry(id: string, token: string): Promise<void> {
    await this.makeRequest<void>(`/entries/${id}`, {
      method: 'DELETE'
    }, token)
  }

  // Get user profile
  async getUserProfile(token?: string): Promise<any> {
    return this.makeRequest('/user/profile', {
      method: 'GET'
    }, token)
  }
}

export const apiService = new APIService()

// React hook for API operations with authentication and offline support
export function useAPI() {
  // Only use useAuth when not skipping authentication
  const authData = DEV_CONFIG.SKIP_AUTH ? null : useAuth()
  const getToken = authData?.getToken || (() => Promise.resolve(null))

  const {
    isOnline,
    isLoading,
    isRefreshing,
    error,
    addOfflineAction,
    setLoading,
    setRefreshing,
    setError,
    setEntries,
    addEntry: addEntryToStore,
    updateEntry: updateEntryInStore,
    deleteEntry: deleteEntryFromStore,
    setNotes,
    addNote,
    updateNote,
    deleteNote,
    setLastRefreshTime
  } = useAppStore()

  const handleOfflineAction = (action: Omit<OfflineAction, 'id' | 'timestamp'>) => {
    if (!isOnline) {
      addOfflineAction(action)
      return true // Indicate that action was queued
    }
    return false // Indicate that action should be executed immediately
  }

  const getEntries = async () => {
    try {
      setLoading(true)
      setError(null)

      // Use mock API in development or when auth is disabled
      if (DEV_CONFIG.USE_MOCK_API || DEV_CONFIG.SKIP_AUTH) {
        const entries = await mockApiService.getEntries()
        setNotes(entries)
        setLastRefreshTime(Date.now())
        return entries
      }

      const token = await getToken()

      // 使用数据路由获取条目
      const rawEntries = await dataRouter.getEntries(token || undefined)

      // Convert API entries to our Entry format
      const convertedEntries: Entry[] = rawEntries.map((entry: any) => ({
        id: entry.id,
        type: entry.type || 'text',
        title: entry.title || 'Untitled',
        content: entry.content || '',
        metadata: entry.metadata || {},
        createdAt: entry.createdAt || Date.now(),
        updatedAt: entry.updatedAt || Date.now()
      }))

      // Convert entries to notes format for backward compatibility
      const convertedNotes: Note[] = convertedEntries.map((entry: Entry) => ({
        id: entry.id,
        userId: 'current-user',
        createdAt: entry.createdAt,
        updatedAt: entry.updatedAt,
        rawText: entry.content,
        blocks: [{
          id: crypto.randomUUID(),
          type: entry.type,
          content: entry.content,
          metadata: entry.metadata
        }]
      }))

      // Set both entries and notes for backward compatibility
      setEntries(convertedEntries)
      setNotes(convertedNotes)
      setLastRefreshTime(Date.now())

      console.log(`📊 获取到 ${convertedEntries.length} 个条目，数据源: ${dataRouter.getCurrentDataSource()}`)
      return convertedEntries
    } catch (error) {
      const errorMessage = error instanceof APIError
        ? error.message
        : 'Failed to fetch entries'
      setError(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const refreshEntries = async () => {
    try {
      setRefreshing(true)
      setError(null)

      console.log('🔄 Refreshing entries data...')

      // Use mock API in development or when auth is disabled
      if (DEV_CONFIG.USE_MOCK_API || DEV_CONFIG.SKIP_AUTH) {
        const entries = await mockApiService.getEntries()
        setNotes(entries)
        setLastRefreshTime(Date.now())
        console.log('✅ Entries refreshed successfully (mock mode)')
        return entries
      }

      const token = await getToken()

      // 使用数据路由获取条目
      const rawEntries = await dataRouter.getEntries(token || undefined)

      // Convert API entries to our Entry format
      const convertedEntries: Entry[] = rawEntries.map((entry: any) => ({
        id: entry.id,
        type: entry.type || 'text',
        title: entry.title || 'Untitled',
        content: entry.content || '',
        metadata: entry.metadata || {},
        createdAt: entry.createdAt || Date.now(),
        updatedAt: entry.updatedAt || Date.now()
      }))

      // Convert entries to notes format for backward compatibility
      const convertedNotes: Note[] = convertedEntries.map((entry: Entry) => ({
        id: entry.id,
        userId: 'current-user',
        createdAt: entry.createdAt,
        updatedAt: entry.updatedAt,
        rawText: entry.content,
        blocks: [{
          id: crypto.randomUUID(),
          type: entry.type,
          content: entry.content,
          metadata: entry.metadata
        }]
      }))

      // Set both entries and notes for backward compatibility
      setEntries(convertedEntries)
      setNotes(convertedNotes)
      setLastRefreshTime(Date.now())

      console.log(`✅ Entries refreshed successfully: ${convertedEntries.length} entries (数据源: ${dataRouter.getCurrentDataSource()})`)
      return convertedEntries
    } catch (error) {
      const errorMessage = error instanceof APIError
        ? error.message
        : 'Failed to refresh entries'
      setError(errorMessage)
      console.error('❌ Failed to refresh entries:', error)
      throw error
    } finally {
      setRefreshing(false)
    }
  }

  const createEntry = async (rawText: string) => {
    try {
      setLoading(true)
      setError(null)

      // Use mock API in development
      if (DEV_CONFIG.USE_MOCK_API) {
        const entry = await mockApiService.createEntry(rawText)
        addNote(entry)
        return entry
      }

      // Check if offline
      if (handleOfflineAction({
        type: 'CREATE',
        endpoint: '/analyze',
        data: { content: rawText }
      })) {
        // Create optimistic entry for offline
        const optimisticEntry: Note = {
          id: `offline-${Date.now()}`,
          userId: 'current-user',
          createdAt: Date.now(),
          updatedAt: Date.now(),
          rawText,
          blocks: [{
            id: crypto.randomUUID(),
            type: 'text',
            content: rawText
          }]
        }
        addNote(optimisticEntry)
        return optimisticEntry
      }

      const token = await getToken()

      // 使用数据路由创建条目
      const createdNote = await dataRouter.createEntry(rawText, token || undefined)

      // 添加到本地状态
      addNote(createdNote)

      // 如果是远程数据源，刷新条目列表
      if (dataRouter.getCurrentDataSource() === 'remote') {
        console.log('🔄 Refreshing entries after remote content creation...')
        try {
          await refreshEntries()
        } catch (refreshError) {
          console.warn('⚠️ Failed to refresh entries after creation:', refreshError)
          // Don't throw error, as the creation was successful
        }
      }

      console.log(`✨ 条目创建成功，数据源: ${dataRouter.getCurrentDataSource()}`)
      return createdNote
    } catch (error) {
      const errorMessage = error instanceof APIError
        ? error.message
        : 'Failed to create entry'
      setError(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const updateEntry = async (id: string, updates: Partial<{
    title: string
    content: string
    metadata: Record<string, any>
  }>) => {
    try {
      setLoading(true)
      setError(null)

      // Check if offline
      if (handleOfflineAction({
        type: 'UPDATE',
        endpoint: `/entries/${id}`,
        data: updates
      })) {
        // Update optimistically for offline
        updateNote(id, { ...updates, updatedAt: Date.now() })
        return
      }

      const token = await getToken()

      // 使用数据路由更新条目
      const updatedEntry = await dataRouter.updateEntry(id, updates, token || undefined)
      updateNote(id, updatedEntry)

      console.log(`🔄 条目更新成功，数据源: ${dataRouter.getCurrentDataSource()}`)
      return updatedEntry
    } catch (error) {
      const errorMessage = error instanceof APIError
        ? error.message
        : 'Failed to update entry'
      setError(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const deleteEntry = async (id: string) => {
    try {
      setLoading(true)
      setError(null)

      // Check if offline
      if (handleOfflineAction({
        type: 'DELETE',
        endpoint: `/entries/${id}`,
        data: {}
      })) {
        // Delete optimistically for offline
        deleteNote(id)
        return
      }

      const token = await getToken()

      // 使用数据路由删除条目
      await dataRouter.deleteEntry(id, token || undefined)
      deleteNote(id)

      console.log(`🗑️ 条目删除成功，数据源: ${dataRouter.getCurrentDataSource()}`)
    } catch (error) {
      const errorMessage = error instanceof APIError
        ? error.message
        : 'Failed to delete entry'
      setError(errorMessage)
      throw error
    } finally {
      setLoading(false)
    }
  }

  return {
    getEntries,
    refreshEntries,
    createEntry,
    updateEntry,
    deleteEntry,
    isLoading,
    isRefreshing,
    error,
    isOnline
  }
}
