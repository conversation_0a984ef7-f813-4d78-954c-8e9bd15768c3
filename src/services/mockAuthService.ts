// ===========================================
// MOCK AUTHENTICATION SERVICE FOR TESTING
// ===========================================

import { env } from '../config/environment'

export interface MockJWTPayload {
  sub: string // User ID
  email: string
  firstName?: string
  lastName?: string
  iss: string // Issuer
  aud: string // Audience
  exp: number // Expiration
  iat: number // Issued at
  nbf: number // Not before
}

export interface MockAuthToken {
  token: string
  payload: MockJWTPayload
  isValid: boolean
  expiresAt: Date
}

export class MockAuthService {
  private static instance: MockAuthService
  private mockTokens: Map<string, MockAuthToken> = new Map()

  private constructor() {}

  static getInstance(): MockAuthService {
    if (!MockAuthService.instance) {
      MockAuthService.instance = new MockAuthService()
    }
    return MockAuthService.instance
  }

  // Generate a mock JWT token for testing
  generateMockToken(userId: string = 'test-user-001', options: Partial<MockJWTPayload> = {}): MockAuthToken {
    const now = Math.floor(Date.now() / 1000)
    const exp = now + (60 * 60) // 1 hour from now
    
    const payload: MockJWTPayload = {
      sub: userId,
      email: options.email || `${userId}@test.com`,
      firstName: options.firstName || 'Test',
      lastName: options.lastName || 'User',
      iss: 'https://clerk.infinite-eagle-21.clerk.accounts.dev',
      aud: 'infinite-eagle-21',
      exp,
      iat: now,
      nbf: now,
      ...options
    }

    // Create a mock JWT token (not cryptographically signed, just base64 encoded)
    const header = {
      alg: 'RS256',
      typ: 'JWT',
      kid: 'ins_2abc123def456ghi789jkl'
    }

    const headerB64 = btoa(JSON.stringify(header))
    const payloadB64 = btoa(JSON.stringify(payload))
    const signature = 'mock-signature-for-testing'

    const token = `${headerB64}.${payloadB64}.${signature}`

    const mockToken: MockAuthToken = {
      token,
      payload,
      isValid: true,
      expiresAt: new Date(exp * 1000)
    }

    this.mockTokens.set(token, mockToken)
    return mockToken
  }

  // Generate an expired token for testing
  generateExpiredToken(userId: string = 'test-user-expired'): MockAuthToken {
    const now = Math.floor(Date.now() / 1000)
    const exp = now - 3600 // 1 hour ago (expired)
    
    return this.generateMockToken(userId, { exp })
  }

  // Generate an invalid token for testing
  generateInvalidToken(): MockAuthToken {
    const mockToken = this.generateMockToken('invalid-user')
    mockToken.isValid = false
    mockToken.token = 'invalid.token.signature'
    return mockToken
  }

  // Get all mock tokens
  getAllMockTokens(): MockAuthToken[] {
    return Array.from(this.mockTokens.values())
  }

  // Clear all mock tokens
  clearMockTokens(): void {
    this.mockTokens.clear()
  }

  // Validate a mock token
  validateMockToken(token: string): MockAuthToken | null {
    return this.mockTokens.get(token) || null
  }
}

// Pre-generated test tokens for different scenarios
export const TEST_TOKENS = {
  VALID_USER: 'valid-user-token',
  EXPIRED_USER: 'expired-user-token',
  INVALID_TOKEN: 'invalid-token',
  ADMIN_USER: 'admin-user-token'
}

// Initialize mock tokens if testing is enabled
if (env.features.devMode && env.isDevelopment) {
  const mockAuth = MockAuthService.getInstance()
  
  // Generate test tokens
  const validToken = mockAuth.generateMockToken('user-001', {
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User'
  })
  
  const expiredToken = mockAuth.generateExpiredToken('user-expired')
  const invalidToken = mockAuth.generateInvalidToken()
  
  const adminToken = mockAuth.generateMockToken('admin-001', {
    email: '<EMAIL>',
    firstName: 'Admin',
    lastName: 'User'
  })

  // Store tokens with predictable keys for testing
  mockAuth.mockTokens.set(TEST_TOKENS.VALID_USER, validToken)
  mockAuth.mockTokens.set(TEST_TOKENS.EXPIRED_USER, expiredToken)
  mockAuth.mockTokens.set(TEST_TOKENS.INVALID_TOKEN, invalidToken)
  mockAuth.mockTokens.set(TEST_TOKENS.ADMIN_USER, adminToken)

  console.log('🧪 Mock authentication tokens generated:', {
    validToken: validToken.token.substring(0, 50) + '...',
    expiredToken: expiredToken.token.substring(0, 50) + '...',
    invalidToken: invalidToken.token.substring(0, 50) + '...',
    adminToken: adminToken.token.substring(0, 50) + '...'
  })
}

// Helper function to get a test token
export const getTestToken = (type: keyof typeof TEST_TOKENS): string => {
  const mockAuth = MockAuthService.getInstance()
  const token = mockAuth.mockTokens.get(TEST_TOKENS[type])
  return token?.token || ''
}

// Helper function to set authorization header
export const createAuthHeaders = (tokenType: keyof typeof TEST_TOKENS = 'VALID_USER'): HeadersInit => {
  const token = getTestToken(tokenType)
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
}

// Helper function to create cookies for browser testing
export const createAuthCookies = (tokenType: keyof typeof TEST_TOKENS = 'VALID_USER'): string => {
  const token = getTestToken(tokenType)
  return `__session=${token}; Path=/; HttpOnly; Secure; SameSite=Lax`
}
