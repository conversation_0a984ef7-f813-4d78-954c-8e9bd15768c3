// ===========================================
// 智能数据路由层 - 根据用户订阅状态选择数据源
// ===========================================

import { apiService } from './api'
import { localSQLiteService } from './localSQLite'
import type { Note, Entry } from '@/store/appStore'

export type DataSource = 'local' | 'remote'
export type SubscriptionStatus = 'free' | 'premium' | 'enterprise'

export interface UserProfile {
  userId: string
  subscriptionStatus: SubscriptionStatus
  subscriptionTier: string
  totalEntries: number
  totalItems: number
  createdAt: number
  updatedAt: number
  lastActiveAt?: number
}

export class DataRouter {
  private userProfile: UserProfile | null = null
  private currentDataSource: DataSource = 'remote' // 默认使用远程

  constructor() {
    this.initializeDataSource()
  }

  /**
   * 初始化数据源选择
   */
  private async initializeDataSource() {
    try {
      // 获取用户配置信息
      await this.fetchUserProfile()
      
      // 根据订阅状态决定数据源
      this.currentDataSource = this.determineDataSource()
      
      console.log(`🔀 数据路由初始化完成，使用数据源: ${this.currentDataSource}`)
    } catch (error) {
      console.error('❌ 数据路由初始化失败:', error)
      // 失败时默认使用远程数据源
      this.currentDataSource = 'remote'
    }
  }

  /**
   * 获取用户配置信息
   */
  private async fetchUserProfile(): Promise<void> {
    try {
      // 暂时使用默认配置，后续可以从API获取
      // TODO: 实现从远程API获取用户配置
      // const profile = await apiService.getUserProfile(token)

      this.userProfile = {
        userId: 'current-user',
        subscriptionStatus: 'free', // 默认免费用户，可以通过环境变量或API配置
        subscriptionTier: 'basic',
        totalEntries: 0,
        totalItems: 0,
        createdAt: Date.now(),
        updatedAt: Date.now()
      }
      console.log('📋 用户配置初始化完成:', this.userProfile)
    } catch (error) {
      console.warn('⚠️ 获取用户配置失败，使用默认配置:', error)
      // 失败时使用默认配置（免费用户）
      this.userProfile = {
        userId: 'current-user',
        subscriptionStatus: 'free',
        subscriptionTier: 'basic',
        totalEntries: 0,
        totalItems: 0,
        createdAt: Date.now(),
        updatedAt: Date.now()
      }
    }
  }

  /**
   * 根据订阅状态确定数据源
   */
  private determineDataSource(): DataSource {
    if (!this.userProfile) {
      return 'remote' // 默认远程
    }

    // 免费用户使用本地SQLite，付费用户使用远程D1
    return this.userProfile.subscriptionStatus === 'free' ? 'local' : 'remote'
  }

  /**
   * 获取当前数据源
   */
  public getCurrentDataSource(): DataSource {
    return this.currentDataSource
  }

  /**
   * 获取用户配置
   */
  public getUserProfile(): UserProfile | null {
    return this.userProfile
  }

  /**
   * 强制刷新用户配置和数据源选择
   */
  public async refreshDataSource(): Promise<void> {
    await this.initializeDataSource()
  }

  /**
   * 统一的数据操作接口 - 获取条目
   */
  public async getEntries(token?: string): Promise<Entry[]> {
    console.log(`📊 使用${this.currentDataSource === 'local' ? '本地' : '远程'}数据源获取条目`)
    
    if (this.currentDataSource === 'local') {
      return await localSQLiteService.getEntries()
    } else {
      if (!token) {
        throw new Error('远程数据源需要认证token')
      }
      return await apiService.getEntries(token)
    }
  }

  /**
   * 统一的数据操作接口 - 创建条目
   */
  public async createEntry(rawText: string, token?: string): Promise<Note> {
    console.log(`✨ 使用${this.currentDataSource === 'local' ? '本地' : '远程'}数据源创建条目`)
    
    if (this.currentDataSource === 'local') {
      // 本地创建需要先调用AI分析，然后存储到本地SQLite
      return await localSQLiteService.createEntry(rawText, token)
    } else {
      if (!token) {
        throw new Error('远程数据源需要认证token')
      }
      // 远程创建直接调用现有API
      return await this.createEntryRemote(rawText, token)
    }
  }

  /**
   * 远程创建条目的辅助方法
   */
  private async createEntryRemote(rawText: string, token: string): Promise<Note> {
    // 提取上下文
    let context = 'general'
    let cleanText = rawText

    if (rawText.startsWith('[CALENDAR]')) {
      context = 'calendar'
      cleanText = rawText.replace('[CALENDAR]', '').trim()
    } else if (rawText.startsWith('[TODOS]')) {
      context = 'todos'
      cleanText = rawText.replace('[TODOS]', '').trim()
    } else if (rawText.startsWith('[NOTES]')) {
      context = 'notes'
      cleanText = rawText.replace('[NOTES]', '').trim()
    }

    const analysisResult = await apiService.analyzeContent(cleanText, context, token)

    // 转换为Note格式
    if (analysisResult.blocks && analysisResult.blocks.length > 0) {
      const block = analysisResult.blocks[0]
      return {
        id: block.id || Date.now().toString(),
        userId: 'current-user',
        createdAt: Date.now(),
        updatedAt: Date.now(),
        rawText: cleanText,
        blocks: [{
          id: block.id || crypto.randomUUID(),
          type: block.type || 'text',
          content: block.content,
          metadata: block.metadata
        }],
        tags: block.metadata?.tags,
        category: block.metadata?.category
      }
    }

    // 回退方案
    return {
      id: Date.now().toString(),
      userId: 'current-user',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      rawText: cleanText,
      blocks: [{
        id: crypto.randomUUID(),
        type: 'text',
        content: cleanText
      }]
    }
  }

  /**
   * 统一的数据操作接口 - 更新条目
   */
  public async updateEntry(id: string, updates: Partial<{
    title: string
    content: string
    metadata: Record<string, any>
  }>, token?: string): Promise<any> {
    console.log(`🔄 使用${this.currentDataSource === 'local' ? '本地' : '远程'}数据源更新条目`)
    
    if (this.currentDataSource === 'local') {
      return await localSQLiteService.updateEntry(id, updates)
    } else {
      if (!token) {
        throw new Error('远程数据源需要认证token')
      }
      return await apiService.updateEntry(id, updates, token)
    }
  }

  /**
   * 统一的数据操作接口 - 删除条目
   */
  public async deleteEntry(id: string, token?: string): Promise<void> {
    console.log(`🗑️ 使用${this.currentDataSource === 'local' ? '本地' : '远程'}数据源删除条目`)
    
    if (this.currentDataSource === 'local') {
      await localSQLiteService.deleteEntry(id)
    } else {
      if (!token) {
        throw new Error('远程数据源需要认证token')
      }
      await apiService.deleteEntry(id, token)
    }
  }

  /**
   * AI分析接口 - 始终使用远程服务
   */
  public async analyzeContent(content: string, context: string = 'general', token?: string): Promise<any> {
    console.log('🤖 使用远程AI服务分析内容')
    return await apiService.analyzeContent(content, context, token)
  }
}

// 导出单例实例
export const dataRouter = new DataRouter()
