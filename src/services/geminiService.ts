// ===========================================
// GOOGLE GEMINI AI SERVICE
// ===========================================

import { promptTemplates, isGeminiConfigured, getGeminiEndpoint, getGeminiHeaders, createGeminiPayload } from '@/config/gemini'
import { env } from '@/config/environment'
import type { Block } from '@/store/appStore'

export interface AIAnalysisResult {
  type: 'text' | 'todo' | 'calendar'
  title: string
  content: string
  metadata: {
    priority?: 'low' | 'medium' | 'high'
    dueDate?: string
    time?: string
    tags?: string[]
    category?: string
  }
}

class GeminiService {
  private async makeRequest(prompt: string): Promise<any> {
    if (!isGeminiConfigured()) {
      throw new Error('Gemini AI is not properly configured')
    }

    const endpoint = getGeminiEndpoint()
    const headers = getGeminiHeaders()
    const payload = createGeminiPayload(prompt)

    if (env.features.debugLogging) {
      console.log('🤖 Gemini API Request:', {
        endpoint,
        headers: { ...headers, 'X-goog-api-key': headers['X-goog-api-key'].substring(0, 20) + '...' },
        payloadSize: JSON.stringify(payload).length
      })
    }

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Gemini API Error Response:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        })
        throw new Error(`Gemini API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()

      if (env.features.debugLogging) {
        console.log('🤖 Gemini API Response:', {
          candidates: data.candidates?.length || 0,
          hasContent: !!data.candidates?.[0]?.content
        })
      }

      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        console.error('Invalid Gemini API response structure:', data)
        throw new Error('Invalid response from Gemini API')
      }

      const textResponse = data.candidates[0].content.parts[0].text

      if (env.features.debugLogging) {
        console.log('🤖 Gemini Raw Response:', textResponse.substring(0, 200) + '...')
      }

      try {
        // Clean the response in case it has markdown formatting
        let cleanedResponse = textResponse.trim()

        // Remove markdown code blocks if present
        if (cleanedResponse.startsWith('```json')) {
          cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
        } else if (cleanedResponse.startsWith('```')) {
          cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
        }

        return JSON.parse(cleanedResponse)
      } catch (parseError) {
        console.error('Failed to parse Gemini response as JSON:', {
          originalResponse: textResponse,
          parseError: parseError instanceof Error ? parseError.message : String(parseError)
        })
        throw new Error('Invalid JSON response from Gemini API')
      }
    } catch (error) {
      console.error('Gemini API request failed:', error)
      throw error
    }
  }

  async analyzeContent(content: string, context?: string): Promise<AIAnalysisResult> {
    if (!env.ai.enableAI || !isGeminiConfigured()) {
      throw new Error('AI processing is not enabled or configured')
    }

    try {
      let prompt: string
      
      if (context && context !== 'general') {
        // Use contextual analysis for specific contexts
        prompt = promptTemplates.contextualAnalysis
          .replace('{context}', context)
          .replace('{content}', content)
      } else {
        // Use general content analysis
        prompt = promptTemplates.contentAnalysis
          .replace('{content}', content)
      }

      const result = await this.makeRequest(prompt)
      
      // Validate the result structure
      if (!result || typeof result !== 'object') {
        throw new Error('Invalid analysis result structure')
      }

      // Ensure required fields exist
      const analysisResult: AIAnalysisResult = {
        type: result.type || 'text',
        title: result.title || content.substring(0, 50) + (content.length > 50 ? '...' : ''),
        content: result.content || content,
        metadata: result.metadata || {}
      }

      if (env.features.debugLogging) {
        console.log('🤖 Gemini Analysis Result:', analysisResult)
      }

      return analysisResult
    } catch (error) {
      console.error('Content analysis failed:', error)
      
      // Fallback to simple analysis
      return this.fallbackAnalysis(content, context)
    }
  }

  private fallbackAnalysis(content: string, context?: string): AIAnalysisResult {
    const lowerContent = content.toLowerCase()
    let type: 'text' | 'todo' | 'calendar' = 'text'

    // Simple keyword-based analysis as fallback
    if (context === 'calendar' || lowerContent.includes('meeting') || lowerContent.includes('appointment') || lowerContent.includes('event')) {
      type = 'calendar'
    } else if (context === 'todos' || lowerContent.includes('todo') || lowerContent.includes('task') || lowerContent.includes('need to')) {
      type = 'todo'
    } else if (context === 'notes') {
      type = 'text'
    }

    return {
      type,
      title: content.substring(0, 50) + (content.length > 50 ? '...' : ''),
      content: content,
      metadata: {
        category: 'other',
        tags: []
      }
    }
  }

  // Helper method to convert AI result to Block format
  createBlockFromAnalysis(analysis: AIAnalysisResult): Block {
    return {
      id: `${Date.now()}-1`,
      type: analysis.type,
      content: analysis.content,
      metadata: analysis.metadata
    }
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    if (!isGeminiConfigured()) {
      return false
    }

    try {
      await this.analyzeContent('Test message for health check')
      return true
    } catch (error) {
      console.error('Gemini health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const geminiService = new GeminiService()

// Development helper
if (env.isDevelopment && env.features.debugLogging) {
  console.log('🤖 Gemini Service initialized:', {
    configured: isGeminiConfigured(),
    enabled: env.ai.enableAI
  })

  // Load diagnostic tool
  import('../utils/geminiDiagnostic').then(({ runGeminiDiagnostic }) => {
    console.log('🔧 Gemini diagnostic loaded. Run window.testGemini() to diagnose API issues.')
  })
}
