// Mock API for development testing
import type { Note, Block } from '@/store/appStore'

// Mock data
let mockNotes: Note[] = [
  {
    id: '1',
    userId: 'mock-user',
    createdAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now() - 2 * 24 * 60 * 60 * 1000,
    rawText: 'Team meeting tomorrow at 2 PM to discuss new feature planning',
    blocks: [
      {
        id: '1-1',
        type: 'calendar',
        content: 'Team meeting tomorrow at 2 PM to discuss new feature planning',
        metadata: { date: '2024-01-16', time: '14:00' }
      }
    ]
  },
  {
    id: '2',
    userId: 'mock-user',
    createdAt: Date.now() - 3 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now() - 3 * 24 * 60 * 60 * 1000,
    rawText: 'Need to finish the user interface design by Friday deadline',
    blocks: [
      {
        id: '2-1',
        type: 'todo',
        content: 'Need to finish the user interface design by Friday deadline',
        metadata: { completed: false, priority: 'high' }
      }
    ]
  },
  {
    id: '3',
    userId: 'mock-user',
    createdAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
    updatedAt: Date.now() - 5 * 24 * 60 * 60 * 1000,
    rawText: 'Some ideas about how to better integrate AI into user experience design patterns. This could revolutionize how users interact with intelligent systems.',
    blocks: [
      {
        id: '3-1',
        type: 'text',
        content: 'Some ideas about how to better integrate AI into user experience design patterns. This could revolutionize how users interact with intelligent systems.',
        metadata: { tags: ['ai', 'design', 'ux'] }
      }
    ]
  }
]

// Mock API functions
export const mockApiService = {
  async getEntries(): Promise<Note[]> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500))
    return [...mockNotes]
  },

  async createEntry(rawText: string): Promise<Note> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Enhanced AI simulation - determine type based on context prefix or keywords
    let type: Block['type'] = 'text'
    let cleanedText = rawText

    // Check for context prefix
    if (rawText.startsWith('[CALENDAR]')) {
      type = 'calendar'
      cleanedText = rawText.replace('[CALENDAR]', '').trim()
    } else if (rawText.startsWith('[TODOS]')) {
      type = 'todo'
      cleanedText = rawText.replace('[TODOS]', '').trim()
    } else if (rawText.startsWith('[NOTES]')) {
      type = 'text'
      cleanedText = rawText.replace('[NOTES]', '').trim()
    } else {
      // Fallback to keyword detection
      if (rawText.toLowerCase().includes('meeting') || rawText.toLowerCase().includes('appointment') || rawText.toLowerCase().includes('event')) {
        type = 'calendar'
      } else if (rawText.toLowerCase().includes('todo') || rawText.toLowerCase().includes('task') || rawText.toLowerCase().includes('need to')) {
        type = 'todo'
      }
    }

    const newNote: Note = {
      id: Date.now().toString(),
      userId: 'mock-user',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      rawText: cleanedText,
      blocks: [
        {
          id: `${Date.now()}-1`,
          type,
          content: cleanedText,
          metadata: {}
        }
      ]
    }

    mockNotes.unshift(newNote)
    return newNote
  },

  async updateEntry(id: string, blocks: Block[]): Promise<Note> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const noteIndex = mockNotes.findIndex(note => note.id === id)
    if (noteIndex === -1) {
      throw new Error('Note not found')
    }

    mockNotes[noteIndex] = {
      ...mockNotes[noteIndex],
      blocks,
      updatedAt: Date.now()
    }

    return mockNotes[noteIndex]
  },

  async deleteEntry(id: string): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const noteIndex = mockNotes.findIndex(note => note.id === id)
    if (noteIndex === -1) {
      throw new Error('Note not found')
    }

    mockNotes.splice(noteIndex, 1)
  }
}
