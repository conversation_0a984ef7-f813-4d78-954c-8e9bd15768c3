import type { SearchResult } from '@/hooks/useGlobalSearch'

export interface NavigationContext {
  itemId: string
  type: SearchResult['type']
  action?: 'view' | 'edit' | 'highlight' | 'search'
  date?: string
  metadata?: {
    priority?: string
    completed?: boolean
    category?: string
    tags?: string[]
    filterAdjustment?: string // Filter to switch to for visibility
  }
}

export class SearchNavigationService {
  private static instance: SearchNavigationService
  private navigationCallbacks: Map<string, (context: NavigationContext) => void> = new Map()

  static getInstance(): SearchNavigationService {
    if (!SearchNavigationService.instance) {
      SearchNavigationService.instance = new SearchNavigationService()
    }
    return SearchNavigationService.instance
  }

  // Register navigation callback for a specific page
  registerNavigationCallback(pageType: string, callback: (context: NavigationContext) => void) {
    this.navigationCallbacks.set(pageType, callback)
  }

  // Unregister navigation callback
  unregisterNavigationCallback(pageType: string) {
    this.navigationCallbacks.delete(pageType)
  }

  // Navigate to a search result
  navigateToResult(result: SearchResult): string {
    const context: NavigationContext = {
      itemId: result.id,
      type: result.type,
      action: 'highlight',
      metadata: {
        priority: result.metadata.priority,
        completed: result.metadata.completed,
        category: result.metadata.category,
        tags: result.metadata.tags
      }
    }

    // Add specific context based on result type
    switch (result.type) {
      case 'notes':
        context.action = 'highlight'
        break
      case 'todos':
        context.action = 'highlight'
        // Determine filter adjustment for todos
        if (result.metadata.completed) {
          context.metadata!.filterAdjustment = 'completed'
        } else if (result.metadata.priority === 'high') {
          context.metadata!.filterAdjustment = 'priority'
        } else {
          context.metadata!.filterAdjustment = 'all'
        }
        break
      case 'calendar':
        context.action = 'highlight'
        if (result.metadata.dueDate) {
          context.date = result.metadata.dueDate
        }
        break
      case 'inbox':
        context.action = 'highlight'
        // Determine filter adjustment for inbox
        if (result.type === 'notes') {
          context.metadata!.filterAdjustment = 'notes'
        } else if (result.type === 'todos') {
          context.metadata!.filterAdjustment = 'todos'
        } else if (result.type === 'calendar') {
          context.metadata!.filterAdjustment = 'calendar'
        } else {
          context.metadata!.filterAdjustment = 'all'
        }
        break
    }

    // Store context for the destination page
    this.setSearchContext(context)

    // Call the appropriate navigation callback
    const callback = this.navigationCallbacks.get(result.type)
    if (callback) {
      callback(context)
    }

    // Return the route path for navigation
    return this.getRoutePath(result.type)
  }

  // Get the route path for a content type
  private getRoutePath(type: SearchResult['type']): string {
    switch (type) {
      case 'notes':
        return '/notes'
      case 'todos':
        return '/todos'
      case 'calendar':
        return '/calendar'
      case 'inbox':
        return '/inbox'
      default:
        return '/inbox'
    }
  }

  // Store search context in sessionStorage for cross-page navigation
  setSearchContext(context: NavigationContext) {
    try {
      sessionStorage.setItem('searchContext', JSON.stringify(context))
    } catch (error) {
      console.warn('Failed to store search context:', error)
    }
  }

  // Retrieve and clear search context from sessionStorage
  getAndClearSearchContext(): NavigationContext | null {
    try {
      const stored = sessionStorage.getItem('searchContext')
      if (stored) {
        sessionStorage.removeItem('searchContext')
        return JSON.parse(stored)
      }
    } catch (error) {
      console.warn('Failed to retrieve search context:', error)
    }
    return null
  }

  // Check if there's a pending search context
  hasPendingSearchContext(): boolean {
    try {
      return sessionStorage.getItem('searchContext') !== null
    } catch (error) {
      return false
    }
  }
}

// Export singleton instance
export const searchNavigationService = SearchNavigationService.getInstance()

// Hook for using search navigation in React components
export function useSearchNavigation() {
  const registerCallback = (pageType: string, callback: (context: NavigationContext) => void) => {
    searchNavigationService.registerNavigationCallback(pageType, callback)
    
    // Return cleanup function
    return () => {
      searchNavigationService.unregisterNavigationCallback(pageType)
    }
  }

  const navigateToResult = (result: SearchResult) => {
    return searchNavigationService.navigateToResult(result)
  }

  const setSearchContext = (context: NavigationContext) => {
    searchNavigationService.setSearchContext(context)
  }

  const getAndClearSearchContext = () => {
    return searchNavigationService.getAndClearSearchContext()
  }

  const hasPendingSearchContext = () => {
    return searchNavigationService.hasPendingSearchContext()
  }

  return {
    registerCallback,
    navigateToResult,
    setSearchContext,
    getAndClearSearchContext,
    hasPendingSearchContext
  }
}
