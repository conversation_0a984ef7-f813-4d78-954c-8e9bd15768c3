// ===========================================
// 本地SQLite服务 - 浏览器端数据库操作
// ===========================================

import initSqlJs, { Database, SqlJsStatic } from 'sql.js'
import { apiService } from './api'
import type { Note, Entry } from '@/store/appStore'

export interface LocalUserDataEntry {
  entryId: string
  userId: string
  sessionId: string
  type: 'todo' | 'calendar' | 'text'
  title: string
  content: string
  metadata?: Record<string, any>
  rawText?: string
  createdAt: number
  updatedAt: number
}

export class LocalSQLiteService {
  private sql: SqlJsStatic | null = null
  private db: Database | null = null
  private isInitialized = false

  constructor() {
    this.initialize()
  }

  /**
   * 初始化SQLite数据库
   */
  private async initialize(): Promise<void> {
    try {
      console.log('🔧 初始化本地SQLite数据库...')
      
      // 加载sql.js库
      this.sql = await initSqlJs({
        locateFile: (file) => `https://sql.js.org/dist/${file}`
      })

      // 尝试从IndexedDB加载现有数据库
      const savedDb = await this.loadFromIndexedDB()
      
      if (savedDb) {
        this.db = new this.sql.Database(savedDb)
        console.log('📂 从IndexedDB加载现有数据库')
      } else {
        // 创建新数据库
        this.db = new this.sql.Database()
        await this.createTables()
        console.log('🆕 创建新的本地数据库')
      }

      this.isInitialized = true
      console.log('✅ 本地SQLite数据库初始化完成')
    } catch (error) {
      console.error('❌ SQLite初始化失败:', error)
      throw error
    }
  }

  /**
   * 创建数据库表结构
   */
  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('数据库未初始化')

    // 创建用户数据表
    this.db.run(`
      CREATE TABLE IF NOT EXISTS user_data (
        entryId TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        sessionId TEXT,
        type TEXT NOT NULL CHECK (type IN ('todo', 'calendar', 'text')),
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        metadata TEXT,
        rawText TEXT,
        createdAt INTEGER NOT NULL,
        updatedAt INTEGER NOT NULL
      )
    `)

    // 创建索引
    this.db.run('CREATE INDEX IF NOT EXISTS idx_user_data_userId ON user_data(userId)')
    this.db.run('CREATE INDEX IF NOT EXISTS idx_user_data_sessionId ON user_data(sessionId)')
    this.db.run('CREATE INDEX IF NOT EXISTS idx_user_data_type ON user_data(type)')
    this.db.run('CREATE INDEX IF NOT EXISTS idx_user_data_createdAt ON user_data(createdAt DESC)')

    // 保存到IndexedDB
    await this.saveToIndexedDB()
  }

  /**
   * 确保数据库已初始化
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize()
    }
  }

  /**
   * 从IndexedDB加载数据库
   */
  private async loadFromIndexedDB(): Promise<Uint8Array | null> {
    return new Promise((resolve) => {
      const request = indexedDB.open('SynapseLocalDB', 1)
      
      request.onerror = () => resolve(null)
      
      request.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        const transaction = db.transaction(['database'], 'readonly')
        const store = transaction.objectStore('database')
        const getRequest = store.get('main')
        
        getRequest.onsuccess = () => {
          resolve(getRequest.result ? getRequest.result.data : null)
        }
        
        getRequest.onerror = () => resolve(null)
      }
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        db.createObjectStore('database')
      }
    })
  }

  /**
   * 保存数据库到IndexedDB
   */
  private async saveToIndexedDB(): Promise<void> {
    if (!this.db) return

    const data = this.db.export()
    
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('SynapseLocalDB', 1)
      
      request.onerror = () => reject(request.error)
      
      request.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        const transaction = db.transaction(['database'], 'readwrite')
        const store = transaction.objectStore('database')
        
        store.put({ data }, 'main')
        
        transaction.oncomplete = () => resolve()
        transaction.onerror = () => reject(transaction.error)
      }
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        db.createObjectStore('database')
      }
    })
  }

  /**
   * 获取所有条目
   */
  public async getEntries(): Promise<Entry[]> {
    await this.ensureInitialized()
    if (!this.db) throw new Error('数据库未初始化')

    const stmt = this.db.prepare(`
      SELECT * FROM user_data
      WHERE userId = ?
      ORDER BY createdAt DESC
      LIMIT 50
    `)
    
    const results: LocalUserDataEntry[] = []
    stmt.bind(['current-user'])
    
    while (stmt.step()) {
      const row = stmt.getAsObject() as any
      results.push({
        ...row,
        metadata: row.metadata ? JSON.parse(row.metadata) : undefined
      })
    }
    
    stmt.free()

    // 转换为Entry格式
    return results.map(entry => ({
      id: entry.entryId,
      type: entry.type,
      title: entry.title,
      content: entry.content,
      metadata: entry.metadata || {},
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt
    }))
  }

  /**
   * 创建新条目（需要先调用AI分析）
   */
  public async createEntry(rawText: string, token?: string): Promise<Note> {
    await this.ensureInitialized()
    if (!this.db) throw new Error('数据库未初始化')

    try {
      // 1. 调用远程AI分析服务
      let context = 'general'
      let cleanText = rawText

      if (rawText.startsWith('[CALENDAR]')) {
        context = 'calendar'
        cleanText = rawText.replace('[CALENDAR]', '').trim()
      } else if (rawText.startsWith('[TODOS]')) {
        context = 'todos'
        cleanText = rawText.replace('[TODOS]', '').trim()
      } else if (rawText.startsWith('[NOTES]')) {
        context = 'notes'
        cleanText = rawText.replace('[NOTES]', '').trim()
      }

      const analysisResult = await apiService.analyzeContent(cleanText, context, token)

      // 2. 将分析结果存储到本地数据库
      const sessionId = crypto.randomUUID()
      const now = Date.now()
      const entries: LocalUserDataEntry[] = []

      if (analysisResult.blocks && analysisResult.blocks.length > 0) {
        for (let i = 0; i < analysisResult.blocks.length; i++) {
          const block = analysisResult.blocks[i]
          const entryId = `${sessionId}-${i + 1}`
          
          const entry: LocalUserDataEntry = {
            entryId,
            userId: 'current-user',
            sessionId,
            type: block.type || 'text',
            title: block.content?.split('\n')[0]?.substring(0, 100) || 'Untitled',
            content: block.content || '',
            metadata: block.metadata,
            rawText: cleanText,
            createdAt: now,
            updatedAt: now
          }

          // 插入到数据库
          this.db.run(`
            INSERT INTO user_data (entryId, userId, sessionId, type, title, content, metadata, rawText, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            entry.entryId,
            entry.userId,
            entry.sessionId,
            entry.type,
            entry.title,
            entry.content,
            entry.metadata ? JSON.stringify(entry.metadata) : null,
            entry.rawText,
            entry.createdAt,
            entry.updatedAt
          ])

          entries.push(entry)
        }
      } else {
        // 回退：创建简单文本条目
        const entryId = `${sessionId}-1`
        const entry: LocalUserDataEntry = {
          entryId,
          userId: 'current-user',
          sessionId,
          type: 'text',
          title: cleanText.substring(0, 50) || 'Untitled',
          content: cleanText,
          rawText: cleanText,
          createdAt: now,
          updatedAt: now
        }

        this.db.run(`
          INSERT INTO user_data (entryId, userId, sessionId, type, title, content, rawText, createdAt, updatedAt)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          entry.entryId,
          entry.userId,
          entry.sessionId,
          entry.type,
          entry.title,
          entry.content,
          entry.rawText,
          entry.createdAt,
          entry.updatedAt
        ])

        entries.push(entry)
      }

      // 保存到IndexedDB
      await this.saveToIndexedDB()

      // 返回第一个条目作为Note格式
      const firstEntry = entries[0]
      return {
        id: firstEntry.entryId,
        userId: firstEntry.userId,
        createdAt: firstEntry.createdAt,
        updatedAt: firstEntry.updatedAt,
        rawText: firstEntry.rawText,
        blocks: [{
          id: crypto.randomUUID(),
          type: firstEntry.type,
          content: firstEntry.content,
          metadata: firstEntry.metadata
        }],
        tags: firstEntry.metadata?.tags,
        category: firstEntry.metadata?.category
      }
    } catch (error) {
      console.error('本地创建条目失败:', error)
      throw error
    }
  }

  /**
   * 更新条目
   */
  public async updateEntry(id: string, updates: Partial<{
    title: string
    content: string
    metadata: Record<string, any>
  }>): Promise<any> {
    await this.ensureInitialized()
    if (!this.db) throw new Error('数据库未初始化')

    const now = Date.now()
    const updateFields: string[] = []
    const updateValues: any[] = []

    if (updates.title !== undefined) {
      updateFields.push('title = ?')
      updateValues.push(updates.title)
    }

    if (updates.content !== undefined) {
      updateFields.push('content = ?')
      updateValues.push(updates.content)
    }

    if (updates.metadata !== undefined) {
      updateFields.push('metadata = ?')
      updateValues.push(JSON.stringify(updates.metadata))
    }

    if (updateFields.length === 0) {
      return // 没有更新
    }

    updateFields.push('updatedAt = ?')
    updateValues.push(now)
    updateValues.push(id)

    const updateQuery = `
      UPDATE user_data
      SET ${updateFields.join(', ')}
      WHERE entryId = ? AND userId = ?
    `
    updateValues.push('current-user')

    this.db.run(updateQuery, updateValues)
    await this.saveToIndexedDB()

    return { ...updates, updatedAt: now }
  }

  /**
   * 删除条目
   */
  public async deleteEntry(id: string): Promise<void> {
    await this.ensureInitialized()
    if (!this.db) throw new Error('数据库未初始化')

    this.db.run('DELETE FROM user_data WHERE entryId = ? AND userId = ?', [id, 'current-user'])
    await this.saveToIndexedDB()
  }

  /**
   * 获取数据库统计信息
   */
  public async getStats(): Promise<{ totalEntries: number, totalItems: number }> {
    await this.ensureInitialized()
    if (!this.db) throw new Error('数据库未初始化')

    const stmt = this.db.prepare('SELECT COUNT(*) as count FROM user_data WHERE userId = ?')
    stmt.bind(['current-user'])
    
    let totalItems = 0
    if (stmt.step()) {
      totalItems = (stmt.getAsObject() as any).count
    }
    stmt.free()

    // 计算会话数（作为条目数）
    const sessionStmt = this.db.prepare('SELECT COUNT(DISTINCT sessionId) as count FROM user_data WHERE userId = ?')
    sessionStmt.bind(['current-user'])
    
    let totalEntries = 0
    if (sessionStmt.step()) {
      totalEntries = (sessionStmt.getAsObject() as any).count
    }
    sessionStmt.free()

    return { totalEntries, totalItems }
  }
}

// 导出单例实例
export const localSQLiteService = new LocalSQLiteService()
