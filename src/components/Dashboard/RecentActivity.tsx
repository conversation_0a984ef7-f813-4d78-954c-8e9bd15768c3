import React from 'react'
import { useNavigate } from 'react-router-dom'
import { StickyNote, FileText, Hash } from 'lucide-react'
import { cn, formatDate } from '@/lib/utils'
import type { Entry } from '@/store/appStore'

interface RecentActivityProps {
  recentNotes: Entry[]
  hasRecentNotes: boolean
}

interface NoteCardProps {
  note: Entry
  onClick?: () => void
}

const NoteCard: React.FC<NoteCardProps> = ({ note, onClick }) => {
  const navigate = useNavigate()

  const handleClick = () => {
    if (onClick) {
      onClick()
    } else {
      navigate('/notes')
    }
  }

  const getTruncatedContent = (content: string, maxLength: number = 80) => {
    if (content.length <= maxLength) return content
    return content.substring(0, maxLength) + '...'
  }

  const getCategoryColor = (category?: string) => {
    switch (category) {
      case 'work':
        return 'bg-stone-100 text-stone-700 dark:bg-stone-800 dark:text-stone-300'
      case 'personal':
        return 'bg-emerald-50 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-400'
      case 'ideas':
        return 'bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-400'
      default:
        return 'bg-stone-100 text-stone-600 dark:bg-stone-800 dark:text-stone-400'
    }
  }

  return (
    <div
      className={cn(
        'p-2 md:p-2.5 border border-stone-200 dark:border-stone-700 bg-white dark:bg-stone-800',
        'hover:shadow-sm hover:border-stone-300 dark:hover:border-stone-600 transition-all duration-200 cursor-pointer',
        // Mobile: ensure proper sizing for 2-column grid
        'min-h-[80px] md:min-h-0'
      )}
      onClick={handleClick}
    >
      {/* Mobile: Vertical layout for 2-column grid, Desktop: Horizontal layout */}
      <div className="flex flex-col md:flex-row md:items-start gap-1.5 md:gap-2.5">
        {/* Title section - clean typography */}
        <div className="flex-1 min-w-0">
          {/* Note title */}
          <h4 className="text-xs md:text-sm font-normal text-stone-800 dark:text-stone-200 truncate mb-0.5">
            {note.title}
          </h4>
        </div>

        {/* Note content preview - Hidden on mobile, shown on desktop */}
        <p className="hidden md:block text-xs text-stone-500 dark:text-stone-400 line-clamp-2 mb-1.5 md:flex-1">
          {getTruncatedContent(note.content, 60)}
        </p>

        {/* Metadata section */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-1 md:gap-0">
          <span className="text-xs text-stone-400 dark:text-stone-500">
            {formatDate(note.createdAt)}
          </span>

          {/* Tags or category - Simplified for mobile */}
          <div className="flex items-center gap-1">
            {note.metadata.category && (
              <span className={cn(
                'px-1.5 py-0.5 text-xs font-normal uppercase tracking-wider',
                getCategoryColor(note.metadata.category)
              )}>
                {note.metadata.category}
              </span>
            )}

            {/* Tags - Clean typography only */}
            {note.metadata.tags && note.metadata.tags.length > 0 && (
              <span className="text-xs text-stone-500 dark:text-stone-400 font-normal uppercase tracking-wider">
                <span className="md:hidden">{note.metadata.tags.length}</span>
                <span className="hidden md:inline">
                  {note.metadata.tags.slice(0, 1).join(', ')}
                  {note.metadata.tags.length > 1 && ` +${note.metadata.tags.length - 1}`}
                </span>
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

const RecentActivity: React.FC<RecentActivityProps> = ({
  recentNotes,
  hasRecentNotes
}) => {
  const navigate = useNavigate()

  if (!hasRecentNotes) {
    return (
      <div className="bg-bg-secondary border border-border-primary rounded-xl p-6">
        <div className="text-center">
          <div className="w-12 h-12 bg-accent-purple/10 rounded-full flex items-center justify-center mx-auto mb-3">
            <FileText className="w-6 h-6 text-accent-purple" />
          </div>
          <h3 className="text-lg font-semibold text-text-primary mb-2">
            No Recent Notes
          </h3>
          <p className="text-text-secondary text-sm mb-4">
            Start capturing your thoughts and ideas
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/notes')}
          >
            Create Your First Note
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-3 shadow-sm">
      <div className="mb-3">
        <h3 className="text-sm font-normal text-stone-700 dark:text-stone-300 uppercase tracking-wider">
          RECENT NOTES
        </h3>
      </div>

      {/* Mobile: 2-column grid, Desktop: single column */}
      <div className="grid grid-cols-2 md:grid-cols-1 gap-2 md:gap-0 md:space-y-2">
        {recentNotes.map(note => (
          <NoteCard key={note.id} note={note} />
        ))}
      </div>

      {/* View All Button */}

    </div>
  )
}

export default RecentActivity
