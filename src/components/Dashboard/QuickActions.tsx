import React from 'react'
import { StickyNote, CheckSquare, Calendar, Sparkles } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useAppStore } from '@/store/appStore'
import Button from '@/components/UI/Button'
import type { WeeklyProgress } from '@/hooks/useDashboardData'

interface QuickActionsProps {
  weeklyProgress: WeeklyProgress
}

interface ActionButtonProps {
  icon: React.ComponentType<{ className?: string }>
  label: string
  onClick: () => void
  variant?: 'primary' | 'secondary'
}

const ActionButton: React.FC<ActionButtonProps> = ({
  icon: Icon,
  label,
  onClick,
  variant = 'secondary'
}) => {
  return (
    <Button
      variant={variant === 'primary' ? 'primary' : 'secondary'}
      size="sm"
      onClick={onClick}
      className="w-full justify-start gap-1.5 md:gap-2 text-xs md:text-sm py-1.5 md:py-2"
    >
      <Icon className="w-3 h-3 md:w-4 md:h-4" />
      <span className="truncate">{label}</span>
    </Button>
  )
}

interface ProgressBarProps {
  label: string
  current: number
  total: number
  color?: 'stone' | 'emerald' | 'amber'
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  label,
  current,
  total,
  color = 'stone'
}) => {
  const percentage = total > 0 ? (current / total) * 100 : 0

  const colorClasses = {
    stone: 'bg-stone-500 dark:bg-stone-400',
    emerald: 'bg-emerald-700 dark:bg-emerald-600',
    amber: 'bg-amber-700 dark:bg-amber-600'
  }

  return (
    <div className="space-y-1">
      <div className="flex items-center justify-between">
        <span className="text-xs text-stone-500 dark:text-stone-400">{label}:</span>
        <span className="text-xs font-normal text-stone-800 dark:text-stone-200">
          {current}/{total}
        </span>
      </div>
      <div className="w-full bg-stone-200 dark:bg-stone-700 h-1.5">
        <div
          className={cn('h-1.5 rounded-full transition-all duration-300', colorClasses[color])}
          style={{ width: `${Math.min(percentage, 100)}%` }}
        />
      </div>
    </div>
  )
}

const QuickActions: React.FC<QuickActionsProps> = ({ weeklyProgress }) => {
  const { openMagicInputWithContext } = useAppStore()

  const handleAddNote = () => {
    openMagicInputWithContext('notes')
  }

  const handleAddTodo = () => {
    openMagicInputWithContext('todos')
  }

  const handleAddEvent = () => {
    openMagicInputWithContext('calendar')
  }

  const handleMagicInput = () => {
    openMagicInputWithContext('general')
  }

  return (
    <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-3 shadow-sm">
      {/* Quick Actions Section */}
      <div className="mb-3">
        <div className="mb-3">
          <h3 className="text-sm font-normal text-stone-700 dark:text-stone-300 uppercase tracking-wider">
            QUICK ACTIONS
          </h3>
        </div>

        <div className="grid grid-cols-2 gap-2">
          <ActionButton
            icon={StickyNote}
            label="Add Note"
            onClick={handleAddNote}
          />

          <ActionButton
            icon={CheckSquare}
            label="Add Todo"
            onClick={handleAddTodo}
          />

          <ActionButton
            icon={Calendar}
            label="Add Event"
            onClick={handleAddEvent}
          />

          <ActionButton
            icon={Sparkles}
            label="Magic Input"
            onClick={handleMagicInput}
            variant="primary"
          />
        </div>
      </div>

      {/* Weekly Progress Section */}
      <div>
        <div className="mb-3 md:mb-4">
          <h3 className="text-sm font-normal text-stone-700 dark:text-stone-300 uppercase tracking-wider">
            WEEK PROGRESS
          </h3>
        </div>

        <div className="space-y-2 md:space-y-3">
          {/* Tasks Progress */}
          <ProgressBar
            label="Tasks"
            current={weeklyProgress.completedTasks}
            total={weeklyProgress.totalTasks}
            color="emerald"
          />

          {/* Notes Progress */}
          <ProgressBar
            label="Notes"
            current={weeklyProgress.notesCreated}
            total={Math.max(weeklyProgress.notesCreated, 5)} // Show progress against a goal of 5
            color="amber"
          />

          {/* Events Progress */}
          <ProgressBar
            label="Events"
            current={weeklyProgress.eventsAttended}
            total={weeklyProgress.totalEvents}
            color="stone"
          />

          {/* Overall Completion Rate */}
          <div className="pt-1.5 md:pt-2 border-t border-stone-200 dark:border-stone-700">
            <div className="flex items-center justify-between">
              <span className="text-xs font-medium text-stone-800 dark:text-stone-200">
                Completion Rate
              </span>
              <span className={cn(
                'text-sm font-normal',
                weeklyProgress.completionRate >= 80 ? 'text-emerald-700 dark:text-emerald-600' :
                weeklyProgress.completionRate >= 60 ? 'text-amber-700 dark:text-amber-600' :
                'text-stone-600 dark:text-stone-400'
              )}>
                {weeklyProgress.completionRate}%
              </span>
            </div>

            {/* Motivational message - Hidden on mobile to save space */}
            <p className="hidden md:block text-xs text-stone-500 dark:text-stone-400 mt-0.5">
              {weeklyProgress.completionRate >= 80
                ? "Excellent work! Keep it up!"
                : weeklyProgress.completionRate >= 60
                ? "Good progress! You're on track"
                : "Let's focus on completing more tasks"
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default QuickActions
