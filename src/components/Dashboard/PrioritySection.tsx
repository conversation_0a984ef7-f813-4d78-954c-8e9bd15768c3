import React from 'react'
import { useNavigate } from 'react-router-dom'
import { AlertTriangle, Calendar, Clock, Flag, CheckSquare } from 'lucide-react'
import { cn, formatTime } from '@/lib/utils'
import type { PriorityItem } from '@/hooks/useDashboardData'

interface PrioritySectionProps {
  priorityItems: PriorityItem[]
  hasOverdueItems: boolean
  hasTodaysEvents: boolean
}

interface PriorityItemCardProps {
  item: PriorityItem
  onClick?: () => void
}

const PriorityItemCard: React.FC<PriorityItemCardProps> = ({ item, onClick }) => {
  const navigate = useNavigate()

  const handleClick = () => {
    if (onClick) {
      onClick()
    } else {
      // Navigate to appropriate page
      if (item.type === 'todo') {
        navigate('/todos')
      } else if (item.type === 'calendar') {
        navigate('/calendar')
      }
    }
  }



  const getTypeIcon = () => {
    if (item.type === 'todo') {
      return item.daysOverdue && item.daysOverdue > 0
        ? <div className="w-3 h-3 bg-amber-700 dark:bg-amber-600"></div>
        : <div className="w-3 h-3 bg-stone-500 dark:bg-stone-400"></div>
    }
    return <div className="w-3 h-3 bg-emerald-700 dark:bg-emerald-600"></div>
  }

  const getStatusText = () => {
    if (item.type === 'todo' && item.daysOverdue && item.daysOverdue > 0) {
      return `${item.daysOverdue} day${item.daysOverdue > 1 ? 's' : ''} overdue`
    }
    if (item.type === 'calendar' && item.time) {
      return item.time
    }
    return ''
  }

  return (
    <div
      className={cn(
        'p-3 md:p-4 border transition-all duration-200 cursor-pointer shadow-sm',
        'hover:shadow-md hover:border-stone-300 dark:hover:border-stone-600',
        item.type === 'todo' && item.daysOverdue && item.daysOverdue > 0
          ? 'bg-amber-50/50 dark:bg-amber-900/10 border-amber-300/50 dark:border-amber-800/50'
          : 'bg-white dark:bg-stone-800 border-stone-200 dark:border-stone-700'
      )}
      onClick={handleClick}
    >
      <div className="flex items-start gap-2 md:gap-2.5">
        <div className="flex-shrink-0 mt-0.5">
          {getTypeIcon()}
        </div>

        <div className="flex-1 min-w-0">
          <h4 className="text-xs md:text-sm font-normal text-stone-800 dark:text-stone-200 truncate">
            {item.title}
          </h4>

          {getStatusText() && (
            <div className="flex items-center gap-1 mt-0.5">
              <div className="w-2 h-2 bg-stone-400 dark:bg-stone-500"></div>
              <span className={cn(
                'text-xs',
                item.daysOverdue && item.daysOverdue > 0
                  ? 'text-amber-700 dark:text-amber-600 font-medium'
                  : 'text-stone-500 dark:text-stone-400'
              )}>
                {getStatusText()}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

const PrioritySection: React.FC<PrioritySectionProps> = ({
  priorityItems,
  hasOverdueItems,
  hasTodaysEvents
}) => {
  const navigate = useNavigate()

  const overdueItems = priorityItems.filter(item => 
    item.type === 'todo' && item.daysOverdue && item.daysOverdue > 0
  )
  
  const todaysEvents = priorityItems.filter(item => item.type === 'calendar')

  if (!hasOverdueItems && !hasTodaysEvents) {
    return (
      <div className="bg-bg-secondary border border-border-primary rounded-xl p-6">
        <div className="text-center">
          <div className="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-3">
            <CheckSquare className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <h3 className="text-lg font-semibold text-text-primary mb-2">
            All Caught Up!
          </h3>
          <p className="text-text-secondary text-sm">
            No overdue items or urgent events. Great job staying on top of things!
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-3 shadow-sm">
      <div className="mb-3">
        <h3 className="text-sm font-normal text-stone-700 dark:text-stone-300 uppercase tracking-wider">
          NEEDS ATTENTION
        </h3>
      </div>

      {/* Mobile: Side-by-side layout, Desktop: Stacked layout */}
      <div className="space-y-3">
        {/* Mobile: Grid layout for side-by-side, Desktop: Stacked */}
        <div className="grid grid-cols-2 md:grid-cols-1 gap-3">
          {/* Overdue Todos Section */}
          {overdueItems.length > 0 && (
            <div className="md:col-span-1">
              <div className="flex items-center justify-between mb-1.5 md:mb-2">
                <h4 className="text-xs font-normal text-amber-700 dark:text-amber-600 uppercase tracking-wider">
                  OVERDUE {overdueItems.length}
                </h4>
              </div>
              <div className="space-y-1 md:space-y-1.5">
                {overdueItems.slice(0, 2).map(item => (
                  <PriorityItemCard key={item.id} item={item} />
                ))}
              </div>
            </div>
          )}

          {/* Today's Events Section */}
          {todaysEvents.length > 0 && (
            <div className="md:col-span-1">
              <div className="flex items-center justify-between mb-1.5 md:mb-2">
                <h4 className="text-xs font-normal text-emerald-700 dark:text-emerald-600 uppercase tracking-wider">
                  TODAY {todaysEvents.length}
                </h4>
              </div>
              <div className="space-y-1 md:space-y-1.5">
                {todaysEvents.slice(0, 2).map(item => (
                  <PriorityItemCard key={item.id} item={item} />
                ))}
              </div>
            </div>
          )}
        </div>


      </div>
    </div>
  )
}

export default PrioritySection
