import React from 'react'
import { StickyNote, CheckSquare, Calendar, AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { DashboardStats } from '@/hooks/useDashboardData'

interface QuickStatsCardsProps {
  stats: DashboardStats
}

interface StatCardProps {
  icon: React.ComponentType<{ className?: string }>
  mainValue: number
  mainLabel: string
  subValue?: number
  subLabel?: string
  color: 'blue' | 'green' | 'purple' | 'red'
  onClick?: () => void
}

const StatCard: React.FC<StatCardProps> = ({
  mainValue,
  mainLabel,
  subValue,
  subLabel,
  color,
  onClick
}) => {
  // Typography-as-icon system for clear data identification
  const getTypographyIcon = () => {
    // Notes Card (color: purple)
    if (color === 'purple') {
      return {
        text: 'NOTE',
        colorClass: 'text-amber-700 dark:text-amber-600'
      }
    }
    // Todos Card (color: green)
    if (color === 'green') {
      return {
        text: 'TODO',
        colorClass: 'text-stone-600 dark:text-stone-400'
      }
    }
    // Events Card (color: blue)
    if (color === 'blue') {
      return {
        text: 'EVNT',
        colorClass: 'text-emerald-700 dark:text-emerald-600'
      }
    }
    // Overdue Card (color: red)
    if (color === 'red') {
      return {
        text: 'ATTN',
        colorClass: 'text-amber-700 dark:text-amber-600'
      }
    }
    return {
      text: mainLabel.substring(0, 4).toUpperCase(),
      colorClass: 'text-stone-500 dark:text-stone-400'
    }
  }
  const colorClasses = {
    blue: 'text-stone-700 dark:text-stone-200 bg-white dark:bg-stone-800 border-stone-200 dark:border-stone-700',
    green: 'text-stone-700 dark:text-stone-200 bg-white dark:bg-stone-800 border-stone-200 dark:border-stone-700',
    purple: 'text-stone-700 dark:text-stone-200 bg-white dark:bg-stone-800 border-stone-200 dark:border-stone-700',
    red: 'text-stone-700 dark:text-stone-200 bg-white dark:bg-stone-800 border-stone-200 dark:border-stone-700'
  }

  return (
    <div
      className={cn(
        'border transition-all duration-200 shadow-sm',
        colorClasses[color],
        // Scandinavian: Compact but functional for 1x4 layout
        'p-1.5 md:p-2',
        'hover:shadow-md hover:border-stone-300 dark:hover:border-stone-600',
        onClick && 'cursor-pointer',
        // Compact height for 1x4 layout
        'min-h-[70px] md:min-h-[80px]'
      )}
      onClick={onClick}
    >
      {/* Scandinavian design: Typography-as-icon system */}
      <div className="flex flex-col h-full justify-center text-center">
        {/* Typography icon - functional and visual */}
        <div className="mb-1">
          <div className={cn(
            'text-xs font-medium uppercase tracking-widest leading-none',
            getTypographyIcon().colorClass
          )}>
            {getTypographyIcon().text}
          </div>
        </div>

        {/* Main value - prominent but compact */}
        <div className="text-lg md:text-xl font-light text-stone-800 dark:text-stone-100 leading-none mb-0.5">
          {mainValue}
        </div>

        {/* Sub value - compact, always reserve space for alignment */}
        <div className="text-xs text-stone-400 dark:text-stone-500 font-light min-h-[16px] flex items-center justify-center">
          {subValue !== undefined ? (
            <span>{subValue} {subLabel}</span>
          ) : subLabel ? (
            <span>{subLabel}</span>
          ) : (
            <span>&nbsp;</span>
          )}
        </div>
      </div>
    </div>
  )
}

const QuickStatsCards: React.FC<QuickStatsCardsProps> = ({ stats }) => {
  return (
    <div className="grid grid-cols-4 gap-3">
      {/* Notes Card */}
      <StatCard
        icon={StickyNote}
        mainValue={stats.totalNotes}
        mainLabel="total"
        subValue={stats.todaysNotes}
        subLabel="today"
        color="purple"
      />

      {/* Todos Card */}
      <StatCard
        icon={CheckSquare}
        mainValue={stats.pendingTodos}
        mainLabel="pending"
        subValue={stats.completedTodos}
        subLabel="completed"
        color="green"
      />

      {/* Events Card */}
      <StatCard
        icon={Calendar}
        mainValue={stats.totalEvents}
        mainLabel="total"
        subValue={stats.todaysEvents}
        subLabel="today"
        color="blue"
      />

      {/* Overdue Card */}
      <StatCard
        icon={AlertTriangle}
        mainValue={stats.overdueItems}
        mainLabel="items"
        subLabel={stats.overdueItems > 0 ? "attention" : "caught up"}
        color="red"
      />
    </div>
  )
}

export default QuickStatsCards
