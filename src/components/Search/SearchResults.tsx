import React from 'react'
import { Calendar, CheckSquare, FileText, Inbox, Clock, Tag, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import type { SearchResult, GroupedSearchResults } from '@/hooks/useGlobalSearch'

interface SearchResultsProps {
  results: GroupedSearchResults
  isSearching: boolean
  hasSearched: boolean
  query: string
  onResultClick: (result: SearchResult) => void
  onShowMore: (type: SearchResult['type']) => void
}

const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  isSearching,
  hasSearched,
  query,
  onResultClick,
  onShowMore
}) => {
  const getTypeIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'notes':
        return <FileText className="w-4 h-4" />
      case 'todos':
        return <CheckSquare className="w-4 h-4" />
      case 'calendar':
        return <Calendar className="w-4 h-4" />
      case 'inbox':
        return <Inbox className="w-4 h-4" />
    }
  }

  const getTypeColor = (type: SearchResult['type']) => {
    switch (type) {
      case 'notes':
        return 'text-emerald-700 dark:text-emerald-600'
      case 'todos':
        return 'text-emerald-700 dark:text-emerald-600'
      case 'calendar':
        return 'text-amber-700 dark:text-amber-600'
      case 'inbox':
        return 'text-stone-600 dark:text-stone-400'
    }
  }

  const getTypeName = (type: SearchResult['type']) => {
    switch (type) {
      case 'notes':
        return 'Notes'
      case 'todos':
        return 'Todos'
      case 'calendar':
        return 'Calendar'
      case 'inbox':
        return 'Inbox'
    }
  }

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return 'Today'
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays < 7) {
      return `${diffDays} days ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  const ResultItem: React.FC<{ result: SearchResult }> = ({ result }) => (
    <div
      onClick={() => onResultClick(result)}
      className={cn(
        'flex items-start gap-3 p-3 cursor-pointer transition-colors',
        'hover:bg-stone-50 dark:hover:bg-stone-700 border border-transparent hover:border-stone-200 dark:hover:border-stone-600'
      )}
    >
      <div className={cn('flex-shrink-0 mt-0.5', getTypeColor(result.type))}>
        {getTypeIcon(result.type)}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-start justify-between gap-2 mb-1">
          <h4
            className="font-normal text-stone-800 dark:text-stone-200 truncate"
            dangerouslySetInnerHTML={{ __html: result.highlightedTitle }}
          />
          <div className="flex items-center gap-2 flex-shrink-0">
            {result.metadata.priority && (
              <span className={cn(
                'text-xs px-1.5 py-0.5 uppercase tracking-wider',
                result.metadata.priority === 'high' && 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400',
                result.metadata.priority === 'medium' && 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400',
                result.metadata.priority === 'low' && 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400'
              )}>
                {result.metadata.priority}
              </span>
            )}
            {result.metadata.completed && (
              <span className="text-xs px-1.5 py-0.5 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 uppercase tracking-wider">
                Done
              </span>
            )}
          </div>
        </div>

        {result.snippet && (
          <p
            className="text-sm text-stone-500 dark:text-stone-400 line-clamp-2 mb-2"
            dangerouslySetInnerHTML={{ __html: result.highlightedSnippet }}
          />
        )}

        <div className="flex items-center gap-3 text-xs text-stone-400 dark:text-stone-500">
          <span className="flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {formatDate(result.updatedAt)}
          </span>
          
          {result.metadata.tags && result.metadata.tags.length > 0 && (
            <span className="flex items-center gap-1">
              <Tag className="w-3 h-3" />
              {result.metadata.tags.slice(0, 2).join(', ')}
              {result.metadata.tags.length > 2 && ` +${result.metadata.tags.length - 2}`}
            </span>
          )}
          
          {result.metadata.dueDate && (
            <span className="flex items-center gap-1">
              <Calendar className="w-3 h-3" />
              {new Date(result.metadata.dueDate).toLocaleDateString()}
            </span>
          )}
        </div>
      </div>
      
      <ChevronRight className="w-4 h-4 text-text-tertiary flex-shrink-0 mt-1" />
    </div>
  )

  const ResultSection: React.FC<{ 
    type: SearchResult['type']
    results: SearchResult[]
    totalCount: number 
  }> = ({ type, results, totalCount }) => {
    if (results.length === 0) return null

    return (
      <div className="mb-4 last:mb-0">
        <div className="flex items-center justify-between mb-2 px-1">
          <div className="flex items-center gap-2">
            <div className={getTypeColor(type)}>
              {getTypeIcon(type)}
            </div>
            <h3 className="font-normal text-stone-800 dark:text-stone-200 uppercase tracking-wider">
              {getTypeName(type)}
            </h3>
            <span className="text-xs text-stone-400 dark:text-stone-500">
              ({results.length}{totalCount > results.length ? `/${totalCount}` : ''})
            </span>
          </div>

          {totalCount > results.length && (
            <button
              onClick={() => onShowMore(type)}
              className="text-xs text-emerald-700 dark:text-emerald-600 hover:text-emerald-800 dark:hover:text-emerald-700 transition-colors uppercase tracking-wider"
            >
              Show all {totalCount}
            </button>
          )}
        </div>
        
        <div className="space-y-1">
          {results.map(result => (
            <ResultItem key={result.id} result={result} />
          ))}
        </div>
      </div>
    )
  }

  if (isSearching) {
    return (
      <div className="p-4 text-center">
        <div className="animate-spin w-6 h-6 border-2 border-emerald-700 dark:border-emerald-600 border-t-transparent mx-auto mb-2" />
        <p className="text-sm text-stone-500 dark:text-stone-400">Searching...</p>
      </div>
    )
  }

  if (!hasSearched && !query) {
    return (
      <div className="p-4 text-center">
        <div className="w-12 h-12 bg-stone-100 dark:bg-stone-700 flex items-center justify-center mx-auto mb-3">
          <FileText className="w-6 h-6 text-stone-500 dark:text-stone-400" />
        </div>
        <p className="text-sm text-stone-600 dark:text-stone-300 mb-1 uppercase tracking-wider">Search across all your content</p>
        <p className="text-xs text-stone-400 dark:text-stone-500">Notes, todos, calendar events, and more</p>
      </div>
    )
  }

  if (hasSearched && results.total === 0) {
    return (
      <div className="p-4 text-center">
        <div className="w-12 h-12 bg-stone-100 dark:bg-stone-700 flex items-center justify-center mx-auto mb-3">
          <FileText className="w-6 h-6 text-stone-500 dark:text-stone-400" />
        </div>
        <p className="text-sm text-stone-600 dark:text-stone-300 mb-1 uppercase tracking-wider">No results found</p>
        <p className="text-xs text-stone-400 dark:text-stone-500">
          Try different keywords or check your spelling
        </p>
      </div>
    )
  }

  return (
    <div className="p-2">
      <div className="mb-3 px-2">
        <p className="text-xs text-stone-400 dark:text-stone-500 uppercase tracking-wider">
          Found {results.total} result{results.total !== 1 ? 's' : ''} for "{query}"
        </p>
      </div>
      
      <div className="max-h-96 overflow-y-auto">
        <ResultSection 
          type="notes" 
          results={results.notes} 
          totalCount={results.notes.length} 
        />
        <ResultSection 
          type="todos" 
          results={results.todos} 
          totalCount={results.todos.length} 
        />
        <ResultSection 
          type="calendar" 
          results={results.calendar} 
          totalCount={results.calendar.length} 
        />
        <ResultSection 
          type="inbox" 
          results={results.inbox} 
          totalCount={results.inbox.length} 
        />
      </div>
    </div>
  )
}

export default SearchResults
