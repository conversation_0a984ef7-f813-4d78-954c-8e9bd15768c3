// ===========================================
// 数据源测试组件 - 验证混合架构功能
// ===========================================

import React, { useState, useEffect } from 'react'
import { dataRouter } from '@/services/dataRouter'
import { localSQLiteService } from '@/services/localSQLite'
import type { DataSource, UserProfile } from '@/services/dataRouter'

export const DataSourceTest: React.FC = () => {
  const [currentDataSource, setCurrentDataSource] = useState<DataSource>('remote')
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [testResults, setTestResults] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    // 初始化时获取当前数据源和用户配置
    const initializeTest = async () => {
      try {
        await dataRouter.refreshDataSource()
        setCurrentDataSource(dataRouter.getCurrentDataSource())
        setUserProfile(dataRouter.getUserProfile())
      } catch (error) {
        console.error('初始化测试失败:', error)
      }
    }

    initializeTest()
  }, [])

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testLocalSQLite = async () => {
    setIsLoading(true)
    addTestResult('🧪 开始测试本地SQLite...')

    try {
      // 测试本地数据库统计
      const stats = await localSQLiteService.getStats()
      addTestResult(`📊 本地数据库统计: ${stats.totalEntries} 条目, ${stats.totalItems} 项目`)

      // 测试创建条目
      const testEntry = await localSQLiteService.createEntry('测试本地SQLite创建功能')
      addTestResult(`✅ 本地创建成功: ${testEntry.id}`)

      // 测试获取条目
      const entries = await localSQLiteService.getEntries()
      addTestResult(`📋 本地获取条目: ${entries.length} 个`)

      // 测试更新条目
      if (entries.length > 0) {
        const firstEntry = entries[0]
        await localSQLiteService.updateEntry(firstEntry.id, {
          title: '更新后的标题',
          content: '更新后的内容'
        })
        addTestResult(`🔄 本地更新成功: ${firstEntry.id}`)
      }

      addTestResult('✅ 本地SQLite测试完成')
    } catch (error) {
      addTestResult(`❌ 本地SQLite测试失败: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testDataRouter = async () => {
    setIsLoading(true)
    addTestResult('🔀 开始测试数据路由器...')

    try {
      // 测试获取条目
      const entries = await dataRouter.getEntries()
      addTestResult(`📋 路由器获取条目: ${entries.length} 个 (数据源: ${dataRouter.getCurrentDataSource()})`)

      // 测试创建条目
      const testEntry = await dataRouter.createEntry('测试数据路由器创建功能')
      addTestResult(`✅ 路由器创建成功: ${testEntry.id} (数据源: ${dataRouter.getCurrentDataSource()})`)

      // 测试AI分析
      const analysisResult = await dataRouter.analyzeContent('明天下午2点开会讨论项目进展')
      addTestResult(`🤖 AI分析完成: ${analysisResult.blocks?.length || 0} 个块`)

      addTestResult('✅ 数据路由器测试完成')
    } catch (error) {
      addTestResult(`❌ 数据路由器测试失败: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const switchDataSource = async () => {
    setIsLoading(true)
    addTestResult('🔄 刷新数据源配置...')

    try {
      await dataRouter.refreshDataSource()
      const newDataSource = dataRouter.getCurrentDataSource()
      const newProfile = dataRouter.getUserProfile()
      
      setCurrentDataSource(newDataSource)
      setUserProfile(newProfile)
      
      addTestResult(`🔀 数据源已切换到: ${newDataSource}`)
      addTestResult(`👤 用户状态: ${newProfile?.subscriptionStatus}`)
    } catch (error) {
      addTestResult(`❌ 数据源切换失败: ${error}`)
    } finally {
      setIsLoading(false)
    }
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">数据源架构测试</h1>
      
      {/* 当前状态显示 */}
      <div className="bg-gray-100 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">当前状态</h2>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p><strong>数据源:</strong> 
              <span className={`ml-2 px-2 py-1 rounded text-sm ${
                currentDataSource === 'local' 
                  ? 'bg-green-200 text-green-800' 
                  : 'bg-blue-200 text-blue-800'
              }`}>
                {currentDataSource === 'local' ? '本地SQLite' : '远程D1'}
              </span>
            </p>
            <p><strong>用户状态:</strong> {userProfile?.subscriptionStatus || '未知'}</p>
          </div>
          <div>
            <p><strong>用户ID:</strong> {userProfile?.userId || '未知'}</p>
            <p><strong>订阅层级:</strong> {userProfile?.subscriptionTier || '未知'}</p>
          </div>
        </div>
      </div>

      {/* 测试按钮 */}
      <div className="flex flex-wrap gap-4 mb-6">
        <button
          onClick={testLocalSQLite}
          disabled={isLoading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          测试本地SQLite
        </button>
        
        <button
          onClick={testDataRouter}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          测试数据路由器
        </button>
        
        <button
          onClick={switchDataSource}
          disabled={isLoading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
        >
          刷新数据源
        </button>
        
        <button
          onClick={clearResults}
          disabled={isLoading}
          className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
        >
          清空结果
        </button>
      </div>

      {/* 测试结果显示 */}
      <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm">
        <h3 className="text-lg font-semibold mb-2 text-white">测试结果</h3>
        <div className="h-64 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500">点击上方按钮开始测试...</p>
          ) : (
            testResults.map((result, index) => (
              <div key={index} className="mb-1">
                {result}
              </div>
            ))
          )}
          {isLoading && (
            <div className="text-yellow-400">⏳ 测试进行中...</div>
          )}
        </div>
      </div>

      {/* 架构说明 */}
      <div className="mt-6 bg-blue-50 p-4 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">架构说明</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li><strong>免费用户 (subscriptionStatus === 'free')</strong>: 使用本地SQLite数据库</li>
          <li><strong>付费用户 (subscriptionStatus !== 'free')</strong>: 使用远程D1数据库</li>
          <li><strong>AI分析服务</strong>: 始终使用远程Cloudflare Workers</li>
          <li><strong>认证服务</strong>: 始终使用远程Clerk集成</li>
          <li><strong>数据路由器</strong>: 自动根据用户状态选择合适的数据源</li>
        </ul>
      </div>
    </div>
  )
}
