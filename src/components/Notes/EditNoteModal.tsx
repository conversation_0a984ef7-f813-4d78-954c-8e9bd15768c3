import React, { useState, useEffect, useRef } from 'react'
import { X, Save, Tag, Folder } from 'lucide-react'
import { cn } from '@/lib/utils'
import Button from '@/components/UI/Button'

interface Note {
  id: string
  title: string
  preview: string
  tags: string[]
  category: string
  timestamp: Date
  favorited: boolean
}

interface EditNoteModalProps {
  note: Note | null
  isOpen: boolean
  onClose: () => void
  onSave: (updatedNote: {
    title: string
    content: string
    tags: string
    category: string
  }) => Promise<void>
  isLoading?: boolean
}

const EditNoteModal: React.FC<EditNoteModalProps> = ({
  note,
  isOpen,
  onClose,
  onSave,
  isLoading = false
}) => {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    tags: '',
    category: 'general'
  })
  
  const [hasChanges, setHasChanges] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const titleInputRef = useRef<HTMLInputElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Initialize form data when note changes
  useEffect(() => {
    if (note && isOpen) {
      const newFormData = {
        title: note.title,
        content: note.preview, // This should be full content in real implementation
        tags: note.tags.join(', '),
        category: note.category
      }
      setFormData(newFormData)
      setHasChanges(false)

      // Focus title input after modal opens
      setTimeout(() => {
        titleInputRef.current?.focus()
        titleInputRef.current?.select()
      }, 150)
    }
  }, [note, isOpen])

  // Track changes
  useEffect(() => {
    if (!note) return
    
    const hasChanged = 
      formData.title !== note.title ||
      formData.content !== note.preview ||
      formData.tags !== note.tags.join(', ') ||
      formData.category !== note.category
    
    setHasChanges(hasChanged)
  }, [formData, note])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return

      if (e.key === 'Escape') {
        handleClose()
      } else if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
        e.preventDefault()
        handleSave()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, formData])

  const handleClose = () => {
    if (hasChanges) {
      setShowConfirmDialog(true)
      return
    }
    onClose()
  }

  const handleConfirmClose = () => {
    setShowConfirmDialog(false)
    onClose()
  }

  const handleCancelClose = () => {
    setShowConfirmDialog(false)
  }

  const handleSave = async () => {
    if (!formData.title.trim() || isLoading) return
    
    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Failed to save note:', error)
    }
  }

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const categoryOptions = [
    { value: 'general', label: 'General', icon: '📝' },
    { value: 'work', label: 'Work', icon: '💼' },
    { value: 'personal', label: 'Personal', icon: '👤' },
    { value: 'ideas', label: 'Ideas', icon: '💡' },
    { value: 'research', label: 'Research', icon: '🔬' }
  ]

  if (!isOpen || !note) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/40 transition-opacity duration-200"
        onClick={handleClose}
      />

      {/* Modal - Scandinavian Clean */}
      <div
        ref={modalRef}
        className={cn(
          "relative w-full max-w-lg bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 transition-all duration-200",
          "shadow-lg overflow-visible",
          isOpen ? "translate-y-0 opacity-100 scale-100" : "translate-y-4 opacity-0 scale-95"
        )}
      >
        {/* Header - Minimal */}
        <div className="p-4 border-b border-stone-200 dark:border-stone-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-light text-stone-800 dark:text-stone-100 uppercase tracking-wider">
              EDIT NOTE
            </h2>
            <button
              onClick={handleClose}
              className="w-6 h-6 flex items-center justify-center text-stone-400 dark:text-stone-500 hover:text-stone-600 dark:hover:text-stone-300 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Content - Compact */}
        <div className="p-4 space-y-4 overflow-visible">
          {/* Title Field */}
          <div className="space-y-1">
            <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
              TITLE
            </label>
            <input
              ref={titleInputRef}
              type="text"
              value={formData.title}
              onChange={(e) => updateField('title', e.target.value)}
              onFocus={() => setFocusedField('title')}
              onBlur={() => setFocusedField(null)}
              placeholder="Enter note title"
              className={cn(
                "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors",
                focusedField === 'title'
                  ? "border-emerald-600 dark:border-emerald-400"
                  : "hover:border-stone-300 dark:hover:border-stone-500",
                "text-stone-800 dark:text-stone-100 placeholder-stone-400 dark:placeholder-stone-500"
              )}
            />
          </div>

          {/* Content Field */}
          <div className="space-y-1">
            <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
              CONTENT
            </label>
            <textarea
              value={formData.content}
              onChange={(e) => updateField('content', e.target.value)}
              onFocus={() => setFocusedField('content')}
              onBlur={() => setFocusedField(null)}
              placeholder="Write your thoughts..."
              rows={6}
              className={cn(
                "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors resize-none",
                focusedField === 'content'
                  ? "border-emerald-600 dark:border-emerald-400"
                  : "hover:border-stone-300 dark:hover:border-stone-500",
                "text-stone-800 dark:text-stone-100 placeholder-stone-400 dark:placeholder-stone-500"
              )}
            />
          </div>

          {/* Tags and Category - Compact Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {/* Tags Field */}
            <div className="space-y-1">
              <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider flex items-center gap-1">
                <Tag className="w-3 h-3" />
                TAGS
              </label>
              <input
                type="text"
                value={formData.tags}
                onChange={(e) => updateField('tags', e.target.value)}
                onFocus={() => setFocusedField('tags')}
                onBlur={() => setFocusedField(null)}
                placeholder="tag1, tag2, tag3"
                className={cn(
                  "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors",
                  focusedField === 'tags'
                    ? "border-emerald-600 dark:border-emerald-400"
                    : "hover:border-stone-300 dark:hover:border-stone-500",
                  "text-stone-800 dark:text-stone-100 placeholder-stone-400 dark:placeholder-stone-500"
                )}
              />
            </div>

            {/* Category Selector */}
            <div className="space-y-1">
              <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider flex items-center gap-1">
                <Folder className="w-3 h-3" />
                CATEGORY
              </label>
              <div className="space-y-1">
                {categoryOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => updateField('category', option.value)}
                    className={cn(
                      "w-full px-2 py-1.5 text-xs border transition-colors flex items-center gap-2",
                      formData.category === option.value
                        ? "border-emerald-600 dark:border-emerald-400 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300"
                        : "border-stone-200 dark:border-stone-600 hover:border-stone-300 dark:hover:border-stone-500 text-stone-600 dark:text-stone-400"
                    )}
                  >
                    <span>{option.icon}</span>
                    <span>{option.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Footer - Minimal */}
        <div className="p-4 border-t border-stone-200 dark:border-stone-700">
          <div className="flex gap-2">
            <Button
              variant="ghost"
              onClick={handleClose}
              className="flex-1 px-3 py-2 text-sm border border-stone-300 dark:border-stone-600 hover:bg-stone-100 dark:hover:bg-stone-600"
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSave}
              disabled={!formData.title.trim() || isLoading}
              className={cn(
                "flex-1 px-3 py-2 text-sm bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "flex items-center justify-center gap-1"
              )}
            >
              {isLoading ? (
                <>
                  <div className="w-3 h-3 border border-white/30 border-t-white rounded-full animate-spin" />
                  Saving
                </>
              ) : (
                <>
                  <Save className="w-3 h-3" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Custom Confirmation Dialog - Scandinavian Clean */}
        {showConfirmDialog && (
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 shadow-lg max-w-xs w-full">
              <div className="p-4">
                <h3 className="text-sm font-light text-stone-800 dark:text-stone-100 uppercase tracking-wider mb-2">
                  UNSAVED CHANGES
                </h3>
                <p className="text-xs text-stone-600 dark:text-stone-400 mb-4">
                  You have unsaved changes. Are you sure you want to close?
                </p>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    onClick={handleCancelClose}
                    className="flex-1 px-2 py-1.5 text-xs border border-stone-300 dark:border-stone-600 hover:bg-stone-100 dark:hover:bg-stone-600"
                  >
                    Keep Editing
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleConfirmClose}
                    className="flex-1 px-2 py-1.5 text-xs bg-amber-600 hover:bg-amber-700 dark:bg-amber-500 dark:hover:bg-amber-600 text-white"
                  >
                    Discard
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default EditNoteModal
