import React, { useState, useEffect } from 'react'
import { Save, Tag, Star } from 'lucide-react'
import { cn } from '@/lib/utils'
import BottomSheet from '../UI/BottomSheet'
import Button from '../UI/Button'
import Input from '../UI/Input'

interface Note {
  id: string
  title: string
  content: string
  tags: string[]
  favorite: boolean
  createdAt: Date
  updatedAt: Date
}

interface NoteEditorProps {
  note: Note | null
  isOpen: boolean
  onClose: () => void
  onSave: (note: Partial<Note>) => Promise<void>
  isLoading?: boolean
}

const NoteEditor: React.FC<NoteEditorProps> = ({
  note,
  isOpen,
  onClose,
  onSave,
  isLoading = false
}) => {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    tags: [] as string[],
    favorite: false
  })
  const [newTag, setNewTag] = useState('')

  useEffect(() => {
    if (note) {
      setFormData({
        title: note.title,
        content: note.content,
        tags: note.tags,
        favorite: note.favorite
      })
    } else {
      setFormData({
        title: '',
        content: '',
        tags: [],
        favorite: false
      })
    }
  }, [note])

  const handleSave = async () => {
    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Failed to save note:', error)
    }
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave()
    }
  }

  return (
    <BottomSheet
      isOpen={isOpen}
      onClose={onClose}
      title={note ? 'Edit Note' : 'New Note'}
    >
      <div className="space-y-6" onKeyDown={handleKeyPress}>
        {/* Title */}
        <Input
          label="Title"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          placeholder="Enter note title..."
          autoFocus
        />

        {/* Content */}
        <div>
          <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
            Content
          </label>
          <textarea
            value={formData.content}
            onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
            placeholder="Write your note content..."
            className={cn(
              'w-full px-3 py-2 border border-stone-200 dark:border-stone-700',
              'bg-white dark:bg-stone-800 text-stone-800 dark:text-stone-200',
              'placeholder-stone-400 dark:placeholder-stone-500',
              'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent',
              'resize-none min-h-[120px]'
            )}
            rows={6}
          />
        </div>

        {/* Tags */}
        <div>
          <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
            Tags
          </label>
          
          {/* Tag Input */}
          <div className="flex gap-2 mb-3">
            <Input
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              placeholder="Add tag..."
              onKeyPress={(e) => e.key === 'Enter' && addTag()}
              className="flex-1"
            />
            <Button
              variant="secondary"
              size="sm"
              onClick={addTag}
              disabled={!newTag.trim()}
            >
              <Tag className="w-4 h-4" />
            </Button>
          </div>

          {/* Tag List */}
          {formData.tags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {formData.tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 text-xs uppercase tracking-wider cursor-pointer hover:bg-emerald-200 dark:hover:bg-emerald-900/50"
                  onClick={() => removeTag(tag)}
                >
                  {tag}
                  <span className="text-emerald-500 dark:text-emerald-400">×</span>
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Favorite Toggle */}
        <div className="flex items-center gap-3">
          <button
            onClick={() => setFormData(prev => ({ ...prev, favorite: !prev.favorite }))}
            className={cn(
              'flex items-center gap-2 px-3 py-2 transition-colors',
              formData.favorite
                ? 'text-amber-700 dark:text-amber-600'
                : 'text-stone-500 dark:text-stone-400 hover:text-amber-700 dark:hover:text-amber-600'
            )}
          >
            <Star className={cn('w-4 h-4', formData.favorite && 'fill-current')} />
            <span className="text-sm font-normal uppercase tracking-wider">
              {formData.favorite ? 'Favorited' : 'Add to Favorites'}
            </span>
          </button>
        </div>

        {/* Actions */}
        <div className="flex gap-3 pt-4 border-t border-stone-200 dark:border-stone-700">
          <Button
            variant="secondary"
            onClick={onClose}
            className="flex-1"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            className="flex-1"
            disabled={isLoading || !formData.title.trim()}
          >
            <Save className="w-4 h-4 mr-2" />
            {isLoading ? 'Saving...' : 'Save Note'}
          </Button>
        </div>

        {/* Keyboard Shortcut Hint */}
        <div className="text-center">
          <p className="text-xs text-stone-400 dark:text-stone-500 uppercase tracking-wider">
            Ctrl+Enter to save • Esc to cancel
          </p>
        </div>
      </div>
    </BottomSheet>
  )
}

export default NoteEditor
