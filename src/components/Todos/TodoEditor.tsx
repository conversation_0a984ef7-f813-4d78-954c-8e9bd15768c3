import React, { useState, useEffect } from 'react'
import { Save, Calendar, Flag } from 'lucide-react'
import { cn } from '@/lib/utils'
import BottomSheet from '../UI/BottomSheet'
import Button from '../UI/Button'
import Input from '../UI/Input'
import Select from '../UI/Select'

interface Todo {
  id: string
  title: string
  description?: string
  priority: 'low' | 'medium' | 'high'
  dueDate?: Date
  completed: boolean
  createdAt: Date
  updatedAt: Date
}

interface TodoEditorProps {
  todo: Todo | null
  isOpen: boolean
  onClose: () => void
  onSave: (todo: Partial<Todo>) => Promise<void>
  isLoading?: boolean
}

const TodoEditor: React.FC<TodoEditorProps> = ({
  todo,
  isOpen,
  onClose,
  onSave,
  isLoading = false
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium' as 'low' | 'medium' | 'high',
    dueDate: '',
    dueTime: ''
  })

  useEffect(() => {
    if (todo) {
      const dueDate = todo.dueDate ? new Date(todo.dueDate) : null
      setFormData({
        title: todo.title,
        description: todo.description || '',
        priority: todo.priority,
        dueDate: dueDate ? dueDate.toISOString().split('T')[0] : '',
        dueTime: dueDate ? dueDate.toTimeString().slice(0, 5) : ''
      })
    } else {
      setFormData({
        title: '',
        description: '',
        priority: 'medium',
        dueDate: '',
        dueTime: ''
      })
    }
  }, [todo])

  const handleSave = async () => {
    try {
      const saveData: Partial<Todo> = {
        title: formData.title,
        description: formData.description || undefined,
        priority: formData.priority
      }

      // Combine date and time if both are provided
      if (formData.dueDate) {
        const dateTime = formData.dueTime 
          ? `${formData.dueDate}T${formData.dueTime}:00`
          : `${formData.dueDate}T23:59:59`
        saveData.dueDate = new Date(dateTime)
      }

      await onSave(saveData)
      onClose()
    } catch (error) {
      console.error('Failed to save todo:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave()
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-amber-700 dark:text-amber-600'
      case 'medium':
        return 'text-emerald-700 dark:text-emerald-600'
      case 'low':
        return 'text-stone-500 dark:text-stone-400'
      default:
        return 'text-stone-500 dark:text-stone-400'
    }
  }

  return (
    <BottomSheet
      isOpen={isOpen}
      onClose={onClose}
      title={todo ? 'Edit Todo' : 'New Todo'}
    >
      <div className="space-y-6" onKeyDown={handleKeyPress}>
        {/* Title */}
        <Input
          label="Title"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          placeholder="Enter todo title..."
          autoFocus
        />

        {/* Description */}
        <div>
          <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Add description (optional)..."
            className={cn(
              'w-full px-3 py-2 border border-stone-200 dark:border-stone-700',
              'bg-white dark:bg-stone-800 text-stone-800 dark:text-stone-200',
              'placeholder-stone-400 dark:placeholder-stone-500',
              'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent',
              'resize-none'
            )}
            rows={3}
          />
        </div>

        {/* Priority */}
        <div>
          <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
            Priority
          </label>
          <div className="flex gap-2">
            {(['low', 'medium', 'high'] as const).map((priority) => (
              <button
                key={priority}
                onClick={() => setFormData(prev => ({ ...prev, priority }))}
                className={cn(
                  'flex items-center gap-2 px-3 py-2 border transition-colors',
                  'border-stone-200 dark:border-stone-700',
                  formData.priority === priority
                    ? 'bg-stone-100 dark:bg-stone-700 border-emerald-500'
                    : 'bg-white dark:bg-stone-800 hover:bg-stone-50 dark:hover:bg-stone-700'
                )}
              >
                <Flag className={cn('w-4 h-4', getPriorityColor(priority))} />
                <span className={cn(
                  'text-sm font-normal uppercase tracking-wider',
                  getPriorityColor(priority)
                )}>
                  {priority}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Due Date & Time */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
              Due Date
            </label>
            <Input
              type="date"
              value={formData.dueDate}
              onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
            />
          </div>
          <div>
            <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
              Due Time
            </label>
            <Input
              type="time"
              value={formData.dueTime}
              onChange={(e) => setFormData(prev => ({ ...prev, dueTime: e.target.value }))}
              disabled={!formData.dueDate}
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-3 pt-4 border-t border-stone-200 dark:border-stone-700">
          <Button
            variant="secondary"
            onClick={onClose}
            className="flex-1"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            className="flex-1"
            disabled={isLoading || !formData.title.trim()}
          >
            <Save className="w-4 h-4 mr-2" />
            {isLoading ? 'Saving...' : 'Save Todo'}
          </Button>
        </div>

        {/* Keyboard Shortcut Hint */}
        <div className="text-center">
          <p className="text-xs text-stone-400 dark:text-stone-500 uppercase tracking-wider">
            Ctrl+Enter to save • Esc to cancel
          </p>
        </div>
      </div>
    </BottomSheet>
  )
}

export default TodoEditor
