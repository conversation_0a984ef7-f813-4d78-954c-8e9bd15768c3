import React, { useState, useEffect, useRef } from 'react'
import { X, Save, Calendar, ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import Button from '@/components/UI/Button'

interface Todo {
  id: string
  title: string
  description?: string
  completed: boolean
  priority: 'low' | 'medium' | 'high'
  dueDate?: Date
}

interface EditTodoModalProps {
  todo: Todo | null
  isOpen: boolean
  onClose: () => void
  onSave: (updatedTodo: {
    title: string
    description: string
    priority: 'low' | 'medium' | 'high'
    dueDate: string
  }) => Promise<void>
  isLoading?: boolean
}

const EditTodoModal: React.FC<EditTodoModalProps> = ({
  todo,
  isOpen,
  onClose,
  onSave,
  isLoading = false
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium' as 'low' | 'medium' | 'high',
    dueDate: ''
  })
  
  const [hasChanges, setHasChanges] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const titleInputRef = useRef<HTMLInputElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Initialize form data when todo changes
  useEffect(() => {
    if (todo && isOpen) {
      const newFormData = {
        title: todo.title,
        description: todo.description || '',
        priority: todo.priority,
        dueDate: todo.dueDate ? todo.dueDate.toISOString().split('T')[0] : ''
      }
      setFormData(newFormData)
      setSelectedDate(todo.dueDate || null)
      setHasChanges(false)

      // Focus title input after modal opens
      setTimeout(() => {
        titleInputRef.current?.focus()
        titleInputRef.current?.select()
      }, 150)
    }
  }, [todo, isOpen])

  // Track changes
  useEffect(() => {
    if (!todo) return
    
    const hasChanged = 
      formData.title !== todo.title ||
      formData.description !== (todo.description || '') ||
      formData.priority !== todo.priority ||
      formData.dueDate !== (todo.dueDate ? todo.dueDate.toISOString().split('T')[0] : '')
    
    setHasChanges(hasChanged)
  }, [formData, todo])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return

      if (e.key === 'Escape') {
        if (showDatePicker) {
          setShowDatePicker(false)
        } else {
          handleClose()
        }
      } else if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
        e.preventDefault()
        handleSave()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, formData, showDatePicker])

  // Handle click outside date picker
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (showDatePicker && modalRef.current && !modalRef.current.contains(e.target as Node)) {
        setShowDatePicker(false)
      }
    }

    if (showDatePicker) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showDatePicker])

  const handleClose = () => {
    if (hasChanges) {
      setShowConfirmDialog(true)
      return
    }
    onClose()
  }

  const handleConfirmClose = () => {
    setShowConfirmDialog(false)
    onClose()
  }

  const handleCancelClose = () => {
    setShowConfirmDialog(false)
  }

  const handleSave = async () => {
    if (!formData.title.trim() || isLoading) return
    
    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Failed to save todo:', error)
    }
  }

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Date picker functions
  const formatDateDisplay = (date: Date | null) => {
    if (!date) return 'Select date'
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
    setFormData(prev => ({
      ...prev,
      dueDate: date.toISOString().split('T')[0]
    }))
    setShowDatePicker(false)
  }

  const clearDate = () => {
    setSelectedDate(null)
    setFormData(prev => ({ ...prev, dueDate: '' }))
    setShowDatePicker(false)
  }

  // Quick date options
  const getQuickDateOptions = () => {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)
    const nextWeek = new Date(today)
    nextWeek.setDate(today.getDate() + 7)

    return [
      { label: 'Today', date: today },
      { label: 'Tomorrow', date: tomorrow },
      { label: 'Next Week', date: nextWeek }
    ]
  }

  // Calendar functionality
  const [calendarDate, setCalendarDate] = useState(new Date())
  const [showCalendar, setShowCalendar] = useState(false)

  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay()
  }

  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(calendarDate)
    const firstDay = getFirstDayOfMonth(calendarDate)
    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(null)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day)
    }

    return days
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCalendarDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const selectCalendarDate = (day: number) => {
    const selectedDate = new Date(calendarDate.getFullYear(), calendarDate.getMonth(), day)
    handleDateSelect(selectedDate)
    setShowCalendar(false)
  }

  const isToday = (day: number) => {
    const today = new Date()
    return day === today.getDate() &&
           calendarDate.getMonth() === today.getMonth() &&
           calendarDate.getFullYear() === today.getFullYear()
  }

  const isSelectedDate = (day: number) => {
    if (!selectedDate) return false
    return day === selectedDate.getDate() &&
           calendarDate.getMonth() === selectedDate.getMonth() &&
           calendarDate.getFullYear() === selectedDate.getFullYear()
  }

  const priorityOptions = [
    { value: 'low', label: 'Low', color: 'text-emerald-600 dark:text-emerald-400' },
    { value: 'medium', label: 'Medium', color: 'text-amber-600 dark:text-amber-400' },
    { value: 'high', label: 'High', color: 'text-stone-600 dark:text-stone-400' }
  ]

  if (!isOpen || !todo) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/40 transition-opacity duration-200"
        onClick={handleClose}
      />

      {/* Modal - Scandinavian Clean */}
      <div
        ref={modalRef}
        className={cn(
          "relative w-full max-w-md bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 transition-all duration-200",
          "shadow-lg overflow-visible", // Allow dropdown to extend outside modal
          isOpen ? "translate-y-0 opacity-100 scale-100" : "translate-y-4 opacity-0 scale-95"
        )}
      >
        {/* Header - Minimal */}
        <div className="p-4 border-b border-stone-200 dark:border-stone-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-light text-stone-800 dark:text-stone-100 uppercase tracking-wider">
              EDIT TODO
            </h2>
            <button
              onClick={handleClose}
              className="w-6 h-6 flex items-center justify-center text-stone-400 dark:text-stone-500 hover:text-stone-600 dark:hover:text-stone-300 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Content - Compact */}
        <div className="p-4 space-y-4 overflow-visible">
          {/* Title Field */}
          <div className="space-y-1">
            <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
              TITLE
            </label>
            <input
              ref={titleInputRef}
              type="text"
              value={formData.title}
              onChange={(e) => updateField('title', e.target.value)}
              onFocus={() => setFocusedField('title')}
              onBlur={() => setFocusedField(null)}
              placeholder="What needs to be done?"
              className={cn(
                "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors",
                focusedField === 'title'
                  ? "border-emerald-600 dark:border-emerald-400"
                  : "hover:border-stone-300 dark:hover:border-stone-500",
                "text-stone-800 dark:text-stone-100 placeholder-stone-400 dark:placeholder-stone-500"
              )}
            />
          </div>

          {/* Description Field */}
          <div className="space-y-1">
            <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
              DESCRIPTION
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => updateField('description', e.target.value)}
              onFocus={() => setFocusedField('description')}
              onBlur={() => setFocusedField(null)}
              placeholder="Add details (optional)"
              rows={2}
              className={cn(
                "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors resize-none",
                focusedField === 'description'
                  ? "border-emerald-600 dark:border-emerald-400"
                  : "hover:border-stone-300 dark:hover:border-stone-500",
                "text-stone-800 dark:text-stone-100 placeholder-stone-400 dark:placeholder-stone-500"
              )}
            />
          </div>

          {/* Priority and Due Date - Compact Grid */}
          <div className="grid grid-cols-2 gap-4">
            {/* Priority Selector */}
            <div className="space-y-1">
              <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
                PRIORITY
              </label>
              <div className="space-y-1">
                {priorityOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => updateField('priority', option.value)}
                    className={cn(
                      "w-full px-2 py-1.5 text-xs border transition-colors flex items-center justify-center",
                      formData.priority === option.value
                        ? "border-emerald-600 dark:border-emerald-400 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300"
                        : "border-stone-200 dark:border-stone-600 hover:border-stone-300 dark:hover:border-stone-500 text-stone-600 dark:text-stone-400"
                    )}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Custom Date Picker - Scandinavian Clean */}
            <div className="space-y-1 relative">
              <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
                DUE DATE
              </label>

              {/* Date Display Button */}
              <button
                type="button"
                onClick={() => setShowDatePicker(!showDatePicker)}
                className={cn(
                  "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors text-left flex items-center justify-between",
                  showDatePicker
                    ? "border-emerald-600 dark:border-emerald-400"
                    : "hover:border-stone-300 dark:hover:border-stone-500",
                  selectedDate
                    ? "text-stone-800 dark:text-stone-100"
                    : "text-stone-400 dark:text-stone-500"
                )}
              >
                <span>{formatDateDisplay(selectedDate)}</span>
                <Calendar className="w-3 h-3 text-stone-400 dark:text-stone-500" />
              </button>

              {/* Custom Date Picker - Mobile-Optimized Modal */}
              {showDatePicker && (
                <>
                  {/* Mobile Overlay */}
                  <div
                    className="fixed inset-0 bg-black/40 z-50 sm:hidden"
                    onClick={() => setShowDatePicker(false)}
                  />

                  {/* Date Picker Content */}
                  <div className={`
                    sm:absolute sm:top-full sm:left-0 sm:right-0 sm:mt-1 sm:z-10
                    fixed bottom-0 left-0 right-0 z-50 sm:relative sm:bottom-auto
                    bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 shadow-lg
                    sm:rounded-none rounded-t-lg
                    max-h-[70vh] overflow-y-auto
                  `}>
                  <div className="p-3">
                    {/* Current Selection Display */}
                    {selectedDate && (
                      <div className="mb-3 pb-3 border-b border-stone-200 dark:border-stone-600">
                        <div className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider mb-1">
                          SELECTED
                        </div>
                        <div className="text-sm text-stone-800 dark:text-stone-100 font-medium">
                          {formatDateDisplay(selectedDate)}
                        </div>
                      </div>
                    )}

                    {/* Quick Date Options - Fixed Grid */}
                    <div className="mb-3">
                      <div className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider mb-2">
                        QUICK SELECT
                      </div>
                      <div className="grid grid-cols-1 gap-2">
                        {getQuickDateOptions().map((option) => (
                          <button
                            key={option.label}
                            type="button"
                            onClick={() => handleDateSelect(option.date)}
                            className={cn(
                              "w-full px-3 py-2 text-sm border transition-colors text-left",
                              selectedDate && selectedDate.toDateString() === option.date.toDateString()
                                ? "border-emerald-600 dark:border-emerald-400 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300"
                                : "border-stone-200 dark:border-stone-600 hover:border-emerald-600 dark:hover:border-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 text-stone-700 dark:text-stone-300"
                            )}
                          >
                            <div className="flex items-center justify-between">
                              <span>{option.label}</span>
                              <span className="text-xs text-stone-500 dark:text-stone-400">
                                {option.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                              </span>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Calendar Toggle */}
                    <div className="pt-2 border-t border-stone-200 dark:border-stone-600">
                      <button
                        type="button"
                        onClick={() => setShowCalendar(!showCalendar)}
                        className="w-full px-3 py-2 text-sm text-stone-600 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200 transition-colors text-center flex items-center justify-center gap-2"
                      >
                        <Calendar className="w-3 h-3" />
                        {showCalendar ? 'Hide Calendar' : 'Show Calendar'}
                      </button>
                    </div>

                    {/* Full Calendar Interface */}
                    {showCalendar && (
                      <div className="pt-3 border-t border-stone-200 dark:border-stone-600">
                        {/* Calendar Header */}
                        <div className="flex items-center justify-between mb-3">
                          <button
                            type="button"
                            onClick={() => navigateMonth('prev')}
                            className="p-1 hover:bg-stone-100 dark:hover:bg-stone-600 transition-colors"
                          >
                            <ChevronLeft className="w-4 h-4 text-stone-600 dark:text-stone-400" />
                          </button>

                          <div className="text-sm font-medium text-stone-800 dark:text-stone-100">
                            {calendarDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                          </div>

                          <button
                            type="button"
                            onClick={() => navigateMonth('next')}
                            className="p-1 hover:bg-stone-100 dark:hover:bg-stone-600 transition-colors"
                          >
                            <ChevronRight className="w-4 h-4 text-stone-600 dark:text-stone-400" />
                          </button>
                        </div>

                        {/* Calendar Grid */}
                        <div className="grid grid-cols-7 gap-1 mb-2">
                          {/* Day headers */}
                          {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
                            <div key={day} className="text-xs text-stone-500 dark:text-stone-400 text-center py-1">
                              {day}
                            </div>
                          ))}

                          {/* Calendar days */}
                          {generateCalendarDays().map((day, index) => (
                            <button
                              key={index}
                              type="button"
                              onClick={() => day && selectCalendarDate(day)}
                              disabled={!day}
                              className={cn(
                                "text-xs py-1.5 transition-colors",
                                !day && "invisible",
                                day && "hover:bg-emerald-50 dark:hover:bg-emerald-900/20",
                                isToday(day || 0) && "bg-stone-100 dark:bg-stone-600 font-medium",
                                isSelectedDate(day || 0) && "bg-emerald-600 text-white hover:bg-emerald-700",
                                day && !isSelectedDate(day) && !isToday(day) && "text-stone-700 dark:text-stone-300"
                              )}
                            >
                              {day}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Clear Date Option */}
                    <div className="pt-2 border-t border-stone-200 dark:border-stone-600">
                      <button
                        type="button"
                        onClick={clearDate}
                        className="w-full px-3 py-2 text-sm text-stone-500 dark:text-stone-400 hover:text-stone-700 dark:hover:text-stone-200 transition-colors text-center"
                      >
                        Clear Date
                      </button>
                    </div>
                  </div>
                </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Footer - Minimal */}
        <div className="p-4 border-t border-stone-200 dark:border-stone-700">
          <div className="flex gap-2">
            <Button
              variant="ghost"
              onClick={handleClose}
              className="flex-1 px-3 py-2 text-sm border border-stone-300 dark:border-stone-600 hover:bg-stone-100 dark:hover:bg-stone-600"
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSave}
              disabled={!formData.title.trim() || isLoading}
              className={cn(
                "flex-1 px-3 py-2 text-sm bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "flex items-center justify-center gap-1"
              )}
            >
              {isLoading ? (
                <>
                  <div className="w-3 h-3 border border-white/30 border-t-white rounded-full animate-spin" />
                  Saving
                </>
              ) : (
                <>
                  <Save className="w-3 h-3" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Custom Confirmation Dialog - Scandinavian Clean */}
        {showConfirmDialog && (
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 shadow-lg max-w-xs w-full">
              <div className="p-4">
                <h3 className="text-sm font-light text-stone-800 dark:text-stone-100 uppercase tracking-wider mb-2">
                  UNSAVED CHANGES
                </h3>
                <p className="text-xs text-stone-600 dark:text-stone-400 mb-4">
                  You have unsaved changes. Are you sure you want to close?
                </p>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    onClick={handleCancelClose}
                    className="flex-1 px-2 py-1.5 text-xs border border-stone-300 dark:border-stone-600 hover:bg-stone-100 dark:hover:bg-stone-600"
                  >
                    Keep Editing
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleConfirmClose}
                    className="flex-1 px-2 py-1.5 text-xs bg-amber-600 hover:bg-amber-700 dark:bg-amber-500 dark:hover:bg-amber-600 text-white"
                  >
                    Discard
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default EditTodoModal
