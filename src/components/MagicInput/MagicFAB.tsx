import React, { useState } from 'react'
import { Sparkles } from 'lucide-react'
import { useAppStore } from '@/store/appStore'
import { cn } from '@/lib/utils'
import MagicInputPanel from './MagicInputPanel'

const MagicFAB: React.FC = () => {
  const { magicInputOpen, setMagicInputOpen, isLoading, openMagicInputWithContext } = useAppStore()
  const [isClosing, setIsClosing] = useState(false)

  const handleFABClick = () => {
    // Only handle opening since FAB is hidden when panel is open
    // Always open with general context when Magic FAB is clicked directly
    openMagicInputWithContext('general')
  }

  const handleBackdropClick = () => {
    // Handle closing with animation
    setIsClosing(true)
    setTimeout(() => {
      setMagicInputOpen(false)
      setIsClosing(false)
    }, 300) // Match animation duration
  }

  return (
    <>
      {/* Backdrop */}
      {magicInputOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 animate-fade-in"
          onClick={handleBackdropClick}
        />
      )}

      {/* Magic Input Panel */}
      <MagicInputPanel isClosing={isClosing} onClose={handleBackdropClick} />

      {/* FAB Button - Scandinavian Clean */}
      {!magicInputOpen && (
        <div className="fixed bottom-24 right-4 z-50">
          <button
            onClick={handleFABClick}
            className={cn(
              'w-14 h-14 shadow-lg transition-all duration-300 ease-out',
              'flex items-center justify-center transform',
              'bg-emerald-700 dark:bg-emerald-600',
              'hover:bg-emerald-800 dark:hover:bg-emerald-700 active:scale-95',
              // Add margin bottom to prevent overlap with bottom content
              'mb-2'
            )}
            aria-label="Open Magic Input"
          >
            <div className="relative">
              <Sparkles className="w-6 h-6 text-white" />

              {/* AI Processing Ring */}
              {isLoading && (
                <div className="absolute inset-0 border-2 border-white border-t-transparent animate-spin" />
              )}
            </div>
          </button>
        </div>
      )}
    </>
  )
}

export default MagicFAB
