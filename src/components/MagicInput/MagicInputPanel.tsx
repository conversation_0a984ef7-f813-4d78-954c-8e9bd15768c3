import React, { useState } from 'react'
import { X, Layers, Keyboard, Mic, Clipboard, Image, Trash2, Sparkles } from 'lucide-react'
import { useAppStore } from '@/store/appStore'
import { useAPI } from '@/services/api'
import { cn } from '@/lib/utils'
import Button from '../UI/Button'

interface ContentBlock {
  id: string
  type: 'text' | 'voice' | 'clipboard' | 'image'
  content: string
  timestamp: number
}

interface MagicInputPanelProps {
  isClosing?: boolean
  onClose?: () => void
}

const MagicInputPanel: React.FC<MagicInputPanelProps> = ({
  isClosing: externalIsClosing = false,
  onClose
}) => {
  const { magicInputOpen, setMagicInputOpen, isLoading, magicInputContext } = useAppStore()
  const { createEntry } = useAPI()
  const [contentBlocks, setContentBlocks] = useState<ContentBlock[]>([])
  const [textInput, setTextInput] = useState('')
  const [showTextInput, setShowTextInput] = useState(false)
  const [isClosing, setIsClosing] = useState(false)

  // Context-aware configuration
  const getContextConfig = () => {
    switch (magicInputContext) {
      case 'calendar':
        return {
          title: 'Calendar Event',
          placeholder: 'Describe your event (e.g., "Team meeting tomorrow at 3 PM")',
          icon: '📅',
          color: 'from-blue-500 to-purple-600'
        }
      case 'todos':
        return {
          title: 'Todo Item',
          placeholder: 'What do you need to get done? (e.g., "Buy groceries by Friday")',
          icon: '✅',
          color: 'from-green-500 to-teal-600'
        }
      case 'notes':
        return {
          title: 'Note',
          placeholder: 'Capture your thoughts and ideas...',
          icon: '📝',
          color: 'from-orange-500 to-pink-600'
        }
      default:
        return {
          title: 'Mixed Content',
          placeholder: 'Type your text here...',
          icon: '✨',
          color: 'from-accent-primary to-accent-purple'
        }
    }
  }

  const contextConfig = getContextConfig()

  // Auto-open text input when panel opens
  React.useEffect(() => {
    if (magicInputOpen && contentBlocks.length === 0) {
      setShowTextInput(true)
    }
  }, [magicInputOpen, contentBlocks.length])

  // Handle smooth closing animation
  const handleClose = () => {
    if (onClose) {
      onClose()
    } else {
      // Fallback to direct close if no onClose provided
      setIsClosing(true)
      setTimeout(() => {
        setMagicInputOpen(false)
        setIsClosing(false)
      }, 300) // Match animation duration
    }
  }

  const addContentBlock = (type: ContentBlock['type'], content: string) => {
    const newBlock: ContentBlock = {
      id: crypto.randomUUID(),
      type,
      content,
      timestamp: Date.now()
    }
    setContentBlocks(prev => [...prev, newBlock])
  }

  const removeContentBlock = (id: string) => {
    setContentBlocks(prev => prev.filter(block => block.id !== id))
  }

  const handleAddText = () => {
    if (textInput.trim()) {
      addContentBlock('text', textInput.trim())
      setTextInput('')
      setShowTextInput(false)
    }
  }

  const handleVoiceInput = () => {
    // Voice input is disabled in current phase
    console.log('Voice input is not available in this version')
  }

  const handleClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText()
      if (text.trim()) {
        addContentBlock('clipboard', text.trim())
      } else {
        console.log('Clipboard is empty')
      }
    } catch (error) {
      console.error('Failed to read clipboard:', error)
      // Fallback: show a message to user
      alert('Unable to access clipboard. Please paste manually using Ctrl+V.')
    }
  }

  const handleImageUpload = () => {
    // Image upload is disabled in current phase
    console.log('Image upload is not available in this version')
  }

  const handleProcess = async () => {
    if (contentBlocks.length === 0) return

    try {
      // Combine all content blocks into raw text
      const rawText = contentBlocks.map(block => {
        // Add type prefix for non-text blocks
        if (block.type === 'clipboard') {
          return `[CLIPBOARD] ${block.content}`
        }
        return block.content
      }).join('\n\n')

      // Add context information to the raw text for AI processing
      const contextualRawText = magicInputContext !== 'general'
        ? `[${magicInputContext.toUpperCase()}] ${rawText}`
        : rawText

      console.log('Processing content:', {
        context: magicInputContext,
        blocks: contentBlocks.length,
        rawText: contextualRawText
      })

      // Send to API for processing
      await createEntry(contextualRawText)

      // Clear content and close panel on success
      setContentBlocks([])
      setTextInput('')
      setShowTextInput(false)
      setMagicInputOpen(false)

      console.log('Content processed successfully')
    } catch (error) {
      console.error('Failed to process content:', error)
      // Error handling is done in the API hook, but we can provide user feedback
      // The error will be shown in the UI through the store's error state
    }
  }

  const handleClearAll = () => {
    setContentBlocks([])
    setTextInput('')
    setShowTextInput(false)
  }

  if (!magicInputOpen) return null

  return (
    <div className={cn(
      'fixed bottom-0 left-1/2 w-full max-w-md',
      'bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 shadow-2xl z-50',
      (isClosing || externalIsClosing) ? 'animate-panel-to-fab' : 'animate-fab-to-panel'
    )}
    style={{
      transform: 'translateX(-50%)'
    }}>
      {/* Header - Scandinavian Clean */}
      <div className="flex items-center justify-between p-4 border-b border-stone-200 dark:border-stone-700">
        <div className="flex items-center gap-2">
          <div className={cn(
            'w-8 h-8 flex items-center justify-center text-white text-sm font-normal',
            contextConfig.color === 'from-blue-500 to-purple-600' ? 'bg-emerald-700 dark:bg-emerald-600' :
            contextConfig.color === 'from-green-500 to-blue-500' ? 'bg-emerald-700 dark:bg-emerald-600' :
            contextConfig.color === 'from-purple-500 to-pink-500' ? 'bg-amber-700 dark:bg-amber-600' :
            contextConfig.color === 'from-orange-500 to-red-500' ? 'bg-stone-600 dark:bg-stone-500' :
            'bg-emerald-700 dark:bg-emerald-600'
          )}>
            {contextConfig.icon}
          </div>
          <span className="font-normal text-stone-800 dark:text-stone-200 uppercase tracking-wider">{contextConfig.title}</span>
          {magicInputContext === 'general' && (
            <div className="bg-emerald-700 dark:bg-emerald-600 text-white text-xs px-2 py-1">
              {contentBlocks.length}
            </div>
          )}
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="p-1"
        >
          <X className="w-5 h-5" />
        </Button>
      </div>

      {/* Content Area */}
      <div className="p-4 max-h-96 overflow-y-auto custom-scrollbar">
        {/* Quick Actions */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowTextInput(!showTextInput)}
              className="p-2"
              title="Add Text"
            >
              <Keyboard className="w-5 h-5" />
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleVoiceInput}
              className="p-2 opacity-50 cursor-not-allowed"
              title="Voice Input (Coming Soon)"
              disabled
            >
              <Mic className="w-5 h-5" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleClipboard}
              className="p-2"
              title="Paste from Clipboard"
            >
              <Clipboard className="w-5 h-5" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={handleImageUpload}
              className="p-2 opacity-50 cursor-not-allowed"
              title="Add Image (Coming Soon)"
              disabled
            >
              <Image className="w-5 h-5" />
            </Button>
          </div>

          {/* Content Processing Options */}
          <div className="flex items-center gap-2 text-xs">
            <span className="text-text-secondary">
              {contentBlocks.length} content block{contentBlocks.length !== 1 ? 's' : ''}
            </span>
          </div>
        </div>

        {/* Text Input - Scandinavian Clean */}
        {showTextInput && (
          <div className="mb-4 p-3 bg-stone-50 dark:bg-stone-700 border border-stone-200 dark:border-stone-600">
            <textarea
              value={textInput}
              onChange={(e) => setTextInput(e.target.value)}
              onKeyDown={(e) => {
                // Handle Ctrl+Enter to add text
                if (e.ctrlKey && e.key === 'Enter') {
                  e.preventDefault()
                  handleAddText()
                }
                // Handle Escape to cancel
                if (e.key === 'Escape') {
                  e.preventDefault()
                  setShowTextInput(false)
                }
              }}
              placeholder={contextConfig.placeholder}
              className="w-full bg-transparent border-none outline-none resize-none text-stone-800 dark:text-stone-200 placeholder-stone-400 dark:placeholder-stone-500"
              rows={3}
              autoFocus
            />
            <div className="flex justify-between items-center mt-2">
              <div className="text-xs text-stone-500 dark:text-stone-400">
                <span>Ctrl+Enter to add • Esc to cancel</span>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowTextInput(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleAddText}
                  disabled={!textInput.trim()}
                >
                  Add
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Content Blocks - Scandinavian Clean */}
        {contentBlocks.length > 0 ? (
          <div className="space-y-2 mb-4">
            {contentBlocks.map((block) => (
              <div
                key={block.id}
                className="flex items-start gap-3 p-3 bg-stone-50 dark:bg-stone-700 border border-stone-200 dark:border-stone-600"
              >
                <div className="flex-shrink-0 mt-1">
                  {block.type === 'text' && <Keyboard className="w-4 h-4 text-emerald-700 dark:text-emerald-600" />}
                  {block.type === 'voice' && <Mic className="w-4 h-4 text-emerald-700 dark:text-emerald-600" />}
                  {block.type === 'clipboard' && <Clipboard className="w-4 h-4 text-amber-700 dark:text-amber-600" />}
                  {block.type === 'image' && <Image className="w-4 h-4 text-stone-600 dark:text-stone-400" />}
                </div>

                <div className="flex-1 min-w-0">
                  <p className="text-sm text-stone-800 dark:text-stone-200 break-words">
                    {block.content}
                  </p>
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeContentBlock(block.id)}
                  className="p-1 text-stone-500 dark:text-stone-400 hover:text-amber-700 dark:hover:text-amber-600"
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-stone-500 dark:text-stone-400">
            <div className="w-8 h-8 bg-stone-300 dark:bg-stone-600 mx-auto mb-2"></div>
            <p className="text-sm">Start building your content</p>
            <p className="text-xs mt-1">
              Use the <Keyboard className="w-3 h-3 inline mx-1" /> text input or
              <Clipboard className="w-3 h-3 inline mx-1" /> clipboard to add content
            </p>
            <p className="text-xs mt-1 opacity-75">
              Voice and image inputs coming soon
            </p>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="p-4 border-t border-border-primary">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-text-secondary">
            <Layers className="w-4 h-4" />
            <span>
              {contentBlocks.length === 0
                ? 'Add content to process'
                : `${contentBlocks.length} item${contentBlocks.length !== 1 ? 's' : ''} ready`
              }
            </span>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={handleClearAll}
              disabled={contentBlocks.length === 0 || isLoading}
            >
              <Trash2 className="w-4 h-4 mr-1" />
              Clear
            </Button>

            <Button
              variant="primary"
              size="sm"
              onClick={handleProcess}
              disabled={contentBlocks.length === 0}
              isLoading={isLoading}
            >
              <Sparkles className="w-4 h-4 mr-1" />
              {isLoading ? 'Processing...' : 'Process'}
            </Button>
          </div>
        </div>

        {/* Processing hint */}
        {contentBlocks.length > 0 && !isLoading && (
          <div className="mt-2 text-xs text-text-tertiary text-center">
            AI will analyze and organize your content automatically
          </div>
        )}
      </div>
    </div>
  )
}

export default MagicInputPanel
