import React, { useState, useEffect } from 'react'
import { Save, Calendar, Clock, Flag } from 'lucide-react'
import { cn } from '@/lib/utils'
import BottomSheet from '../UI/BottomSheet'
import Button from '../UI/Button'
import Input from '../UI/Input'
import Select from '../UI/Select'

interface CalendarEvent {
  id: string
  title: string
  description?: string
  date: Date
  time?: string
  type: 'meeting' | 'task' | 'reminder'
  priority?: 'low' | 'medium' | 'high'
}

interface EventEditorProps {
  event: CalendarEvent | null
  isOpen: boolean
  onClose: () => void
  onSave: (event: Partial<CalendarEvent>) => Promise<void>
  isLoading?: boolean
  defaultDate?: Date
}

const EventEditor: React.FC<EventEditorProps> = ({
  event,
  isOpen,
  onClose,
  onSave,
  isLoading = false,
  defaultDate
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    date: '',
    time: '',
    type: 'meeting' as 'meeting' | 'task' | 'reminder',
    priority: 'medium' as 'low' | 'medium' | 'high'
  })

  useEffect(() => {
    if (event) {
      setFormData({
        title: event.title,
        description: event.description || '',
        date: event.date.toISOString().split('T')[0],
        time: event.time || '',
        type: event.type,
        priority: event.priority || 'medium'
      })
    } else {
      const initialDate = defaultDate || new Date()
      setFormData({
        title: '',
        description: '',
        date: initialDate.toISOString().split('T')[0],
        time: '',
        type: 'meeting',
        priority: 'medium'
      })
    }
  }, [event, defaultDate])

  const handleSave = async () => {
    try {
      const saveData: Partial<CalendarEvent> = {
        title: formData.title,
        description: formData.description || undefined,
        date: new Date(formData.date),
        time: formData.time || undefined,
        type: formData.type,
        priority: formData.priority
      }

      await onSave(saveData)
      onClose()
    } catch (error) {
      console.error('Failed to save event:', error)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave()
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'meeting':
        return 'text-emerald-700 dark:text-emerald-600'
      case 'task':
        return 'text-emerald-700 dark:text-emerald-600'
      case 'reminder':
        return 'text-amber-700 dark:text-amber-600'
      default:
        return 'text-stone-500 dark:text-stone-400'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-amber-700 dark:text-amber-600'
      case 'medium':
        return 'text-emerald-700 dark:text-emerald-600'
      case 'low':
        return 'text-stone-500 dark:text-stone-400'
      default:
        return 'text-stone-500 dark:text-stone-400'
    }
  }

  return (
    <BottomSheet
      isOpen={isOpen}
      onClose={onClose}
      title={event ? 'Edit Event' : 'New Event'}
    >
      <div className="space-y-6" onKeyDown={handleKeyPress}>
        {/* Title */}
        <Input
          label="Title"
          value={formData.title}
          onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
          placeholder="Enter event title..."
          autoFocus
        />

        {/* Description */}
        <div>
          <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Add description (optional)..."
            className={cn(
              'w-full px-3 py-2 border border-stone-200 dark:border-stone-700',
              'bg-white dark:bg-stone-800 text-stone-800 dark:text-stone-200',
              'placeholder-stone-400 dark:placeholder-stone-500',
              'focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent',
              'resize-none'
            )}
            rows={3}
          />
        </div>

        {/* Date & Time */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
              Date
            </label>
            <Input
              type="date"
              value={formData.date}
              onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
            />
          </div>
          <div>
            <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
              Time
            </label>
            <Input
              type="time"
              value={formData.time}
              onChange={(e) => setFormData(prev => ({ ...prev, time: e.target.value }))}
              placeholder="Optional"
            />
          </div>
        </div>

        {/* Type */}
        <div>
          <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
            Type
          </label>
          <div className="flex gap-2">
            {(['meeting', 'task', 'reminder'] as const).map((type) => (
              <button
                key={type}
                onClick={() => setFormData(prev => ({ ...prev, type }))}
                className={cn(
                  'flex items-center gap-2 px-3 py-2 border transition-colors',
                  'border-stone-200 dark:border-stone-700',
                  formData.type === type
                    ? 'bg-stone-100 dark:bg-stone-700 border-emerald-500'
                    : 'bg-white dark:bg-stone-800 hover:bg-stone-50 dark:hover:bg-stone-700'
                )}
              >
                <Calendar className={cn('w-4 h-4', getTypeColor(type))} />
                <span className={cn(
                  'text-sm font-normal uppercase tracking-wider',
                  getTypeColor(type)
                )}>
                  {type}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Priority */}
        <div>
          <label className="block text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
            Priority
          </label>
          <div className="flex gap-2">
            {(['low', 'medium', 'high'] as const).map((priority) => (
              <button
                key={priority}
                onClick={() => setFormData(prev => ({ ...prev, priority }))}
                className={cn(
                  'flex items-center gap-2 px-3 py-2 border transition-colors',
                  'border-stone-200 dark:border-stone-700',
                  formData.priority === priority
                    ? 'bg-stone-100 dark:bg-stone-700 border-emerald-500'
                    : 'bg-white dark:bg-stone-800 hover:bg-stone-50 dark:hover:bg-stone-700'
                )}
              >
                <Flag className={cn('w-4 h-4', getPriorityColor(priority))} />
                <span className={cn(
                  'text-sm font-normal uppercase tracking-wider',
                  getPriorityColor(priority)
                )}>
                  {priority}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-3 pt-4 border-t border-stone-200 dark:border-stone-700">
          <Button
            variant="secondary"
            onClick={onClose}
            className="flex-1"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            className="flex-1"
            disabled={isLoading || !formData.title.trim()}
          >
            <Save className="w-4 h-4 mr-2" />
            {isLoading ? 'Saving...' : 'Save Event'}
          </Button>
        </div>

        {/* Keyboard Shortcut Hint */}
        <div className="text-center">
          <p className="text-xs text-stone-400 dark:text-stone-500 uppercase tracking-wider">
            Ctrl+Enter to save • Esc to cancel
          </p>
        </div>
      </div>
    </BottomSheet>
  )
}

export default EventEditor
