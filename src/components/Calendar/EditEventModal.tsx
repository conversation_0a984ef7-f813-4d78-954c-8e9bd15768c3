import React, { useState, useEffect, useRef } from 'react'
import { X, Save, Calendar, Clock, Flag, Users, ChevronLeft, ChevronRight } from 'lucide-react'
import { cn } from '@/lib/utils'
import Button from '@/components/UI/Button'
import { useAppStore } from '@/store/appStore'

interface CalendarEvent {
  id: string
  title: string
  description?: string
  date: Date
  time?: string
  type: 'meeting' | 'task' | 'reminder'
  priority?: 'low' | 'medium' | 'high'
}

interface EditEventModalProps {
  event: CalendarEvent | null
  isOpen: boolean
  onClose: () => void
  onSave: (updatedEvent: {
    title: string
    description: string
    type: 'meeting' | 'task' | 'reminder'
    priority: 'low' | 'medium' | 'high'
    date: string
    time: string
  }) => Promise<void>
  isLoading?: boolean
}

const EditEventModal: React.FC<EditEventModalProps> = ({
  event,
  isOpen,
  onClose,
  onSave,
  isLoading = false
}) => {
  const { theme } = useAppStore()
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'meeting' as 'meeting' | 'task' | 'reminder',
    priority: 'medium' as 'low' | 'medium' | 'high',
    date: '',
    time: ''
  })
  
  const [hasChanges, setHasChanges] = useState(false)
  const [focusedField, setFocusedField] = useState<string | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [showTimePicker, setShowTimePicker] = useState(false)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [calendarDate, setCalendarDate] = useState(new Date())
  const [showCalendar, setShowCalendar] = useState(false)
  const titleInputRef = useRef<HTMLInputElement>(null)
  const modalRef = useRef<HTMLDivElement>(null)

  // Initialize form data when event changes
  useEffect(() => {
    if (event && isOpen) {
      const newFormData = {
        title: event.title,
        description: event.description || '',
        type: event.type,
        priority: event.priority || 'medium',
        date: event.date.toISOString().split('T')[0],
        time: event.time || ''
      }
      setFormData(newFormData)
      setSelectedDate(event.date)
      setHasChanges(false)

      // Focus title input after modal opens
      setTimeout(() => {
        titleInputRef.current?.focus()
        titleInputRef.current?.select()
      }, 150)
    }
  }, [event, isOpen])

  // Track changes
  useEffect(() => {
    if (!event) return
    
    const hasChanged = 
      formData.title !== event.title ||
      formData.description !== (event.description || '') ||
      formData.type !== event.type ||
      formData.priority !== (event.priority || 'medium') ||
      formData.date !== event.date.toISOString().split('T')[0] ||
      formData.time !== (event.time || '')
    
    setHasChanges(hasChanged)
  }, [formData, event])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return

      if (e.key === 'Escape') {
        if (showDatePicker) {
          setShowDatePicker(false)
        } else if (showTimePicker) {
          setShowTimePicker(false)
        } else {
          handleClose()
        }
      } else if ((e.metaKey || e.ctrlKey) && e.key === 'Enter') {
        e.preventDefault()
        handleSave()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, formData, showDatePicker, showTimePicker])

  // Handle click outside pickers
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if ((showDatePicker || showTimePicker) && modalRef.current && !modalRef.current.contains(e.target as Node)) {
        setShowDatePicker(false)
        setShowTimePicker(false)
      }
    }

    if (showDatePicker || showTimePicker) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showDatePicker, showTimePicker])

  const handleClose = () => {
    if (hasChanges) {
      setShowConfirmDialog(true)
      return
    }
    onClose()
  }

  const handleConfirmClose = () => {
    setShowConfirmDialog(false)
    onClose()
  }

  const handleCancelClose = () => {
    setShowConfirmDialog(false)
  }

  const handleSave = async () => {
    if (!formData.title.trim() || isLoading) return
    
    try {
      await onSave(formData)
      onClose()
    } catch (error) {
      console.error('Failed to save event:', error)
    }
  }

  const updateField = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  // Date picker functions
  const formatDateDisplay = (date: Date | null) => {
    if (!date) return 'Select date'
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date)
    setFormData(prev => ({
      ...prev,
      date: date.toISOString().split('T')[0]
    }))
    setShowDatePicker(false)
  }

  const clearDate = () => {
    setSelectedDate(null)
    setFormData(prev => ({ ...prev, date: '' }))
    setShowDatePicker(false)
  }

  // Calendar functionality
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate()
  }

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay()
  }

  const generateCalendarDays = () => {
    const daysInMonth = getDaysInMonth(calendarDate)
    const firstDay = getFirstDayOfMonth(calendarDate)
    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      days.push(null)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(day)
    }

    return days
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCalendarDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const selectCalendarDate = (day: number) => {
    const selectedDate = new Date(calendarDate.getFullYear(), calendarDate.getMonth(), day)
    handleDateSelect(selectedDate)
    setShowCalendar(false)
  }

  const isToday = (day: number) => {
    const today = new Date()
    return day === today.getDate() &&
           calendarDate.getMonth() === today.getMonth() &&
           calendarDate.getFullYear() === today.getFullYear()
  }

  const isSelectedDate = (day: number) => {
    if (!selectedDate) return false
    return day === selectedDate.getDate() &&
           calendarDate.getMonth() === selectedDate.getMonth() &&
           calendarDate.getFullYear() === selectedDate.getFullYear()
  }

  // Quick date options
  const getQuickDateOptions = () => {
    const today = new Date()
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)
    const nextWeek = new Date(today)
    nextWeek.setDate(nextWeek.getDate() + 7)

    return [
      { label: 'Today', date: today },
      { label: 'Tomorrow', date: tomorrow },
      { label: 'Next Week', date: nextWeek }
    ]
  }

  // Time picker functions
  const formatTimeDisplay = (time: string) => {
    if (!time) return 'Select time'
    const [hours, minutes] = time.split(':')
    const hour = parseInt(hours)
    const ampm = hour >= 12 ? 'PM' : 'AM'
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
    return `${displayHour}:${minutes} ${ampm}`
  }

  const selectTime = (time: string) => {
    setFormData(prev => ({ ...prev, time }))
    setShowTimePicker(false)
  }

  const clearTime = () => {
    setFormData(prev => ({ ...prev, time: '' }))
    setShowTimePicker(false)
  }

  // Common time options
  const getCommonTimeOptions = () => {
    return [
      { label: '9:00 AM', value: '09:00' },
      { label: '10:00 AM', value: '10:00' },
      { label: '11:00 AM', value: '11:00' },
      { label: '12:00 PM', value: '12:00' },
      { label: '1:00 PM', value: '13:00' },
      { label: '2:00 PM', value: '14:00' },
      { label: '3:00 PM', value: '15:00' },
      { label: '4:00 PM', value: '16:00' },
      { label: '5:00 PM', value: '17:00' },
      { label: '6:00 PM', value: '18:00' }
    ]
  }

  const typeOptions = [
    { value: 'meeting', label: 'Meeting', icon: '🤝' },
    { value: 'task', label: 'Task', icon: '✅' },
    { value: 'reminder', label: 'Reminder', icon: '⏰' }
  ]

  const priorityOptions = [
    { value: 'low', label: 'Low', color: 'text-emerald-600 dark:text-emerald-400' },
    { value: 'medium', label: 'Medium', color: 'text-amber-600 dark:text-amber-400' },
    { value: 'high', label: 'High', color: 'text-stone-600 dark:text-stone-400' }
  ]

  if (!isOpen || !event) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black/40 transition-opacity duration-200"
          onClick={handleClose}
        />

      {/* Modal - Scandinavian Clean */}
      <div
        ref={modalRef}
        className={cn(
          "relative w-full max-w-lg bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 transition-all duration-200",
          "shadow-lg overflow-visible",
          isOpen ? "translate-y-0 opacity-100 scale-100" : "translate-y-4 opacity-0 scale-95"
        )}
      >
        {/* Header - Minimal */}
        <div className="p-4 border-b border-stone-200 dark:border-stone-700">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-light text-stone-800 dark:text-stone-100 uppercase tracking-wider">
              EDIT EVENT
            </h2>
            <button
              onClick={handleClose}
              className="w-6 h-6 flex items-center justify-center text-stone-400 dark:text-stone-500 hover:text-stone-600 dark:hover:text-stone-300 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Content - Compact */}
        <div className="p-4 space-y-4 overflow-visible">
          {/* Title Field */}
          <div className="space-y-1">
            <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
              TITLE
            </label>
            <input
              ref={titleInputRef}
              type="text"
              value={formData.title}
              onChange={(e) => updateField('title', e.target.value)}
              onFocus={() => setFocusedField('title')}
              onBlur={() => setFocusedField(null)}
              placeholder="Enter event title"
              className={cn(
                "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors",
                focusedField === 'title'
                  ? "border-emerald-600 dark:border-emerald-400"
                  : "hover:border-stone-300 dark:hover:border-stone-500",
                "text-stone-800 dark:text-stone-100 placeholder-stone-400 dark:placeholder-stone-500"
              )}
            />
          </div>

          {/* Description Field */}
          <div className="space-y-1">
            <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
              DESCRIPTION
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => updateField('description', e.target.value)}
              onFocus={() => setFocusedField('description')}
              onBlur={() => setFocusedField(null)}
              placeholder="Add event details (optional)"
              rows={3}
              className={cn(
                "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors resize-none",
                focusedField === 'description'
                  ? "border-emerald-600 dark:border-emerald-400"
                  : "hover:border-stone-300 dark:hover:border-stone-500",
                "text-stone-800 dark:text-stone-100 placeholder-stone-400 dark:placeholder-stone-500"
              )}
            />
          </div>

          {/* Type and Priority - Compact Grid */}
          <div className="grid grid-cols-2 gap-4">
            {/* Event Type Selector */}
            <div className="space-y-1">
              <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider flex items-center gap-1">
                <Users className="w-3 h-3" />
                TYPE
              </label>
              <div className="space-y-1">
                {typeOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => updateField('type', option.value)}
                    className={cn(
                      "w-full px-2 py-1.5 text-xs border transition-colors flex items-center gap-2",
                      formData.type === option.value
                        ? "border-emerald-600 dark:border-emerald-400 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300"
                        : "border-stone-200 dark:border-stone-600 hover:border-stone-300 dark:hover:border-stone-500 text-stone-600 dark:text-stone-400"
                    )}
                  >
                    <span>{option.icon}</span>
                    <span>{option.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Priority Selector */}
            <div className="space-y-1">
              <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider flex items-center gap-1">
                <Flag className="w-3 h-3" />
                PRIORITY
              </label>
              <div className="space-y-1">
                {priorityOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => updateField('priority', option.value)}
                    className={cn(
                      "w-full px-2 py-1.5 text-xs border transition-colors flex items-center justify-center",
                      formData.priority === option.value
                        ? "border-emerald-600 dark:border-emerald-400 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300"
                        : "border-stone-200 dark:border-stone-600 hover:border-stone-300 dark:hover:border-stone-500 text-stone-600 dark:text-stone-400"
                    )}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Date and Time - Custom Pickers */}
          <div className="grid grid-cols-2 gap-4">
            {/* Custom Date Picker - Copy from Todos */}
            <div className="space-y-1 relative">
              <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
                DATE
              </label>

              {/* Date Display Button */}
              <button
                type="button"
                onClick={() => setShowDatePicker(!showDatePicker)}
                className={cn(
                  "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors text-left flex items-center justify-between",
                  showDatePicker
                    ? "border-emerald-600 dark:border-emerald-400"
                    : "hover:border-stone-300 dark:hover:border-stone-500",
                  selectedDate
                    ? "text-stone-800 dark:text-stone-100"
                    : "text-stone-400 dark:text-stone-500"
                )}
              >
                <span>{formatDateDisplay(selectedDate)}</span>
                <Calendar className="w-3 h-3 text-stone-400 dark:text-stone-500" />
              </button>

              {/* Custom Date Picker - Mobile-Optimized Modal */}
              {showDatePicker && (
                <>
                  {/* Mobile Overlay */}
                  <div
                    className="fixed inset-0 bg-black/40 z-50 sm:hidden"
                    onClick={() => setShowDatePicker(false)}
                  />

                  {/* Date Picker Content */}
                  <div className={`
                    sm:absolute sm:top-full sm:left-0 sm:right-0 sm:mt-1 sm:z-10
                    fixed bottom-0 left-0 right-0 z-50 sm:relative sm:bottom-auto
                    bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 shadow-lg
                    sm:rounded-none rounded-t-lg
                    max-h-[70vh] overflow-y-auto
                  `}>
                  <div className="p-3">
                    {/* Current Selection Display */}
                    {selectedDate && (
                      <div className="mb-3 pb-3 border-b border-stone-200 dark:border-stone-600">
                        <div className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider mb-1">
                          SELECTED
                        </div>
                        <div className="text-sm text-stone-800 dark:text-stone-100 font-medium">
                          {formatDateDisplay(selectedDate)}
                        </div>
                      </div>
                    )}

                    {/* Quick Date Options - Fixed Grid */}
                    <div className="mb-3">
                      <div className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider mb-2">
                        QUICK SELECT
                      </div>
                      <div className="grid grid-cols-1 gap-2">
                        {getQuickDateOptions().map((option) => (
                          <button
                            key={option.label}
                            type="button"
                            onClick={() => handleDateSelect(option.date)}
                            className={cn(
                              "w-full px-3 py-2 text-sm border transition-colors text-left",
                              selectedDate && selectedDate.toDateString() === option.date.toDateString()
                                ? "border-emerald-600 dark:border-emerald-400 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300"
                                : "border-stone-200 dark:border-stone-600 hover:border-emerald-600 dark:hover:border-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 text-stone-700 dark:text-stone-300"
                            )}
                          >
                            <div className="flex items-center justify-between">
                              <span>{option.label}</span>
                              <span className="text-xs text-stone-500 dark:text-stone-400">
                                {option.date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                              </span>
                            </div>
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* Calendar Toggle */}
                    <div className="pt-2 border-t border-stone-200 dark:border-stone-600">
                      <button
                        type="button"
                        onClick={() => setShowCalendar(!showCalendar)}
                        className="w-full px-3 py-2 text-sm text-stone-600 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200 transition-colors text-center flex items-center justify-center gap-2"
                      >
                        <Calendar className="w-3 h-3" />
                        {showCalendar ? 'Hide Calendar' : 'Show Calendar'}
                      </button>
                    </div>

                    {/* Full Calendar Interface */}
                    {showCalendar && (
                      <div className="pt-3 border-t border-stone-200 dark:border-stone-600">
                        {/* Calendar Header */}
                        <div className="flex items-center justify-between mb-3">
                          <button
                            type="button"
                            onClick={() => navigateMonth('prev')}
                            className="p-1 hover:bg-stone-100 dark:hover:bg-stone-600 transition-colors"
                          >
                            <ChevronLeft className="w-4 h-4 text-stone-600 dark:text-stone-400" />
                          </button>

                          <div className="text-sm font-medium text-stone-800 dark:text-stone-100">
                            {calendarDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                          </div>

                          <button
                            type="button"
                            onClick={() => navigateMonth('next')}
                            className="p-1 hover:bg-stone-100 dark:hover:bg-stone-600 transition-colors"
                          >
                            <ChevronRight className="w-4 h-4 text-stone-600 dark:text-stone-400" />
                          </button>
                        </div>

                        {/* Calendar Grid */}
                        <div className="grid grid-cols-7 gap-1 mb-2">
                          {/* Day headers */}
                          {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
                            <div key={day} className="text-xs text-stone-500 dark:text-stone-400 text-center py-1">
                              {day}
                            </div>
                          ))}

                          {/* Calendar days */}
                          {generateCalendarDays().map((day, index) => (
                            <button
                              key={index}
                              type="button"
                              onClick={() => day && selectCalendarDate(day)}
                              disabled={!day}
                              className={cn(
                                "text-xs py-1.5 transition-colors",
                                !day && "invisible",
                                day && "hover:bg-emerald-50 dark:hover:bg-emerald-900/20",
                                isToday(day || 0) && "bg-stone-100 dark:bg-stone-600 font-medium",
                                isSelectedDate(day || 0) && "bg-emerald-600 text-white hover:bg-emerald-700",
                                day && !isSelectedDate(day) && !isToday(day) && "text-stone-700 dark:text-stone-300"
                              )}
                            >
                              {day}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Clear Date Option */}
                    <div className="pt-2 border-t border-stone-200 dark:border-stone-600">
                      <button
                        type="button"
                        onClick={clearDate}
                        className="w-full px-3 py-2 text-sm text-stone-500 dark:text-stone-400 hover:text-stone-700 dark:hover:text-stone-200 transition-colors text-center"
                      >
                        Clear Date
                      </button>
                    </div>
                  </div>
                  </div>
                </>
              )}
            </div>

            {/* Simple Time Picker */}
            <div className="space-y-1 relative">
              <label className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider">
                TIME
              </label>

              {/* Time Display Button */}
              <button
                type="button"
                onClick={() => setShowTimePicker(!showTimePicker)}
                className={cn(
                  "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors text-left flex items-center justify-between",
                  showTimePicker
                    ? "border-emerald-600 dark:border-emerald-400"
                    : "hover:border-stone-300 dark:hover:border-stone-500",
                  formData.time
                    ? "text-stone-800 dark:text-stone-100"
                    : "text-stone-400 dark:text-stone-500"
                )}
              >
                <span>{formatTimeDisplay(formData.time)}</span>
                <Clock className="w-3 h-3 text-stone-400 dark:text-stone-500" />
              </button>

              {/* Simple Time Picker */}
              {showTimePicker && (
                <>
                  {/* Mobile Overlay */}
                  <div
                    className="fixed inset-0 bg-black/40 z-50 sm:hidden"
                    onClick={() => setShowTimePicker(false)}
                  />

                  {/* Time Picker Content */}
                  <div className={`
                    sm:absolute sm:top-full sm:left-0 sm:right-0 sm:mt-1 sm:z-10
                    fixed bottom-0 left-0 right-0 z-50 sm:relative sm:bottom-auto
                    bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 shadow-lg
                    sm:rounded-none rounded-t-lg
                  `}>
                    <div className="p-3">
                      {/* Current Selection */}
                      {formData.time && (
                        <div className="mb-3 pb-3 border-b border-stone-200 dark:border-stone-600">
                          <div className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider mb-1">
                            SELECTED
                          </div>
                          <div className="text-sm text-stone-800 dark:text-stone-100 font-medium">
                            {formatTimeDisplay(formData.time)}
                          </div>
                        </div>
                      )}

                      {/* Common Time Options */}
                      <div className="mb-3">
                        <div className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider mb-2">
                          COMMON TIMES
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          {getCommonTimeOptions().map((option) => (
                            <button
                              key={option.value}
                              type="button"
                              onClick={() => selectTime(option.value)}
                              className={cn(
                                "px-3 py-2 text-sm border transition-colors text-left",
                                formData.time === option.value
                                  ? "border-emerald-600 dark:border-emerald-400 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300"
                                  : "border-stone-200 dark:border-stone-600 hover:border-emerald-600 dark:hover:border-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 text-stone-700 dark:text-stone-300"
                              )}
                            >
                              {option.label}
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Custom Time Input */}
                      <div className="pt-2 border-t border-stone-200 dark:border-stone-600">
                        <div className="text-xs font-normal text-stone-600 dark:text-stone-400 uppercase tracking-wider mb-2">
                          CUSTOM TIME
                        </div>
                        <input
                          type="time"
                          value={formData.time}
                          onChange={(e) => selectTime(e.target.value)}
                          className={cn(
                            "w-full px-3 py-2 text-sm bg-white dark:bg-stone-700 border border-stone-200 dark:border-stone-600 transition-colors",
                            "hover:border-emerald-600 dark:hover:border-emerald-400 focus:border-emerald-600 dark:focus:border-emerald-400 focus:outline-none",
                            "text-stone-800 dark:text-stone-100 font-normal",
                            // 自定义原生time input的样式
                            "[&::-webkit-datetime-edit]:text-stone-800 [&::-webkit-datetime-edit]:dark:text-stone-100",
                            "[&::-webkit-datetime-edit-fields-wrapper]:text-stone-800 [&::-webkit-datetime-edit-fields-wrapper]:dark:text-stone-100",
                            "[&::-webkit-datetime-edit-hour-field]:text-stone-800 [&::-webkit-datetime-edit-hour-field]:dark:text-stone-100",
                            "[&::-webkit-datetime-edit-minute-field]:text-stone-800 [&::-webkit-datetime-edit-minute-field]:dark:text-stone-100",
                            "[&::-webkit-datetime-edit-ampm-field]:text-stone-800 [&::-webkit-datetime-edit-ampm-field]:dark:text-stone-100",
                            // 时钟图标样式 - 适配浅色/深色模式
                            "[&::-webkit-calendar-picker-indicator]:opacity-100 [&::-webkit-calendar-picker-indicator]:cursor-pointer",
                            // 浅色模式下反色让图标变深色，深色模式下不反色保持浅色
                            "[&::-webkit-calendar-picker-indicator]:brightness-0 dark:[&::-webkit-calendar-picker-indicator]:brightness-100"
                          )}
                          style={{
                            // 根据当前主题设置colorScheme
                            colorScheme: theme === 'dark' ? 'dark' : 'light'
                          }}
                        />
                        <div className="text-xs text-stone-500 dark:text-stone-400 mt-1">
                          Type time manually (e.g., 14:30) or click the clock icon
                        </div>
                      </div>

                      {/* Clear Time Option */}
                      <div className="pt-2 border-t border-stone-200 dark:border-stone-600 mt-3">
                        <button
                          type="button"
                          onClick={clearTime}
                          className="w-full px-3 py-2 text-sm text-stone-500 dark:text-stone-400 hover:text-stone-700 dark:hover:text-stone-200 transition-colors text-center"
                        >
                          Clear Time
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Footer - Minimal */}
        <div className="p-4 border-t border-stone-200 dark:border-stone-700">
          <div className="flex gap-2">
            <Button
              variant="ghost"
              onClick={handleClose}
              className="flex-1 px-3 py-2 text-sm border border-stone-300 dark:border-stone-600 hover:bg-stone-100 dark:hover:bg-stone-600"
            >
              Cancel
            </Button>
            <Button
              variant="primary"
              onClick={handleSave}
              disabled={!formData.title.trim() || isLoading}
              className={cn(
                "flex-1 px-3 py-2 text-sm bg-emerald-600 hover:bg-emerald-700 dark:bg-emerald-500 dark:hover:bg-emerald-600 text-white",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                "flex items-center justify-center gap-1"
              )}
            >
              {isLoading ? (
                <>
                  <div className="w-3 h-3 border border-white/30 border-t-white rounded-full animate-spin" />
                  Saving
                </>
              ) : (
                <>
                  <Save className="w-3 h-3" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Custom Confirmation Dialog - Scandinavian Clean */}
        {showConfirmDialog && (
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 shadow-lg max-w-xs w-full">
              <div className="p-4">
                <h3 className="text-sm font-light text-stone-800 dark:text-stone-100 uppercase tracking-wider mb-2">
                  UNSAVED CHANGES
                </h3>
                <p className="text-xs text-stone-600 dark:text-stone-400 mb-4">
                  You have unsaved changes. Are you sure you want to close?
                </p>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    onClick={handleCancelClose}
                    className="flex-1 px-2 py-1.5 text-xs border border-stone-300 dark:border-stone-600 hover:bg-stone-100 dark:hover:bg-stone-600"
                  >
                    Keep Editing
                  </Button>
                  <Button
                    variant="primary"
                    onClick={handleConfirmClose}
                    className="flex-1 px-2 py-1.5 text-xs bg-amber-600 hover:bg-amber-700 dark:bg-amber-500 dark:hover:bg-amber-600 text-white"
                  >
                    Discard
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default EditEventModal
