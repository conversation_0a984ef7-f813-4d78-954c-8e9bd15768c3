import React, { useState, useEffect } from 'react'
import { Wifi, WifiOff } from 'lucide-react'
import { useAppStore } from '@/store/appStore'
import { cn } from '@/lib/utils'

const OfflineIndicator: React.FC = () => {
  const { isOnline, setOnline, offlineQueue } = useAppStore()
  const [showIndicator, setShowIndicator] = useState(false)
  const [justWentOnline, setJustWentOnline] = useState(false)

  useEffect(() => {
    const handleOnline = () => {
      setOnline(true)
      setJustWentOnline(true)
      setShowIndicator(true)
      
      // Hide indicator after showing "back online" message
      setTimeout(() => {
        setShowIndicator(false)
        setJustWentOnline(false)
      }, 3000)
    }

    const handleOffline = () => {
      setOnline(false)
      setShowIndicator(true)
      setJustWentOnline(false)
    }

    // Set initial state
    setOnline(navigator.onLine)
    if (!navigator.onLine) {
      setShowIndicator(true)
    }

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [setOnline])

  // Don't show indicator if online and not just went online
  if (isOnline && !justWentOnline) {
    return null
  }

  // Don't show if we haven't determined to show it
  if (!showIndicator) {
    return null
  }

  return (
    <div className={cn(
      'fixed top-16 left-1/2 transform -translate-x-1/2 z-50',
      'px-4 py-2 rounded-full shadow-lg transition-all duration-300',
      'flex items-center gap-2 text-sm font-medium',
      isOnline 
        ? 'bg-green-500 text-white animate-fade-in'
        : 'bg-yellow-500 text-white'
    )}>
      {isOnline ? (
        <>
          <Wifi className="w-4 h-4" />
          <span>Back online</span>
          {offlineQueue.length > 0 && (
            <span className="text-xs bg-white/20 px-2 py-0.5 rounded-full">
              Syncing {offlineQueue.length} items
            </span>
          )}
        </>
      ) : (
        <>
          <WifiOff className="w-4 h-4" />
          <span>Working offline</span>
          {offlineQueue.length > 0 && (
            <span className="text-xs bg-white/20 px-2 py-0.5 rounded-full">
              {offlineQueue.length} pending
            </span>
          )}
        </>
      )}
    </div>
  )
}

export default OfflineIndicator
