import React, { useState, useEffect } from 'react'
import { Download, X, Smartphone } from 'lucide-react'
import { cn } from '@/lib/utils'
import Button from '../UI/Button'

interface BeforeInstallPromptEvent extends Event {
  prompt(): Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

const InstallPrompt: React.FC = () => {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null)
  const [showPrompt, setShowPrompt] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)

  useEffect(() => {
    // Check if app is already installed
    const checkInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches
      const isInWebAppiOS = (window.navigator as any).standalone === true
      setIsInstalled(isStandalone || isInWebAppiOS)
    }

    checkInstalled()

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e as BeforeInstallPromptEvent)
      
      // Show prompt after a delay if not already installed
      if (!isInstalled) {
        setTimeout(() => setShowPrompt(true), 3000)
      }
    }

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setShowPrompt(false)
      setDeferredPrompt(null)
      console.log('PWA was installed')
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [isInstalled])

  const handleInstallClick = async () => {
    if (!deferredPrompt) return

    try {
      await deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt')
      } else {
        console.log('User dismissed the install prompt')
      }
      
      setDeferredPrompt(null)
      setShowPrompt(false)
    } catch (error) {
      console.error('Error during installation:', error)
    }
  }

  const handleDismiss = () => {
    setShowPrompt(false)
    // Don't show again for this session
    sessionStorage.setItem('installPromptDismissed', 'true')
  }

  // Don't show if already installed or dismissed this session
  if (isInstalled || !showPrompt || !deferredPrompt) {
    return null
  }

  // Check if dismissed this session
  if (sessionStorage.getItem('installPromptDismissed')) {
    return null
  }

  return (
    <div className="fixed bottom-20 left-4 right-4 z-40 max-w-mobile mx-auto">
      <div className={cn(
        'bg-bg-secondary border border-border-primary rounded-2xl shadow-2xl p-4',
        'animate-slide-up'
      )}>
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 w-10 h-10 bg-accent-primary/10 rounded-full flex items-center justify-center">
            <Smartphone className="w-5 h-5 text-accent-primary" />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-text-primary mb-1">
              Install Synapse
            </h3>
            <p className="text-sm text-text-secondary mb-3">
              Add to your home screen for quick access and offline use
            </p>
            
            <div className="flex items-center gap-2">
              <Button
                variant="primary"
                size="sm"
                onClick={handleInstallClick}
                className="flex-1"
              >
                <Download className="w-4 h-4 mr-1" />
                Install
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="px-3"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
        
        {/* Features */}
        <div className="mt-3 pt-3 border-t border-border-primary">
          <div className="grid grid-cols-3 gap-2 text-xs text-text-secondary">
            <div className="text-center">
              <div className="text-accent-primary mb-1">⚡</div>
              <div>Fast Access</div>
            </div>
            <div className="text-center">
              <div className="text-accent-success mb-1">📱</div>
              <div>Offline Ready</div>
            </div>
            <div className="text-center">
              <div className="text-accent-purple mb-1">🔄</div>
              <div>Auto Sync</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InstallPrompt
