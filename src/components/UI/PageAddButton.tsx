import React from 'react'
import { Plus } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PageAddButtonProps {
  onClick: () => void
  children: React.ReactNode
  className?: string
  disabled?: boolean
}

const PageAddButton: React.FC<PageAddButtonProps> = ({
  onClick,
  children,
  className,
  disabled = false
}) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        // Base styles
        'group relative flex items-center gap-2.5 px-4 py-2.5',
        'font-normal text-sm uppercase tracking-wider',
        'transition-all duration-300 ease-out',
        'focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed',
        
        // Scandinavian Clean styling
        'bg-gradient-to-r from-emerald-700 to-emerald-600',
        'dark:from-emerald-600 dark:to-emerald-500',
        'text-white',
        'border border-emerald-600/20 dark:border-emerald-500/20',
        'shadow-lg shadow-emerald-700/25 dark:shadow-emerald-600/25',
        
        // Hover effects
        'hover:from-emerald-800 hover:to-emerald-700',
        'dark:hover:from-emerald-700 dark:hover:to-emerald-600',
        'hover:shadow-xl hover:shadow-emerald-700/30 dark:hover:shadow-emerald-600/30',
        'hover:-translate-y-0.5',
        
        // Active state
        'active:translate-y-0 active:shadow-md',
        
        className
      )}
    >
      {/* Background glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-emerald-600/0 via-emerald-500/20 to-emerald-600/0 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
      
      {/* Icon with animation */}
      <div className="relative z-10 flex items-center justify-center">
        <Plus className={cn(
          'w-4 h-4 transition-transform duration-300',
          'group-hover:rotate-90 group-hover:scale-110',
          'drop-shadow-sm'
        )} />
      </div>
      
      {/* Text */}
      <span className="relative z-10 drop-shadow-sm">
        {children}
      </span>
      
      {/* Subtle inner highlight */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/10 to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300" />
    </button>
  )
}

export default PageAddButton
