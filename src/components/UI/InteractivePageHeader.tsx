import React from 'react'
import { Plus } from 'lucide-react'
import { cn } from '@/lib/utils'

interface InteractivePageHeaderProps {
  title: string
  description?: string // 可选，某些页面可能不需要
  onAddClick: () => void
  addLabel: string // "Add Note", "Add Todo", "Add Event"
  rightContent?: React.ReactNode // 右侧内容区域
  className?: string
}

const InteractivePageHeader: React.FC<InteractivePageHeaderProps> = ({
  title,
  description,
  onAddClick,
  addLabel,
  rightContent,
  className
}) => {
  return (
    <div className={cn("flex items-start justify-between gap-4", className)}>
      {/* Left: Interactive Title Section */}
      <div className="flex-1 min-w-0">
        <button
          onClick={onAddClick}
          className={cn(
            'group w-full text-left p-2 -m-2 rounded-lg transition-all duration-300',
            'hover:bg-stone-50 dark:hover:bg-stone-800/50',
            'focus:outline-none focus:ring-2 focus:ring-emerald-500/30 focus:ring-offset-2',
            'active:scale-[0.98]'
          )}
        >
          <div className="flex items-center gap-3">
            <h2 className={cn(
              'text-2xl font-light text-stone-800 dark:text-stone-100',
              'transition-colors duration-300',
              'group-hover:text-emerald-700 dark:group-hover:text-emerald-400'
            )}>
              {title}
            </h2>

            {/* Plus Icon - 移动端始终可见，桌面端悬停显示 */}
            <div className={cn(
              'flex items-center justify-center w-6 h-6 rounded-full',
              'bg-emerald-700 dark:bg-emerald-600 text-white',
              'transition-all duration-300 shadow-sm',
              // 移动端始终显示
              'opacity-100 scale-100',
              // 桌面端悬停效果
              'sm:opacity-60 sm:scale-90',
              'sm:group-hover:opacity-100 sm:group-hover:scale-100',
              // 点击动画
              'group-active:rotate-90 group-active:scale-110'
            )}>
              <Plus className={cn(
                'w-3.5 h-3.5 transition-transform duration-300',
                'group-hover:rotate-90',
                'group-active:rotate-180'
              )} />
            </div>
          </div>

          {description && (
            <p className={cn(
              'text-stone-500 dark:text-stone-400 font-normal mt-1',
              'transition-colors duration-300',
              'group-hover:text-stone-600 dark:group-hover:text-stone-300'
            )}>
              {description}
            </p>
          )}
        </button>
      </div>

      {/* Right: Custom Content Area */}
      {rightContent && (
        <div className="flex-shrink-0 self-start">
          {rightContent}
        </div>
      )}
    </div>
  )
}

export default InteractivePageHeader
