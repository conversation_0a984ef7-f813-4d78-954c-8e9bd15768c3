import React from 'react'
import Header from './Header'
import TabBar from './TabBar'
import PageTransition from './PageTransition'
import InstallPrompt from '../PWA/InstallPrompt'
import OfflineIndicator from '../PWA/OfflineIndicator'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="mobile-container flex flex-col">
      <Header />
      <OfflineIndicator />

      <main className="flex-1 overflow-hidden">
        <PageTransition>
          <div className="p-4 pb-24">
            {children}
          </div>
        </PageTransition>
      </main>

      <InstallPrompt />
      <TabBar />
    </div>
  )
}

export default Layout
