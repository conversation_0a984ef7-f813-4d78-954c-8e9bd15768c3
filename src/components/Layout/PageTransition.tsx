import React, { useEffect, useRef, useLayoutEffect } from 'react'
import { useLocation } from 'react-router-dom'

interface PageTransitionProps {
  children: React.ReactNode
}

// Global scroll position storage with better key management
const scrollPositions = new Map<string, number>()

// Expose scroll positions globally for tab bar access
;(window as any).pageScrollPositions = scrollPositions

// Mobile-specific debugging
const isMobile = () => window.innerWidth <= 768
const logMobile = (message: string, data?: any) => {
  if (isMobile()) {
    console.log(`📱 [MOBILE] ${message}`, data || '')
  } else {
    console.log(`🖥️ [DESKTOP] ${message}`, data || '')
  }
}

const PageTransition: React.FC<PageTransitionProps> = ({ children }) => {
  const location = useLocation()
  const containerRef = useRef<HTMLDivElement>(null)
  const previousLocationRef = useRef<string>(location.pathname)
  const isInitialMount = useRef(true)
  const restoreTimeoutRef = useRef<NodeJS.Timeout>()

  // Enhanced scroll container detection for mobile
  const getScrollContainer = () => {
    if (containerRef.current) {
      logMobile(`Container found via ref: scrollTop=${containerRef.current.scrollTop}`)
      return containerRef.current
    }

    // Fallback selectors for mobile compatibility
    const selectors = [
      'main .relative.w-full.h-full.overflow-y-auto',
      'main > div > div', // PageTransition container
      'main',
      '[data-scroll-container]'
    ]

    for (const selector of selectors) {
      const element = document.querySelector(selector) as HTMLElement
      if (element && element.scrollHeight > element.clientHeight) {
        logMobile(`Container found via selector "${selector}": scrollTop=${element.scrollTop}`)
        return element
      }
    }

    logMobile('No scroll container found!')
    return null
  }

  // Save scroll position immediately when location changes (before unmount)
  useLayoutEffect(() => {
    const currentPath = previousLocationRef.current

    // Save current scroll position before navigation
    if (!isInitialMount.current) {
      const container = getScrollContainer()
      if (container) {
        const scrollTop = container.scrollTop
        scrollPositions.set(currentPath, scrollTop)
        logMobile(`💾 Saved scroll position for ${currentPath}: ${scrollTop}`)
      }
    }

    // Update the previous location reference
    previousLocationRef.current = location.pathname
    isInitialMount.current = false
  }, [location.pathname])

  // Restore scroll position after content is rendered with mobile-optimized timing
  useLayoutEffect(() => {
    const savedPosition = scrollPositions.get(location.pathname) || 0

    // Clear any pending restore timeout
    if (restoreTimeoutRef.current) {
      clearTimeout(restoreTimeoutRef.current)
    }

    // Multi-stage restoration for mobile compatibility
    const restorePosition = (attempt = 1) => {
      const container = getScrollContainer()
      if (container) {
        // For mobile, use scrollTo with instant behavior
        if (isMobile()) {
          container.scrollTo({
            top: savedPosition,
            behavior: 'instant'
          })
        } else {
          container.scrollTop = savedPosition
        }

        logMobile(`📍 Restored scroll position for ${location.pathname}: ${savedPosition} (attempt ${attempt})`)

        // Verify restoration worked
        setTimeout(() => {
          const actualPosition = container.scrollTop
          if (Math.abs(actualPosition - savedPosition) > 5 && attempt < 3) {
            logMobile(`🔄 Position mismatch (expected: ${savedPosition}, actual: ${actualPosition}), retrying...`)
            restorePosition(attempt + 1)
          }
        }, 50)
      } else if (attempt < 5) {
        // Retry if container not found (common on mobile during transitions)
        restoreTimeoutRef.current = setTimeout(() => restorePosition(attempt + 1), 50)
      }
    }

    // Initial restoration attempt
    requestAnimationFrame(() => {
      restorePosition()
    })

    // Cleanup timeout on unmount
    return () => {
      if (restoreTimeoutRef.current) {
        clearTimeout(restoreTimeoutRef.current)
      }
    }
  }, [location.pathname, children])

  // Continuously save scroll position while scrolling with mobile optimization
  useEffect(() => {
    let scrollTimeout: NodeJS.Timeout

    const handleScroll = () => {
      const container = getScrollContainer()
      if (!container) return

      // Mobile-optimized debouncing (faster for touch scrolling)
      const debounceTime = isMobile() ? 50 : 100

      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        const scrollTop = container.scrollTop
        scrollPositions.set(location.pathname, scrollTop)
        // Uncomment for detailed scroll debugging
        // logMobile(`📜 Updated scroll position for ${location.pathname}: ${scrollTop}`)
      }, debounceTime)
    }

    const handleBeforeUnload = () => {
      const container = getScrollContainer()
      if (container) {
        const scrollTop = container.scrollTop
        scrollPositions.set(location.pathname, scrollTop)
        logMobile(`💾 Final save on unload for ${location.pathname}: ${scrollTop}`)
      }
    }

    // Add scroll listener to the container
    const container = getScrollContainer()
    if (container) {
      container.addEventListener('scroll', handleScroll, { passive: true })
      logMobile(`🎯 Scroll listener attached to container for ${location.pathname}`)
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      clearTimeout(scrollTimeout)
      if (container) {
        container.removeEventListener('scroll', handleScroll)
      }
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [location.pathname])

  return (
    <div
      ref={containerRef}
      data-scroll-container="true"
      className="relative w-full h-full overflow-y-auto custom-scrollbar"
      style={{
        // Ensure smooth scrolling behavior
        scrollBehavior: 'auto', // Prevent interference with position restoration
        // Mobile-specific optimizations
        WebkitOverflowScrolling: 'touch', // iOS momentum scrolling
        overscrollBehavior: 'contain' // Prevent scroll chaining
      }}
    >
      {/* Content with crossfade animation */}
      <div
        key={location.pathname} // Force re-render on route change for clean transitions
        className="w-full min-h-full animate-in fade-in duration-300"
        style={{
          animationFillMode: 'both',
          animationTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)'
        }}
      >
        {children}
      </div>
    </div>
  )
}

export default PageTransition
