import React, { useState, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Home, Calendar, CheckSquare, StickyNote, Sparkles } from 'lucide-react'
import { useAppStore } from '@/store/appStore'
import { cn } from '@/lib/utils'
import MagicInputPanel from '../MagicInput/MagicInputPanel'

const TabBar: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { setActiveTab, openMagicInputWithContext, isLoading, magicInputOpen, setMagicInputOpen } = useAppStore()
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [isClosing, setIsClosing] = useState(false)

  useEffect(() => {
    let scrollContainer: HTMLElement | null = null

    const handleScroll = () => {
      if (!scrollContainer) return

      const currentScrollY = scrollContainer.scrollTop
      const scrollDifference = Math.abs(currentScrollY - lastScrollY)

      // Only process significant scroll changes to avoid jitter
      if (scrollDifference < 5) return

      if (currentScrollY <= 10) {
        // Always show at top
        setIsVisible(true)
      } else if (currentScrollY > lastScrollY && currentScrollY > 80) {
        // Scrolling down & past threshold - hide
        setIsVisible(false)
      } else if (currentScrollY < lastScrollY) {
        // Scrolling up - show
        setIsVisible(true)
      }

      setLastScrollY(currentScrollY)
    }

    // Find the scroll container with retry logic
    const findScrollContainer = () => {
      const selectors = [
        '[data-scroll-container="true"]',
        'main .relative.w-full.h-full.overflow-y-auto',
        'main > div > div'
      ]

      for (const selector of selectors) {
        const element = document.querySelector(selector) as HTMLElement
        if (element && element.scrollHeight > element.clientHeight) {
          return element
        }
      }
      return null
    }

    // Try to find scroll container with delay for DOM readiness
    const initializeScrollListener = () => {
      scrollContainer = findScrollContainer()

      if (scrollContainer) {
        scrollContainer.addEventListener('scroll', handleScroll, { passive: true })
      } else {
        // Retry after a short delay if container not found
        setTimeout(initializeScrollListener, 100)
      }
    }

    initializeScrollListener()

    return () => {
      if (scrollContainer) {
        scrollContainer.removeEventListener('scroll', handleScroll)
      }
    }
  }, [lastScrollY])

  const tabs = [
    {
      id: 'dashboard' as const,
      label: 'Dashboard',
      icon: Home,
      path: '/'
    },
    {
      id: 'notes' as const,
      label: 'Notes',
      icon: StickyNote,
      path: '/notes'
    },
    {
      id: 'magic' as const,
      label: 'Magic Input',
      icon: Sparkles,
      path: null, // Special handling for Magic Input
      isMagic: true
    },
    {
      id: 'calendar' as const,
      label: 'Calendar',
      icon: Calendar,
      path: '/calendar'
    },
    {
      id: 'todos' as const,
      label: 'Todos',
      icon: CheckSquare,
      path: '/todos'
    }
  ]

  const handleTabClick = (tab: typeof tabs[0]) => {
    if (tab.isMagic) {
      // Handle Magic Input button
      openMagicInputWithContext('general')
    } else {
      const isCurrentlyActive = isActive(tab.path)

      if (isCurrentlyActive) {
        // If clicking on the currently active tab, scroll to top with visual feedback
        // Enhanced container detection for mobile compatibility
        const getScrollContainer = () => {
          const selectors = [
            '[data-scroll-container="true"]',
            'main .relative.w-full.h-full.overflow-y-auto',
            'main > div > div',
            'main'
          ]

          for (const selector of selectors) {
            const element = document.querySelector(selector) as HTMLElement
            if (element && element.scrollHeight > element.clientHeight) {
              return element
            }
          }
          return null
        }

        const mainContainer = getScrollContainer()
        if (mainContainer) {
          // Add visual feedback to the clicked tab
          const tabElement = document.querySelector(`button[aria-label="${tab.label}"]`)
          if (tabElement) {
            tabElement.classList.add('scroll-to-top-pulse')
            setTimeout(() => {
              tabElement.classList.remove('scroll-to-top-pulse')
            }, 300)
          }

          // Smooth scroll to top and update stored position
          mainContainer.scrollTo({
            top: 0,
            behavior: 'smooth'
          })

          // Update the stored scroll position to 0 after scrolling
          setTimeout(() => {
            // Access the scroll positions map from PageTransition
            const scrollPositions = (window as any).pageScrollPositions
            if (scrollPositions) {
              scrollPositions.set(location.pathname, 0)
              console.log(`📱 Updated scroll position to 0 for ${location.pathname} via tab click`)
            }
          }, 500) // Wait for scroll animation to complete
        }
      } else {
        // Navigate to the new tab
        setActiveTab(tab.id)
        navigate(tab.path!)
      }
    }
  }

  const isActive = (path: string | null) => {
    if (path === null) return false // Magic Input button is never "active"
    return location.pathname === path || (location.pathname === '/dashboard' && path === '/')
  }

  const handleBackdropClick = () => {
    // Handle closing with animation
    setIsClosing(true)
    setTimeout(() => {
      setMagicInputOpen(false)
      setIsClosing(false)
    }, 300) // Match animation duration
  }

  return (
    <>
      {/* Backdrop */}
      {magicInputOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 animate-fade-in"
          onClick={handleBackdropClick}
        />
      )}

      {/* Magic Input Panel */}
      <MagicInputPanel isClosing={isClosing} onClose={handleBackdropClick} />

      <nav className={cn(
        "fixed bottom-0 left-1/2 w-full max-w-md transition-transform duration-300 safe-bottom z-40",
        "border-t border-stone-200 dark:border-stone-700 bg-white dark:bg-stone-800 shadow-lg"
      )}
      style={{
        transform: `translateX(-50%) ${isVisible ? 'translateY(0)' : 'translateY(100%)'}`
      }}>
        <div className="flex items-center justify-around py-2">
          {tabs.map((tab) => {
            const Icon = tab.icon
            const active = isActive(tab.path)

            // Special styling for Magic Input button - Scandinavian Clean
            if (tab.isMagic) {
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabClick(tab)}
                  className={cn(
                    'relative flex items-center justify-center',
                    'w-12 h-12 transition-all duration-300 ease-out',
                    'bg-emerald-700 dark:bg-emerald-600',
                    'hover:bg-emerald-800 dark:hover:bg-emerald-700 hover:scale-110',
                    'active:scale-95 transform-gpu',
                    'mx-2' // Add horizontal margin to separate from regular tabs
                  )}
                  aria-label={tab.label}
                >
                  <Icon className="w-6 h-6 text-white" />

                  {/* AI Processing Ring */}
                  {isLoading && (
                    <div className="absolute inset-0 border-2 border-white border-t-transparent animate-spin" />
                  )}
                </button>
              )
            }

            // Regular tab styling - Scandinavian Clean typography-as-icon with animations
            return (
              <button
                key={tab.id}
                onClick={() => handleTabClick(tab)}
                className={cn(
                  'flex flex-col items-center justify-center p-2 min-w-0 flex-1',
                  'transition-all duration-200 ease-out',
                  'active:scale-95 hover:scale-105',
                  'transform-gpu', // Enable hardware acceleration
                  active
                    ? 'text-emerald-700 dark:text-emerald-600'
                    : 'text-stone-500 dark:text-stone-400 hover:text-stone-700 dark:hover:text-stone-300'
                )}
                aria-label={tab.label}
              >
                <Icon className={cn(
                  'w-5 h-5 mb-1 transition-transform duration-200 ease-out',
                  'group-active:scale-90', // Icon scales down on click
                  active && 'animate-pulse' // Subtle pulse for active tab
                )} />
                <span className="text-xs font-normal uppercase tracking-wider truncate">
                  {tab.id.toUpperCase()}
                </span>
              </button>
            )
          })}
        </div>
      </nav>
    </>
  )
}

export default TabBar
