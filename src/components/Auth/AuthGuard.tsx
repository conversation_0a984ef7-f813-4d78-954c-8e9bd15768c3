import React, { useEffect, useState } from 'react'
import { useAuth, useUser } from '@clerk/clerk-react'
import { useAppStore } from '@/store/appStore'
import { useAPI } from '@/services/api'
import { DEV_CONFIG } from '@/config/development'
import { env } from '@/config/environment'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import AuthPage from '@/pages/AuthPage'

interface AuthGuardProps {
  children: React.ReactNode
}

/**
 * AuthGuard component that handles authentication state management
 * and protects routes that require authentication
 */
const AuthGuard: React.FC<AuthGuardProps> = ({ children }) => {
  const { setUser, setAuthenticated, initializeApp, isAuthenticated } = useAppStore()
  const { getEntries } = useAPI()
  const [isInitialized, setIsInitialized] = useState(false)
  const [authError, setAuthError] = useState<string | null>(null)

  // Only use Clerk hooks when authentication is enabled
  const authData = env.clerk.enableAuth ? useAuth() : null
  const userData = env.clerk.enableAuth ? useUser() : null

  const { isLoaded, isSignedIn, getToken } = authData || { 
    isLoaded: true, 
    isSignedIn: false, 
    getToken: null 
  }
  const { user } = userData || { user: null }

  // Handle authentication state changes
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Development mode: skip authentication
        if (DEV_CONFIG.SKIP_AUTH) {
          console.log('🚀 Development mode: Skipping authentication')
          setUser(DEV_CONFIG.MOCK_USER)
          setAuthenticated(true)
          initializeApp()

          // Initialize data in development mode
          try {
            console.log('📊 Initializing data in development mode...')
            await getEntries()
            console.log('✅ Initial data loaded successfully')
          } catch (error) {
            console.error('❌ Failed to load initial data:', error)
            setAuthError('Failed to load initial data')
          }

          setIsInitialized(true)
          return
        }

        // Production mode: use Clerk authentication
        if (isLoaded) {
          if (isSignedIn && user) {
            console.log('✅ User authenticated:', user.id)
            
            // Set user data in store
            setUser({
              id: user.id,
              email: user.primaryEmailAddress?.emailAddress || '',
              firstName: user.firstName || '',
              lastName: user.lastName || '',
              imageUrl: user.imageUrl
            })
            
            setAuthenticated(true)

            // Verify token is available
            if (getToken) {
              try {
                const token = await getToken()
                if (token) {
                  console.log('🔑 Authentication token verified')
                } else {
                  console.warn('⚠️ No authentication token available')
                }
              } catch (error) {
                console.error('❌ Token verification failed:', error)
                setAuthError('Failed to verify authentication token')
                setIsInitialized(true)
                return
              }
            }

            initializeApp()

            // Initialize data after successful authentication
            try {
              console.log('📊 Initializing data after authentication...')
              await getEntries()
              console.log('✅ Initial data loaded successfully')
            } catch (error) {
              console.error('❌ Failed to load initial data:', error)
              setAuthError('Failed to load initial data')
            }
          } else {
            console.log('❌ User not authenticated')
            setUser(null)
            setAuthenticated(false)
          }
          
          setIsInitialized(true)
        }
      } catch (error) {
        console.error('❌ Authentication initialization failed:', error)
        setAuthError('Authentication initialization failed')
        setIsInitialized(true)
      }
    }

    initializeAuth()
  }, [isLoaded, isSignedIn, user, setUser, setAuthenticated, initializeApp, getToken])

  // Handle token refresh on focus (for session persistence)
  useEffect(() => {
    if (!env.clerk.enableAuth || !isAuthenticated || !getToken) return

    const handleFocus = async () => {
      try {
        const token = await getToken()
        if (!token) {
          console.warn('⚠️ Token refresh failed, user may need to re-authenticate')
          setAuthenticated(false)
        } else {
          console.log('🔄 Token refreshed successfully')
        }
      } catch (error) {
        console.error('❌ Token refresh error:', error)
      }
    }

    window.addEventListener('focus', handleFocus)
    return () => window.removeEventListener('focus', handleFocus)
  }, [isAuthenticated, getToken])

  // Show loading spinner while initializing
  if (!isInitialized) {
    return (
      <div className="mobile-container flex items-center justify-center min-h-screen">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-text-secondary">
            {DEV_CONFIG.SKIP_AUTH ? 'Initializing development mode...' : 'Checking authentication...'}
          </p>
        </div>
      </div>
    )
  }

  // Show error state if authentication failed
  if (authError) {
    return (
      <div className="mobile-container flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <h2 className="text-lg font-semibold text-text-primary mb-2">
            Authentication Error
          </h2>
          <p className="text-text-secondary mb-4">{authError}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-accent-primary text-white rounded-lg hover:bg-accent-primary/90 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  // Show auth page if not signed in (production mode only)
  if (env.clerk.enableAuth && !isAuthenticated) {
    return <AuthPage />
  }

  // Render protected content
  return <>{children}</>
}

export default AuthGuard
