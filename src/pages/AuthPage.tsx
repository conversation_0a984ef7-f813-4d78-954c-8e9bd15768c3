import React from 'react'
import { SignIn, SignUp } from '@clerk/clerk-react'
import { useState } from 'react'
import { Brain } from 'lucide-react'
import Button from '@/components/UI/Button'

const AuthPage: React.FC = () => {
  const [isSignUp, setIsSignUp] = useState(false)

  return (
    <div className="mobile-container flex flex-col min-h-screen bg-gradient-to-br from-accent-primary/10 to-accent-purple/10">
      {/* Header */}
      <div className="text-center pt-12 pb-8">
        <div className="flex items-center justify-center mb-4">
          <Brain className="w-12 h-12 text-accent-primary" />
        </div>
        <h1 className="text-3xl font-bold text-text-primary mb-2">
          Synapse
        </h1>
        <p className="text-text-secondary">
          AI-Native Note App
        </p>
      </div>

      {/* Features */}
      <div className="px-6 mb-8">
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-accent-primary/20 rounded-full flex items-center justify-center">
              <span className="text-accent-primary text-sm">🧠</span>
            </div>
            <div>
              <h3 className="font-medium text-text-primary">AI-Powered Organization</h3>
              <p className="text-sm text-text-secondary">Automatically organize your thoughts and ideas</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-accent-success/20 rounded-full flex items-center justify-center">
              <span className="text-accent-success text-sm">📱</span>
            </div>
            <div>
              <h3 className="font-medium text-text-primary">Multi-Modal Input</h3>
              <p className="text-sm text-text-secondary">Text, voice, images, and clipboard support</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-accent-purple/20 rounded-full flex items-center justify-center">
              <span className="text-accent-purple text-sm">☁️</span>
            </div>
            <div>
              <h3 className="font-medium text-text-primary">Sync Everywhere</h3>
              <p className="text-sm text-text-secondary">Access your notes on any device, offline or online</p>
            </div>
          </div>
        </div>
      </div>

      {/* Auth Section */}
      <div className="flex-1 px-6">
        <div className="bg-bg-secondary rounded-2xl shadow-lg p-6">
          {/* Toggle Buttons */}
          <div className="flex bg-bg-tertiary rounded-lg p-1 mb-6">
            <Button
              variant={!isSignUp ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setIsSignUp(false)}
              className="flex-1"
            >
              Sign In
            </Button>
            <Button
              variant={isSignUp ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setIsSignUp(true)}
              className="flex-1"
            >
              Sign Up
            </Button>
          </div>

          {/* Clerk Auth Components */}
          <div className="flex justify-center">
            {isSignUp ? (
              <SignUp 
                appearance={{
                  elements: {
                    rootBox: "w-full",
                    card: "shadow-none border-none bg-transparent",
                    headerTitle: "hidden",
                    headerSubtitle: "hidden",
                    socialButtonsBlockButton: "bg-bg-tertiary border-border-primary text-text-primary hover:bg-bg-primary",
                    formButtonPrimary: "bg-accent-primary hover:bg-blue-600",
                    footerActionLink: "text-accent-primary hover:text-blue-600"
                  }
                }}
                redirectUrl="/inbox"
              />
            ) : (
              <SignIn 
                appearance={{
                  elements: {
                    rootBox: "w-full",
                    card: "shadow-none border-none bg-transparent",
                    headerTitle: "hidden",
                    headerSubtitle: "hidden",
                    socialButtonsBlockButton: "bg-bg-tertiary border-border-primary text-text-primary hover:bg-bg-primary",
                    formButtonPrimary: "bg-accent-primary hover:bg-blue-600",
                    footerActionLink: "text-accent-primary hover:text-blue-600"
                  }
                }}
                redirectUrl="/inbox"
              />
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center py-6 text-xs text-text-tertiary">
        <p>By continuing, you agree to our Terms of Service and Privacy Policy</p>
      </div>
    </div>
  )
}

export default AuthPage
