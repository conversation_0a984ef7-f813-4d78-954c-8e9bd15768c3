import React, { useState } from 'react'
import { Search, Heart, Edit2, Trash2 } from 'lucide-react'
import { formatDate } from '@/lib/utils'
import { cn } from '@/lib/utils'
import { useAppStore } from '@/store/appStore'
import { useAPI } from '@/services/api'
import useEntries from '@/hooks/useEntries'
import useItemHighlight from '@/hooks/useItemHighlight'
import Input from '@/components/UI/Input'
import Button from '@/components/UI/Button'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import EditNoteModal from '@/components/Notes/EditNoteModal'
import InteractivePageHeader from '@/components/UI/InteractivePageHeader'

const NotesPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [filter, setFilter] = useState<'all' | 'recent' | 'favorites'>('all')

  // Search highlighting
  const { highlightedItemId } = useItemHighlight('notes')

  // 编辑和删除状态
  const [editingNote, setEditingNote] = useState<any | null>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null)

  // 左滑手势状态
  const [swipedItemId, setSwipedItemId] = useState<string | null>(null)
  const [swipeOffset, setSwipeOffset] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  const { isLoading, error, openMagicInputWithContext } = useAppStore()
  const { updateEntry, deleteEntry, refreshEntries, isLoading: apiLoading } = useAPI()
  const { textEntries, recentEntries, searchEntries } = useEntries()

  // Convert entries to display format
  const displayNotes = textEntries.map(entry => ({
    id: entry.id,
    title: entry.title,
    preview: entry.content.length > 100 ? entry.content.substring(0, 100) + '...' : entry.content,
    tags: entry.metadata.tags || [],
    category: entry.metadata.category || 'general',
    timestamp: entry.createdAt,
    favorited: entry.metadata.favorited || false
  }))

  // Apply search filter
  const searchResults = searchTerm ? searchEntries(searchTerm) : textEntries

  const filteredNotes = displayNotes.filter(note => {
    // Check if note is in search results
    const inSearchResults = searchResults.some(entry => entry.id === note.id)
    if (searchTerm && !inSearchResults) return false

    // Apply category filter
    switch (filter) {
      case 'recent':
        return recentEntries.some(entry => entry.id === note.id)
      case 'favorites':
        return note.favorited === true
      default:
        return true
    }
  })

  // 编辑功能
  const startEdit = (note: any) => {
    setEditingNote(note)
    setIsEditModalOpen(true)
    // 进入编辑模式时重置滑动状态
    closeSwipe()
  }

  const closeEditModal = () => {
    setIsEditModalOpen(false)
    setEditingNote(null)
  }

  const saveEditedNote = async (formData: {
    title: string
    content: string
    tags: string
    category: string
  }) => {
    if (!editingNote) return

    try {
      const tagsArray = formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)

      await updateEntry(editingNote.id, {
        title: formData.title,
        content: formData.content,
        metadata: {
          tags: tagsArray,
          category: formData.category
        }
      })

      await refreshEntries()
      console.log('✅ Note updated successfully')
    } catch (error) {
      console.error('❌ Failed to update note:', error)
    }
  }

  // 删除功能
  const confirmDelete = (id: string) => {
    setDeleteConfirm(id)
  }

  const cancelDelete = () => {
    setDeleteConfirm(null)
  }

  const handleDelete = async () => {
    if (!deleteConfirm) return

    try {
      await deleteEntry(deleteConfirm)
      setDeleteConfirm(null)
      setSwipedItemId(null)
      await refreshEntries()
    } catch (error) {
      console.error('❌ Failed to delete note:', error)
    }
  }

  // 左滑手势处理 - 与Todos页面保持一致
  const SWIPE_THRESHOLD = 60
  const SWIPE_ACTION_WIDTH = 128 // 两个按钮各64px

  const handleSwipeStart = (e: React.MouseEvent | React.TouchEvent, noteId: string) => {
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY

    setIsDragging(true)
    setDragStart({ x: clientX, y: clientY })
    setSwipedItemId(noteId)
    setSwipeOffset(0)
  }

  const handleSwipeMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isDragging || !swipedItemId) return

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const deltaX = dragStart.x - clientX

    // 只允许向左滑动
    if (deltaX > 0 && deltaX <= SWIPE_ACTION_WIDTH) {
      setSwipeOffset(deltaX)
    }
  }

  const handleSwipeEnd = () => {
    setIsDragging(false)

    if (swipeOffset > SWIPE_THRESHOLD) {
      setSwipeOffset(SWIPE_ACTION_WIDTH) // 吸附到完全显示状态
    } else {
      // 否则回弹到原位置
      setSwipeOffset(0)
      setSwipedItemId(null)
    }
  }

  // 关闭滑动操作
  const closeSwipe = () => {
    setSwipeOffset(0)
    setSwipedItemId(null)
  }

  // 点击外部区域关闭滑动
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (swipedItemId && swipeOffset > 0) {
        const target = event.target as Element
        if (!target.closest(`[data-note-id="${swipedItemId}"]`)) {
          closeSwipe()
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [swipedItemId, swipeOffset])

  const toggleFavorite = async (id: string) => {
    try {
      const note = displayNotes.find(n => n.id === id)
      if (!note) {
        console.error('❌ Note not found for favoriting:', id)
        return
      }

      const originalEntry = textEntries.find(e => e.id === id)
      if (!originalEntry) {
        console.error('❌ Original entry not found for favoriting:', id)
        return
      }

      const updatedEntry = {
        ...originalEntry,
        metadata: {
          ...originalEntry.metadata,
          favorited: !note.favorited
        }
      }

      await updateEntry(id, updatedEntry)
      await refreshEntries()
      console.log(`✅ Note ${note.favorited ? 'unfavorited' : 'favorited'} successfully`)
    } catch (error) {
      console.error('❌ Failed to toggle favorite:', error)
      // 可以在这里添加用户友好的错误提示
      // 例如：showToast('Failed to update favorite status. Please try again.')
    }
  }



  if (isLoading && textEntries.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-amber-700 dark:text-amber-600 mb-2 font-normal">Error loading notes</div>
        <p className="text-stone-500 dark:text-stone-400">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Interactive Page Header */}
      <InteractivePageHeader
        title="Notes"
        description="Your thoughts and ideas, beautifully organized"
        onAddClick={() => openMagicInputWithContext('notes')}
        addLabel="Add Note"
        rightContent={
          <div className="flex flex-col gap-1">
            <button
              onClick={() => setFilter('all')}
              className={cn(
                'px-2 py-1 text-xs font-normal uppercase tracking-wider transition-colors text-center',
                filter === 'all'
                  ? 'bg-emerald-700 dark:bg-emerald-600 text-white'
                  : 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400 hover:bg-stone-200 dark:hover:bg-stone-600'
              )}
            >
              All
            </button>
            <button
              onClick={() => setFilter('recent')}
              className={cn(
                'px-2 py-1 text-xs font-normal uppercase tracking-wider transition-colors text-center',
                filter === 'recent'
                  ? 'bg-emerald-700 dark:bg-emerald-600 text-white'
                  : 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400 hover:bg-stone-200 dark:hover:bg-stone-600'
              )}
            >
              Recent
            </button>
            <button
              onClick={() => setFilter('favorites')}
              className={cn(
                'px-2 py-1 text-xs font-normal uppercase tracking-wider transition-colors text-center',
                filter === 'favorites'
                  ? 'bg-emerald-700 dark:bg-emerald-600 text-white'
                  : 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400 hover:bg-stone-200 dark:hover:bg-stone-600'
              )}
            >
              Favorites
            </button>
          </div>
        }
      />

      {/* Search Bar */}
      <Input
        placeholder="Search notes..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        icon={<Search className="w-4 h-4" />}
      />



      {/* Notes Grid */}
      <div className="grid gap-4">
        {filteredNotes.length > 0 ? (
          filteredNotes.map((note) => (
            <div
              key={note.id}
              data-note-id={note.id}
              className={cn(
                "relative overflow-hidden bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 transition-all hover:shadow-sm",
                "search-highlight",
                highlightedItemId === note.id && "search-highlight-active"
              )}
            >
              {/* 左滑操作按钮背景 - 只在滑动时显示 */}
              {swipedItemId === note.id && swipeOffset > 0 && (
                <div className="absolute right-0 top-0 bottom-0 flex items-center bg-amber-700 dark:bg-amber-600">
                  <div className="flex items-center h-full">
                    <button
                      onClick={() => {
                        startEdit(note)
                        closeSwipe()
                      }}
                      className="flex items-center justify-center w-16 h-full bg-emerald-700 dark:bg-emerald-600 text-white hover:bg-emerald-800 dark:hover:bg-emerald-700 transition-colors"
                      title="Edit"
                    >
                      <Edit2 className="w-5 h-5" />
                    </button>
                    <button
                      onClick={() => {
                        confirmDelete(note.id)
                        closeSwipe()
                      }}
                      className="flex items-center justify-center w-16 h-full bg-amber-700 dark:bg-amber-600 text-white hover:bg-amber-800 dark:hover:bg-amber-700 transition-colors"
                      title="Delete"
                    >
                      <Trash2 className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              )}

              {/* 删除确认对话框 - Scandinavian Clean */}
              {deleteConfirm === note.id && (
                <div className="absolute inset-0 bg-amber-50 dark:bg-amber-900/20 border-2 border-amber-200 dark:border-amber-700 flex items-center justify-center z-10 animate-in scale-in duration-200">
                  <div className="text-center">
                    <p className="text-amber-800 dark:text-amber-300 font-normal mb-4 uppercase tracking-wider">Delete this note?</p>
                    <div className="flex gap-2 justify-center">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={cancelDelete}
                        disabled={apiLoading}
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="primary"
                        size="sm"
                        onClick={handleDelete}
                        disabled={apiLoading}
                        className="bg-amber-700 dark:bg-amber-600 hover:bg-amber-800 dark:hover:bg-amber-700 text-white"
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Note Content - 显示模式 */}
                <div
                  className={cn(
                    'relative bg-white dark:bg-stone-800 p-4 transition-transform duration-200 ease-out cursor-pointer group'
                  )}
                  style={{
                    transform: swipedItemId === note.id ? `translateX(-${swipeOffset}px)` : 'translateX(0)'
                  }}
                  onMouseDown={(e) => handleSwipeStart(e, note.id)}
                  onMouseMove={handleSwipeMove}
                  onMouseUp={handleSwipeEnd}
                  onMouseLeave={handleSwipeEnd}
                  onTouchStart={(e) => handleSwipeStart(e, note.id)}
                  onTouchMove={handleSwipeMove}
                  onTouchEnd={handleSwipeEnd}
                  onClick={() => console.log('Open note:', note.id)}
                >
                  {/* Note Header */}
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="font-normal text-stone-800 dark:text-stone-200 group-hover:text-emerald-700 dark:group-hover:text-emerald-600 transition-colors">
                      {note.title}
                    </h3>

                    {/* 只保留收藏按钮 */}
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        toggleFavorite(note.id)
                      }}
                      className={cn(
                        'p-1 transition-colors',
                        note.favorited
                          ? 'text-amber-700 dark:text-amber-600 hover:text-amber-800 dark:hover:text-amber-700'
                          : 'text-stone-400 dark:text-stone-500 hover:text-amber-700 dark:hover:text-amber-600'
                      )}
                    >
                      <Heart className={cn('w-4 h-4', note.favorited && 'fill-current')} />
                    </button>
                  </div>

                  {/* Note Preview */}
                  <p className="text-sm text-stone-500 dark:text-stone-400 mb-4 line-clamp-2">
                    {note.preview}
                  </p>

                  {/* Note Meta */}
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-stone-400 dark:text-stone-500">
                      {formatDate(note.timestamp)}
                    </div>

                    {/* Tags */}
                    <div className="flex gap-1">
                      {note.tags.slice(0, 2).map((tag) => (
                        <span
                          key={tag}
                          className="px-2 py-1 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-400 text-xs font-normal uppercase tracking-wider"
                        >
                          {tag}
                        </span>
                      ))}
                      {note.tags.length > 2 && (
                        <span className="px-2 py-1 bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400 text-xs font-normal">
                          +{note.tags.length - 2}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
            </div>
          ))
        ) : (
          <div className="text-center py-12">
            <div className="w-12 h-12 bg-stone-300 dark:bg-stone-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
              No notes found
            </h3>
            <p className="text-stone-500 dark:text-stone-400">
              {searchTerm
                ? 'Try adjusting your search terms'
                : 'Start capturing your thoughts with the magic input below'
              }
            </p>
          </div>
        )}
      </div>

      {/* Edit Note Modal */}
      <EditNoteModal
        note={editingNote}
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSave={saveEditedNote}
        isLoading={apiLoading}
      />
    </div>
  )
}

export default NotesPage
