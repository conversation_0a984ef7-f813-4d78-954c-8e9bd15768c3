// ===========================================
// MOCK AUTHENTICATION PAGE FOR TESTING
// ===========================================

import React, { useState } from 'react'
import { MockAuthService, TEST_TOKENS, createAuthHeaders } from '../services/mockAuthService'
import { env } from '../config/environment'

interface TestResult {
  test: string
  status: 'success' | 'error' | 'pending'
  message: string
  data?: any
}

const MockAuthPage: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [selectedToken, setSelectedToken] = useState<keyof typeof TEST_TOKENS>('VALID_USER')

  const mockAuth = MockAuthService.getInstance()

  const addTestResult = (result: TestResult) => {
    setTestResults(prev => [...prev, result])
  }

  const clearResults = () => {
    setTestResults([])
  }

  // Test API endpoints with different authentication scenarios
  const testAPIEndpoint = async (endpoint: string, tokenType: keyof typeof TEST_TOKENS, method: string = 'GET', body?: any) => {
    const testName = `${method} ${endpoint} (${tokenType})`
    
    addTestResult({
      test: testName,
      status: 'pending',
      message: 'Testing...'
    })

    try {
      const headers = createAuthHeaders(tokenType)
      const response = await fetch(`${env.api.baseUrl}${endpoint}`, {
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined
      })

      const data = await response.json()
      
      addTestResult({
        test: testName,
        status: response.ok ? 'success' : 'error',
        message: `Status: ${response.status} ${response.statusText}`,
        data
      })
    } catch (error) {
      addTestResult({
        test: testName,
        status: 'error',
        message: `Error: ${error instanceof Error ? error.message : String(error)}`
      })
    }
  }

  // Run comprehensive authentication tests
  const runAuthenticationTests = async () => {
    setIsRunning(true)
    clearResults()

    // Test 1: Health check (no auth required)
    await testAPIEndpoint('/api/health', 'VALID_USER')

    // Test 2: Valid token authentication
    await testAPIEndpoint('/api/entries', 'VALID_USER')

    // Test 3: Expired token
    await testAPIEndpoint('/api/entries', 'EXPIRED_USER')

    // Test 4: Invalid token
    await testAPIEndpoint('/api/entries', 'INVALID_TOKEN')

    // Test 5: Create entry with valid token
    await testAPIEndpoint('/api/entries', 'VALID_USER', 'POST', {
      rawText: 'Test authentication flow: create meeting tomorrow at 2pm, buy groceries'
    })

    // Test 6: Analyze endpoint (optional auth)
    await testAPIEndpoint('/api/analyze', 'VALID_USER', 'POST', {
      content: 'Test analysis: schedule dentist appointment, finish project report'
    })

    setIsRunning(false)
  }

  // Test database operations
  const testDatabaseOperations = async () => {
    setIsRunning(true)
    
    // Test database operations using the test-db endpoint
    const tests = [
      { operation: 'getUser', userId: 'test-user-001' },
      { operation: 'createUser', userId: 'test-user-new' },
      { operation: 'createSession', userId: 'test-user-001', data: {
        rawText: 'Database test: meeting tomorrow, buy milk',
        items: [
          { type: 'calendar', title: 'Meeting', content: 'Meeting tomorrow', metadata: { priority: 'high' } },
          { type: 'todo', title: 'Buy milk', content: 'Buy milk', metadata: { priority: 'low' } }
        ]
      }},
      { operation: 'getUserEntries', userId: 'test-user-001' }
    ]

    for (const test of tests) {
      await testAPIEndpoint('/api/test-db', 'VALID_USER', 'POST', test)
    }

    setIsRunning(false)
  }

  // Generate new test tokens
  const generateNewTokens = () => {
    mockAuth.clearMockTokens()
    
    // Regenerate tokens
    const validToken = mockAuth.generateMockToken('user-001', {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User'
    })
    
    const expiredToken = mockAuth.generateExpiredToken('user-expired')
    const invalidToken = mockAuth.generateInvalidToken()
    
    mockAuth.mockTokens.set(TEST_TOKENS.VALID_USER, validToken)
    mockAuth.mockTokens.set(TEST_TOKENS.EXPIRED_USER, expiredToken)
    mockAuth.mockTokens.set(TEST_TOKENS.INVALID_TOKEN, invalidToken)

    addTestResult({
      test: 'Token Generation',
      status: 'success',
      message: 'New test tokens generated successfully'
    })
  }

  // Test individual endpoint with selected token
  const testSelectedEndpoint = async (endpoint: string, method: string = 'GET') => {
    await testAPIEndpoint(endpoint, selectedToken, method)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            🧪 Authentication Flow Testing
          </h1>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Control Panel */}
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h2 className="text-xl font-semibold text-blue-900 mb-4">Test Controls</h2>
                
                <div className="space-y-3">
                  <button
                    onClick={runAuthenticationTests}
                    disabled={isRunning}
                    className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {isRunning ? 'Running Tests...' : 'Run Authentication Tests'}
                  </button>
                  
                  <button
                    onClick={testDatabaseOperations}
                    disabled={isRunning}
                    className="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50"
                  >
                    {isRunning ? 'Running Tests...' : 'Test Database Operations'}
                  </button>
                  
                  <button
                    onClick={generateNewTokens}
                    className="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
                  >
                    Generate New Tokens
                  </button>
                  
                  <button
                    onClick={clearResults}
                    className="w-full bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700"
                  >
                    Clear Results
                  </button>
                </div>
              </div>

              {/* Individual Endpoint Testing */}
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-yellow-900 mb-3">Individual Endpoint Testing</h3>
                
                <div className="space-y-3">
                  <select
                    value={selectedToken}
                    onChange={(e) => setSelectedToken(e.target.value as keyof typeof TEST_TOKENS)}
                    className="w-full p-2 border border-gray-300 rounded-lg"
                  >
                    <option value="VALID_USER">Valid User Token</option>
                    <option value="EXPIRED_USER">Expired Token</option>
                    <option value="INVALID_TOKEN">Invalid Token</option>
                    <option value="ADMIN_USER">Admin User Token</option>
                  </select>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => testSelectedEndpoint('/api/health')}
                      className="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600"
                    >
                      Health Check
                    </button>
                    <button
                      onClick={() => testSelectedEndpoint('/api/entries')}
                      className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600"
                    >
                      Get Entries
                    </button>
                  </div>
                </div>
              </div>

              {/* Configuration Info */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Configuration</h3>
                <div className="text-sm text-gray-600 space-y-1">
                  <p><strong>API Base URL:</strong> {env.api.baseUrl}</p>
                  <p><strong>Auth Enabled:</strong> {env.clerk.enableAuth ? 'Yes' : 'No'}</p>
                  <p><strong>Environment:</strong> {env.environment}</p>
                  <p><strong>Debug Logging:</strong> {env.features.debugLogging ? 'Yes' : 'No'}</p>
                </div>
              </div>
            </div>

            {/* Test Results */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Results</h2>
              
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {testResults.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No tests run yet. Click a test button to start.</p>
                ) : (
                  testResults.map((result, index) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg border-l-4 ${
                        result.status === 'success' 
                          ? 'bg-green-50 border-green-500' 
                          : result.status === 'error'
                          ? 'bg-red-50 border-red-500'
                          : 'bg-yellow-50 border-yellow-500'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900">{result.test}</h4>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          result.status === 'success' 
                            ? 'bg-green-100 text-green-800' 
                            : result.status === 'error'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {result.status}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{result.message}</p>
                      {result.data && (
                        <details className="mt-2">
                          <summary className="text-xs text-gray-500 cursor-pointer">View Response Data</summary>
                          <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MockAuthPage
