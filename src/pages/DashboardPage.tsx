import React from 'react'
import { formatTodayDate } from '@/lib/utils'
import { useAppStore } from '@/store/appStore'
import useDashboardData from '@/hooks/useDashboardData'
import useItemHighlight from '@/hooks/useItemHighlight'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import QuickStatsCards from '@/components/Dashboard/QuickStatsCards'
import PrioritySection from '@/components/Dashboard/PrioritySection'
import RecentActivity from '@/components/Dashboard/RecentActivity'
import QuickActions from '@/components/Dashboard/QuickActions'

const DashboardPage: React.FC = () => {
  const { isLoading, error } = useAppStore()
  
  // Get dashboard data using our custom hook
  const {
    dashboardStats,
    priorityItems,
    recentNotes,
    weeklyProgress,
    todaysSummary,
    hasOverdueItems,
    hasTodaysEvents,
    hasRecentNotes
  } = useDashboardData()

  // Search highlighting support
  const { highlightedItemId } = useItemHighlight('dashboard')

  if (isLoading && dashboardStats.totalNotes === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-2">Error loading dashboard</div>
        <p className="text-text-secondary">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Page Header - Scandinavian Clean */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-light text-stone-800 dark:text-stone-100">Dashboard</h2>
          <p className="text-stone-500 dark:text-stone-400 font-normal">
            {formatTodayDate()}
          </p>
        </div>
        <div className="text-right">
          <div className="w-8 h-8 bg-emerald-700 dark:bg-emerald-600 flex items-center justify-center">
            <span className="text-white font-normal text-xs">18%</span>
          </div>
          <div className="text-xs text-stone-400 dark:text-stone-500 mt-0.5 uppercase tracking-wide font-normal">
            Progress
          </div>
        </div>
      </div>

      {/* Quick Stats Cards */}
      <QuickStatsCards stats={dashboardStats} />

      {/* Main Content Grid - Scandinavian clean layout */}
      <div className="grid grid-cols-1 gap-4">
        {/* Priority Actions - Full width on mobile, half on tablet, third on desktop */}
        <div className="md:col-span-1 xl:col-span-1">
          <PrioritySection
            priorityItems={priorityItems}
            hasOverdueItems={hasOverdueItems}
            hasTodaysEvents={hasTodaysEvents}
          />
        </div>

        {/* Recent Activity - Full width on mobile, half on tablet, third on desktop */}
        <div className="md:col-span-1 xl:col-span-1">
          <RecentActivity
            recentNotes={recentNotes}
            hasRecentNotes={hasRecentNotes}
          />
        </div>

        {/* Quick Actions & Progress - Full width on mobile, full width on tablet, third on desktop */}
        <div className="md:col-span-2 xl:col-span-1">
          <QuickActions weeklyProgress={weeklyProgress} />
        </div>
      </div>

      {/* Empty State for New Users */}
      {dashboardStats.totalNotes === 0 && 
       dashboardStats.totalTodos === 0 && 
       dashboardStats.totalEvents === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-accent-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-2xl">🎉</span>
          </div>
          <h3 className="text-xl font-semibold text-text-primary mb-2">
            Welcome to Synapse AI Notes!
          </h3>
          <p className="text-text-secondary mb-6 max-w-md mx-auto">
            Start organizing your thoughts, tasks, and schedule. Use the Magic Input 
            or quick actions to create your first content.
          </p>
          <div className="flex justify-center">
            <button
              onClick={() => useAppStore.getState().openMagicInputWithContext('general')}
              className="bg-accent-primary text-white px-6 py-2 rounded-lg hover:bg-accent-primary/90 transition-colors"
            >
              Get Started
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default DashboardPage
