import React, { useState } from 'react'
import { CheckSquare, Square, Clock, Flag, Edit2, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { formatDate } from '@/lib/utils'
import { useAppStore } from '@/store/appStore'
import { useAPI } from '@/services/api'
import useEntries from '@/hooks/useEntries'
import useItemHighlight from '@/hooks/useItemHighlight'
import Button from '@/components/UI/Button'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import EditTodoModal from '@/components/Todos/EditTodoModal'
import InteractivePageHeader from '@/components/UI/InteractivePageHeader'

interface Todo {
  id: string
  title: string
  description?: string
  completed: boolean
  priority: 'low' | 'medium' | 'high'
  dueDate?: Date
  createdAt: Date
}

type FilterType = 'all' | 'pending' | 'completed' | 'overdue' | 'low' | 'medium' | 'high'

const TodosPage: React.FC = () => {
  const [filter, setFilter] = useState<FilterType>('all')
  const [editingTodo, setEditingTodo] = useState<Todo | null>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)

  // Search highlighting
  const { highlightedItemId } = useItemHighlight('todos')
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null)

  // 左滑手势状态
  const [swipedItemId, setSwipedItemId] = useState<string | null>(null)
  const [swipeOffset, setSwipeOffset] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  const { openMagicInputWithContext, isLoading, error } = useAppStore()
  const { updateEntry, deleteEntry, refreshEntries, isLoading: apiLoading } = useAPI()
  const { todoEntries, stats } = useEntries()

  // Convert entries to todos
  const todos: Todo[] = todoEntries.map(entry => ({
    id: entry.id,
    title: entry.title,
    description: entry.content,
    completed: entry.metadata.completed || false,
    priority: entry.metadata.priority || 'medium',
    dueDate: entry.metadata.dueDate ? new Date(entry.metadata.dueDate) : undefined,
    createdAt: new Date(entry.createdAt)
  }))

  const toggleTodo = async (id: string) => {
    try {
      const todo = todos.find(t => t.id === id)
      if (!todo) return

      const updatedEntry = {
        ...todoEntries.find(e => e.id === id)!,
        metadata: {
          ...todoEntries.find(e => e.id === id)!.metadata,
          completed: !todo.completed
        }
      }

      await updateEntry(id, updatedEntry)
      await refreshEntries()
      console.log('✅ Todo completion status updated')
    } catch (error) {
      console.error('❌ Failed to toggle todo:', error)
    }
  }

  // 优先级循环切换：低→中→高→低
  const cyclePriority = async (id: string) => {
    try {
      const todo = todos.find(t => t.id === id)
      if (!todo) return

      const priorityOrder: ('low' | 'medium' | 'high')[] = ['low', 'medium', 'high']
      const currentIndex = priorityOrder.indexOf(todo.priority)
      const nextPriority = priorityOrder[(currentIndex + 1) % priorityOrder.length]

      const updatedEntry = {
        ...todoEntries.find(e => e.id === id)!,
        metadata: {
          ...todoEntries.find(e => e.id === id)!.metadata,
          priority: nextPriority
        }
      }

      await updateEntry(id, updatedEntry)
      await refreshEntries()
      console.log(`✅ Priority updated to ${nextPriority}`)
    } catch (error) {
      console.error('❌ Failed to cycle priority:', error)
    }
  }

  // 左滑手势处理
  const SWIPE_THRESHOLD = 20 // 滑动阈值
  const SWIPE_ACTION_WIDTH = 120 // 操作按钮区域宽度

  const handleSwipeStart = (e: React.MouseEvent | React.TouchEvent, todoId: string) => {
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY

    setIsDragging(true)
    setDragStart({ x: clientX, y: clientY })
    setSwipedItemId(todoId)
    setSwipeOffset(0)
  }

  const handleSwipeMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isDragging || !swipedItemId) return

    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const deltaX = dragStart.x - clientX

    // 只允许向左滑动
    if (deltaX > 0) {
      const clampedOffset = Math.min(deltaX, SWIPE_ACTION_WIDTH)
      setSwipeOffset(clampedOffset)
    }
  }

  const handleSwipeEnd = () => {
    if (!isDragging) return

    setIsDragging(false)

    // 如果滑动距离超过阈值，显示操作按钮
    if (swipeOffset > SWIPE_THRESHOLD) {
      setSwipeOffset(SWIPE_ACTION_WIDTH) // 吸附到完全显示状态
    } else {
      // 否则回弹到原位置
      setSwipeOffset(0)
      setSwipedItemId(null)
    }
  }

  // 关闭滑动操作
  const closeSwipe = () => {
    setSwipeOffset(0)
    setSwipedItemId(null)
  }

  // 点击外部区域关闭滑动
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (swipedItemId && swipeOffset > 0) {
        const target = event.target as Element
        if (!target.closest(`[data-todo-id="${swipedItemId}"]`)) {
          closeSwipe()
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [swipedItemId, swipeOffset])

  const startEdit = (todo: Todo) => {
    setEditingTodo(todo)
    setIsEditModalOpen(true)
  }

  const closeEditModal = () => {
    setIsEditModalOpen(false)
    setEditingTodo(null)
  }

  const saveEditedTodo = async (formData: {
    title: string
    description: string
    priority: 'low' | 'medium' | 'high'
    dueDate: string
  }) => {
    if (!editingTodo) return

    try {
      const originalEntry = todoEntries.find(e => e.id === editingTodo.id)!
      const updatedEntry = {
        ...originalEntry,
        title: formData.title.trim(),
        content: formData.description.trim(),
        metadata: {
          ...originalEntry.metadata,
          priority: formData.priority,
          dueDate: formData.dueDate ? new Date(formData.dueDate).toISOString() : undefined
        }
      }

      await updateEntry(editingTodo.id, updatedEntry)
      await refreshEntries()
      console.log('✅ Todo updated successfully')
    } catch (error) {
      console.error('❌ Failed to update todo:', error)
      throw error
    }
  }

  const confirmDelete = (id: string) => {
    setDeleteConfirm(id)
  }

  const cancelDelete = () => {
    setDeleteConfirm(null)
  }

  const handleDelete = async () => {
    if (!deleteConfirm) return

    try {
      await deleteEntry(deleteConfirm)
      await refreshEntries()
      setDeleteConfirm(null)
      console.log('✅ Todo deleted successfully')
    } catch (error) {
      console.error('❌ Failed to delete todo:', error)
    }
  }

  // Helper functions for date calculations - Fixed and improved
  const isOverdue = (dueDate?: Date) => {
    if (!dueDate) return false
    const now = new Date()
    const due = new Date(dueDate)

    // Simple comparison: if due date has passed, it's overdue
    return due.getTime() < now.getTime()
  }

  const isDueSoon = (dueDate?: Date) => {
    if (!dueDate) return false
    if (isOverdue(dueDate)) return false // Can't be due soon if already overdue

    const now = new Date()
    const due = new Date(dueDate)

    // Due soon if it's within the next 24 hours
    const diffMs = due.getTime() - now.getTime()
    const hoursUntilDue = diffMs / (1000 * 60 * 60)

    return hoursUntilDue >= 0 && hoursUntilDue <= 24
  }

  const getTimeUntilDue = (dueDate?: Date) => {
    if (!dueDate) return null

    const now = new Date()
    const due = new Date(dueDate)

    // Calculate the difference in milliseconds
    const diffMs = due.getTime() - now.getTime()
    const absDiffMs = Math.abs(diffMs)

    // Convert to different time units
    const diffMinutes = Math.floor(absDiffMs / (1000 * 60))
    const diffHours = Math.floor(absDiffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(absDiffMs / (1000 * 60 * 60 * 24))
    const diffWeeks = Math.floor(diffDays / 7)
    const diffMonths = Math.floor(diffDays / 30)

    // Helper function to format time units
    const formatTimeUnit = (value: number, unit: string, isPast: boolean = false) => {
      const plural = value !== 1 ? 's' : ''
      const timeStr = `${value} ${unit}${plural}`
      return isPast ? `${timeStr} ago` : `in ${timeStr}`
    }

    const isPast = diffMs < 0

    // Choose the most appropriate time unit based on the duration
    if (diffMonths > 0) {
      return formatTimeUnit(diffMonths, 'month', isPast)
    } else if (diffWeeks > 0) {
      return formatTimeUnit(diffWeeks, 'week', isPast)
    } else if (diffDays > 0) {
      return formatTimeUnit(diffDays, 'day', isPast)
    } else if (diffHours > 0) {
      return formatTimeUnit(diffHours, 'hour', isPast)
    } else if (diffMinutes > 0) {
      return formatTimeUnit(diffMinutes, 'minute', isPast)
    } else {
      return isPast ? 'just now' : 'due now'
    }
  }

  // Calculate filter counts
  const pendingTodos = todos.filter(todo => !todo.completed)
  const completedTodos = todos.filter(todo => todo.completed)
  const overdueTodos = todos.filter(todo => !todo.completed && isOverdue(todo.dueDate))
  const lowPriorityTodos = todos.filter(todo => todo.priority === 'low')
  const mediumPriorityTodos = todos.filter(todo => todo.priority === 'medium')
  const highPriorityTodos = todos.filter(todo => todo.priority === 'high')

  const filteredTodos = todos.filter(todo => {
    switch (filter) {
      case 'pending':
        return !todo.completed
      case 'completed':
        return todo.completed
      case 'overdue':
        return !todo.completed && isOverdue(todo.dueDate)
      case 'low':
        return todo.priority === 'low'
      case 'medium':
        return todo.priority === 'medium'
      case 'high':
        return todo.priority === 'high'
      default:
        return true
    }
  })

  const getPriorityColor = (priority: Todo['priority']) => {
    switch (priority) {
      case 'high':
        return 'text-red-500'
      case 'medium':
        return 'text-yellow-500'
      case 'low':
        return 'text-green-500'
    }
  }



  const filters = [
    { id: 'all' as FilterType, label: 'All', count: todos.length, group: 'status' },
    { id: 'pending' as FilterType, label: 'Pending', count: pendingTodos.length, group: 'status' },
    { id: 'completed' as FilterType, label: 'Completed', count: completedTodos.length, group: 'status' },
    { id: 'overdue' as FilterType, label: 'Overdue', count: overdueTodos.length, group: 'status' },
    { id: 'high' as FilterType, label: '🔴 High', count: highPriorityTodos.length, group: 'priority' },
    { id: 'medium' as FilterType, label: '🟡 Medium', count: mediumPriorityTodos.length, group: 'priority' },
    { id: 'low' as FilterType, label: '🟢 Low', count: lowPriorityTodos.length, group: 'priority' }
  ]

  if (isLoading && todoEntries.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-amber-700 dark:text-amber-600 mb-2 font-normal">Error loading todos</div>
        <p className="text-stone-500 dark:text-stone-400">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Interactive Page Header */}
      <InteractivePageHeader
        title="Todos"
        description="Keep track of your tasks and get things done"
        onAddClick={() => openMagicInputWithContext('todos')}
        addLabel="Add Todo"
      />

      {/* Stats - Scandinavian Clean */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-4 text-center">
          <div className="text-2xl font-light text-stone-800 dark:text-stone-100">{stats.todos}</div>
          <div className="text-sm text-stone-500 dark:text-stone-400 uppercase tracking-wider">Total</div>
        </div>
        <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-4 text-center">
          <div className="text-2xl font-light text-amber-700 dark:text-amber-600">{stats.pending}</div>
          <div className="text-sm text-stone-500 dark:text-stone-400 uppercase tracking-wider">Pending</div>
        </div>
        <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-4 text-center">
          <div className="text-2xl font-light text-emerald-700 dark:text-emerald-600">{stats.completed}</div>
          <div className="text-sm text-stone-500 dark:text-stone-400 uppercase tracking-wider">Completed</div>
        </div>
      </div>



      {/* Filter Tabs - Compact Two-Row Layout */}
      <div className="space-y-2">
        {/* Status Filters Row */}
        <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
          {filters.filter(f => f.group === 'status').map((filterOption) => {
            const isActive = filter === filterOption.id
            const isOverdue = filterOption.id === 'overdue'

            return (
              <button
                key={filterOption.id}
                onClick={() => setFilter(filterOption.id)}
                className={cn(
                  'flex items-center justify-center gap-1 px-2 py-1.5 transition-colors text-xs font-normal uppercase tracking-wider',
                  isActive
                    ? isOverdue
                      ? 'bg-amber-700 dark:bg-amber-600 text-white'
                      : 'bg-emerald-700 dark:bg-emerald-600 text-white'
                    : isOverdue && filterOption.count > 0
                      ? 'bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-400 border border-amber-200 dark:border-amber-700 hover:bg-amber-100 dark:hover:bg-amber-900/30'
                      : 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200 hover:bg-stone-200 dark:hover:bg-stone-600'
                )}
              >
                <span className="truncate text-xs">{filterOption.label.replace(/[🔴🟡🟢⏰📋✅]/g, '').trim()}</span>
                <span className={cn(
                  'text-xs px-1 py-0.5 flex-shrink-0 rounded',
                  isActive
                    ? 'bg-white/20'
                    : isOverdue && filterOption.count > 0
                      ? 'bg-amber-200 dark:bg-amber-800 text-amber-800 dark:text-amber-200'
                      : 'bg-stone-200 dark:bg-stone-600 text-stone-600 dark:text-stone-400'
                )}>
                  {filterOption.count}
                </span>
              </button>
            )
          })}
        </div>

        {/* Priority Filters Row */}
        <div className="grid grid-cols-3 gap-2">
          {filters.filter(f => f.group === 'priority').map((filterOption) => {
            const isActive = filter === filterOption.id

            return (
              <button
                key={filterOption.id}
                onClick={() => setFilter(filterOption.id)}
                className={cn(
                  'flex items-center justify-center gap-1 px-2 py-1.5 transition-colors text-xs font-normal uppercase tracking-wider',
                  isActive
                    ? 'bg-emerald-700 dark:bg-emerald-600 text-white'
                    : 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200 hover:bg-stone-200 dark:hover:bg-stone-600'
                )}
              >
                <span className="truncate text-xs">{filterOption.label.replace(/[🔴🟡🟢]/g, '').trim()}</span>
                <span className={cn(
                  'text-xs px-1 py-0.5 flex-shrink-0 rounded',
                  isActive ? 'bg-white/20' : 'bg-stone-200 dark:bg-stone-600 text-stone-600 dark:text-stone-400'
                )}>
                  {filterOption.count}
                </span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Todos List */}
      <div className="space-y-3">
        {filteredTodos.length > 0 ? (
          filteredTodos.map((todo) => (
            <div
              key={todo.id}
              data-todo-id={todo.id}
              className={cn(
                "relative overflow-hidden bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 transition-all hover:shadow-sm",
                "search-highlight",
                highlightedItemId === todo.id && "search-highlight-active"
              )}
            >
              {/* 左滑操作按钮背景 - Scandinavian Clean */}
              <div
                className={cn(
                  "absolute right-0 top-0 bottom-0 flex items-center bg-amber-700 dark:bg-amber-600 z-0 transition-all duration-200",
                  swipedItemId === todo.id ? "opacity-100 visible" : "opacity-0 invisible"
                )}
              >
                <div className="flex items-center h-full">
                  <button
                    onClick={() => {
                      startEdit(todo)
                      closeSwipe()
                    }}
                    className="flex items-center justify-center w-16 h-full bg-emerald-700 dark:bg-emerald-600 text-white hover:bg-emerald-800 dark:hover:bg-emerald-700 transition-colors"
                    title="Edit"
                  >
                    <Edit2 className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => {
                      confirmDelete(todo.id)
                      closeSwipe()
                    }}
                    className="flex items-center justify-center w-16 h-full bg-amber-700 dark:bg-amber-600 text-white hover:bg-amber-800 dark:hover:bg-amber-700 transition-colors"
                    title="Delete"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              </div>

              {/* 主要内容区域 - Scandinavian Clean */}
              <div
                className={cn(
                  'relative bg-white dark:bg-stone-800 p-4 transition-transform duration-200 ease-out z-10',
                  todo.completed && 'bg-stone-50 dark:bg-stone-700/50'
                )}
                style={{
                  transform: swipedItemId === todo.id ? `translateX(-${swipeOffset}px)` : 'translateX(0)'
                }}
                onMouseDown={(e) => handleSwipeStart(e, todo.id)}
                onMouseMove={handleSwipeMove}
                onMouseUp={handleSwipeEnd}
                onMouseLeave={handleSwipeEnd}
                onTouchStart={(e) => handleSwipeStart(e, todo.id)}
                onTouchMove={handleSwipeMove}
                onTouchEnd={handleSwipeEnd}
              >
              {/* Todo View Mode */}
                <div className="flex items-start gap-3">
                  {/* Checkbox - Scandinavian Clean */}
                  <button
                    onClick={() => toggleTodo(todo.id)}
                    disabled={apiLoading}
                    className={cn(
                      'flex-shrink-0 w-5 h-5 border-2 transition-colors mt-0.5',
                      todo.completed
                        ? 'bg-emerald-700 dark:bg-emerald-600 border-emerald-700 dark:border-emerald-600 text-white'
                        : 'border-stone-300 dark:border-stone-600 hover:border-emerald-700 dark:hover:border-emerald-600',
                      apiLoading && 'opacity-50 cursor-not-allowed'
                    )}
                  >
                    {todo.completed ? (
                      <CheckSquare className="w-full h-full" />
                    ) : (
                      <Square className="w-full h-full opacity-0" />
                    )}
                  </button>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-1">
                      <h3 className={cn(
                        'font-normal',
                        todo.completed
                          ? 'text-stone-500 dark:text-stone-400 line-through'
                          : 'text-stone-800 dark:text-stone-200'
                      )}>
                        {todo.title}
                      </h3>

                      {/* Priority - 点击切换 */}
                      <button
                        onClick={() => cyclePriority(todo.id)}
                        disabled={apiLoading}
                        className={cn(
                          'flex items-center gap-1 ml-2 p-1 hover:bg-stone-100 dark:hover:bg-stone-700 transition-colors',
                          apiLoading && 'opacity-50 cursor-not-allowed'
                        )}
                        title={`Priority: ${todo.priority} (click to cycle)`}
                      >
                        <Flag className={cn('w-4 h-4 flex-shrink-0', getPriorityColor(todo.priority))} />
                      </button>
                    </div>

                    {todo.description && (
                      <p className={cn(
                        'text-sm mb-2',
                        todo.completed ? 'text-stone-400 dark:text-stone-500' : 'text-stone-500 dark:text-stone-400'
                      )}>
                        {todo.description}
                      </p>
                    )}

                    {/* Meta */}
                    <div className="flex items-center gap-4 text-xs text-stone-400 dark:text-stone-500">
                      <span>Created {formatDate(todo.createdAt.getTime())}</span>

                      {todo.dueDate && !todo.completed && (
                        <div className={cn(
                          'flex items-center gap-1',
                          isOverdue(todo.dueDate) && 'text-amber-700 dark:text-amber-600',
                          isDueSoon(todo.dueDate) && !isOverdue(todo.dueDate) && 'text-amber-600 dark:text-amber-500'
                        )}>
                          <Clock className="w-3 h-3" />
                          <span>
                            {isOverdue(todo.dueDate) ? (
                              `Due ${getTimeUntilDue(todo.dueDate)} (Overdue)`
                            ) : isDueSoon(todo.dueDate) ? (
                              `Due ${getTimeUntilDue(todo.dueDate)} (Due Soon)`
                            ) : (
                              `Due ${formatDate(todo.dueDate.getTime())}`
                            )}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-12">
            <div className="w-12 h-12 bg-stone-300 dark:bg-stone-600 mx-auto mb-4"></div>
            <h3 className="text-lg font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">
              No todos found
            </h3>
            <p className="text-stone-500 dark:text-stone-400">
              {filter === 'all'
                ? 'Add your first todo to get started'
                : `No ${filter} todos at the moment`
              }
            </p>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog - Scandinavian Clean */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 animate-in fade-in duration-200">
          <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-6 max-w-sm w-full animate-in scale-in duration-200">
            <h3 className="text-lg font-normal text-stone-800 dark:text-stone-200 mb-2 uppercase tracking-wider">
              Delete Todo
            </h3>
            <p className="text-stone-500 dark:text-stone-400 mb-6">
              Are you sure you want to delete this todo? This action cannot be undone.
            </p>
            <div className="flex gap-3 justify-end">
              <Button
                variant="ghost"
                size="sm"
                onClick={cancelDelete}
                disabled={apiLoading}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                size="sm"
                onClick={handleDelete}
                disabled={apiLoading}
                className="bg-amber-700 dark:bg-amber-600 hover:bg-amber-800 dark:hover:bg-amber-700 text-white"
              >
                {apiLoading ? 'Deleting...' : 'Delete'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Edit Todo Modal */}
      <EditTodoModal
        todo={editingTodo}
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSave={saveEditedTodo}
        isLoading={apiLoading}
      />
    </div>
  )
}

export default TodosPage
