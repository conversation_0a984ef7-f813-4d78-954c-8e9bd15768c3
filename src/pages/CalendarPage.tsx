import React, { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, X, Edit2, Trash2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useAppStore } from '@/store/appStore'
import useEntries from '@/hooks/useEntries'
import useItemHighlight from '@/hooks/useItemHighlight'
import Button from '@/components/UI/Button'
import LoadingSpinner from '@/components/UI/LoadingSpinner'
import EditEventModal from '@/components/Calendar/EditEventModal'
import InteractivePageHeader from '@/components/UI/InteractivePageHeader'

interface CalendarEvent {
  id: string
  title: string
  date: Date
  time?: string
  type: 'meeting' | 'task' | 'reminder'
  priority?: 'low' | 'medium' | 'high'
}

const CalendarPage: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [view, setView] = useState<'month' | 'week'>('week') // 默认为Week视图
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)

  // Edit/Delete functionality state
  const [editingEvent, setEditingEvent] = useState<CalendarEvent | null>(null)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null)

  // 初始化weekViewOffset以显示今天、明天、后天的3天窗口
  const getInitialOffset = () => {
    const today = new Date()
    const weekDays = (() => {
      const week = []
      const startOfWeek = new Date(today)
      const day = startOfWeek.getDay()
      startOfWeek.setDate(startOfWeek.getDate() - day)

      for (let i = 0; i < 7; i++) {
        const weekDay = new Date(startOfWeek)
        weekDay.setDate(startOfWeek.getDate() + i)
        week.push(weekDay)
      }
      return week
    })()

    const todayIndex = weekDays.findIndex(day =>
      day.toDateString() === today.toDateString()
    )

    // 3天窗口从今天开始：今天、明天、后天
    // 今天在3天窗口的最左侧位置
    if (todayIndex <= 4) {
      return todayIndex // 今天在左侧，显示今天+后面2天
    } else {
      // 如果今天是周五或周六，需要跨周处理
      return todayIndex // 先保持当前逻辑，后续可优化跨周显示
    }
  }

  const [weekViewOffset, setWeekViewOffset] = useState(getInitialOffset())

  // 滑动手势状态
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [dragOffset, setDragOffset] = useState(0)
  const [currentTransform, setCurrentTransform] = useState(0)
  const [hasStartedDragging, setHasStartedDragging] = useState(false)

  // 垂直滚动位置保存
  const [savedScrollPosition, setSavedScrollPosition] = useState(0) // 是否已开始拖拽

  const { openMagicInputWithContext, isLoading, error, updateEntry, deleteEntry } = useAppStore()
  const { calendarEntries, upcomingEvents } = useEntries()

  // Search highlighting
  const { highlightedItemId } = useItemHighlight('calendar')

  // 过滤Upcoming Events，只显示当前时间之后的事件
  const filteredUpcomingEvents = upcomingEvents.filter(entry => {
    const now = new Date()
    const currentTime = now.getHours() * 60 + now.getMinutes() // 当前时间（分钟）

    // 获取事件日期
    const eventDate = entry.metadata.dueDate ? new Date(entry.metadata.dueDate) : new Date(entry.createdAt)

    // 如果事件是今天
    if (eventDate.toDateString() === now.toDateString()) {
      // 检查事件时间
      if (entry.metadata.time) {
        const [hours, minutes] = entry.metadata.time.split(':').map(Number)
        const eventTime = hours * 60 + minutes // 事件时间（分钟）
        return eventTime > currentTime // 只显示未来时间的事件
      }
      return true // 如果没有具体时间，显示今天的事件
    }

    // 如果事件是未来日期，显示
    return eventDate > now
  })

  // Convert entries to calendar events
  const events: CalendarEvent[] = calendarEntries.map(entry => ({
    id: entry.id,
    title: entry.title,
    date: entry.metadata.dueDate ? new Date(entry.metadata.dueDate) : new Date(entry.createdAt),
    time: entry.metadata.time,
    type: entry.metadata.category === 'work' ? 'meeting' : 'task',
    priority: entry.metadata.priority
  }))

  const today = new Date()
  const currentMonth = currentDate.getMonth()
  const currentYear = currentDate.getFullYear()

  // Get first day of month and number of days
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1)
  const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0)
  const firstDayWeekday = firstDayOfMonth.getDay()
  const daysInMonth = lastDayOfMonth.getDate()

  // Generate calendar days
  const calendarDays = []
  
  // Previous month days
  for (let i = firstDayWeekday - 1; i >= 0; i--) {
    const date = new Date(currentYear, currentMonth, -i)
    calendarDays.push({ date, isCurrentMonth: false })
  }
  
  // Current month days
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(currentYear, currentMonth, day)
    calendarDays.push({ date, isCurrentMonth: true })
  }
  
  // Next month days to fill the grid
  const remainingDays = 42 - calendarDays.length
  for (let day = 1; day <= remainingDays; day++) {
    const date = new Date(currentYear, currentMonth + 1, day)
    calendarDays.push({ date, isCurrentMonth: false })
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const navigate = (direction: 'prev' | 'next') => {
    if (view === 'month') {
      navigateMonth(direction)
    } else {
      // Week视图：左右按钮切换整周，重置3天窗口到默认位置
      navigateWeek(direction)
      // 切换周后，重置3天窗口到今天开始的位置
      const today = new Date()
      const newWeekDays = (() => {
        const week = []
        const targetDate = direction === 'prev'
          ? new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000)
          : new Date(currentDate.getTime() + 7 * 24 * 60 * 60 * 1000)
        const startOfWeek = new Date(targetDate)
        const day = startOfWeek.getDay()
        startOfWeek.setDate(startOfWeek.getDate() - day)

        for (let i = 0; i < 7; i++) {
          const weekDay = new Date(startOfWeek)
          weekDay.setDate(startOfWeek.getDate() + i)
          week.push(weekDay)
        }
        return week
      })()

      // 如果新周包含今天，则从今天开始；否则从周日开始
      const todayIndex = newWeekDays.findIndex(day =>
        day.toDateString() === today.toDateString()
      )
      setWeekViewOffset(todayIndex >= 0 ? todayIndex : 0)
    }
  }

  const isToday = (date: Date) => {
    return date.toDateString() === today.toDateString()
  }

  // const hasEvent = (date: Date) => {
  //   return events.some(event => event.date.toDateString() === date.toDateString())
  // }

  const getEventsForDate = (date: Date) => {
    return events.filter(event => event.date.toDateString() === date.toDateString())
  }

  const handleDateClick = (date: Date) => {
    setSelectedDate(date)
    setSelectedEvent(null) // Clear selected event when selecting a new date
  }

  const handleEventClick = (event: CalendarEvent, e?: React.MouseEvent) => {
    if (e) {
      e.stopPropagation() // Prevent date selection when clicking on event
    }
    setSelectedEvent(event)
    // Also set the selected date to the event's date for consistency
    setSelectedDate(event.date)
  }

  const handleTimeSlotClick = (date: Date, time: string) => {
    // Primary interaction: select the date
    setSelectedDate(date)

    // Secondary interaction: if there are events at this time slot, select the first one
    const eventsAtTime = getEventsForDate(date).filter(event => {
      if (!event.time) return false
      const eventHour = parseInt(event.time.split(':')[0])
      const slotHour = parseInt(time.split(':')[0])
      return eventHour === slotHour
    })

    if (eventsAtTime.length > 0) {
      setSelectedEvent(eventsAtTime[0])
    } else {
      setSelectedEvent(null)
    }
  }

  // Edit/Delete event handlers
  const startEditEvent = (event: CalendarEvent) => {
    setEditingEvent(event)
    setIsEditModalOpen(true)
  }

  const closeEditModal = () => {
    setIsEditModalOpen(false)
    setEditingEvent(null)
  }

  const saveEditedEvent = async (formData: {
    title: string
    description: string
    type: 'meeting' | 'task' | 'reminder'
    priority: 'low' | 'medium' | 'high'
    date: string
    time: string
  }) => {
    if (!editingEvent) return

    try {
      const originalEntry = calendarEntries.find(e => e.id === editingEvent.id)!
      const updatedEntry = {
        ...originalEntry,
        title: formData.title.trim(),
        content: formData.description.trim(),
        metadata: {
          ...originalEntry.metadata,
          priority: formData.priority,
          category: formData.type === 'meeting' ? 'work' : 'personal',
          dueDate: formData.date ? new Date(formData.date).toISOString() : undefined,
          time: formData.time || undefined
        }
      }

      await updateEntry(editingEvent.id, updatedEntry)
      console.log('✅ Event updated successfully')
    } catch (error) {
      console.error('❌ Failed to update event:', error)
    }
  }

  const confirmDeleteEvent = (eventId: string) => {
    setDeleteConfirm(eventId)
  }

  const cancelDeleteEvent = () => {
    setDeleteConfirm(null)
  }

  const executeDeleteEvent = async () => {
    if (!deleteConfirm) return

    try {
      await deleteEntry(deleteConfirm)

      // Clear selected event if it was the deleted one
      if (selectedEvent?.id === deleteConfirm) {
        setSelectedEvent(null)
      }

      setDeleteConfirm(null)
      console.log('✅ Event deleted successfully')
    } catch (error) {
      console.error('❌ Failed to delete event:', error)
    }
  }

  // Week view helpers - 7天完整周，但显示3天窗口
  const getWeekDays = (date: Date) => {
    const week = []
    const startOfWeek = new Date(date)
    const day = startOfWeek.getDay()
    startOfWeek.setDate(startOfWeek.getDate() - day) // 周日开始

    for (let i = 0; i < 7; i++) {
      const weekDay = new Date(startOfWeek)
      weekDay.setDate(startOfWeek.getDate() + i)
      week.push(weekDay)
    }
    return week
  }



  // 周级导航 - 切换整周数据
  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setDate(prev.getDate() - 7)
      } else {
        newDate.setDate(prev.getDate() + 7)
      }
      return newDate
    })
  }

  // 滑动手势处理 - 1:1跟手效果
  const DRAG_THRESHOLD = 15 // 滑动阈值：15px（进一步降低阈值，更容易触发）

  const handleDragStart = (clientX: number, clientY: number) => {
    setIsDragging(true)
    setDragStart({ x: clientX, y: clientY })
    setDragOffset(0)
    setCurrentTransform(weekViewOffset * (100/7)) // 修正为7天计算
    setHasStartedDragging(false)
  }

  const handleDragMove = (clientX: number) => {
    if (!isDragging) return

    const deltaX = clientX - dragStart.x
    const absDeltaX = Math.abs(deltaX)

    // 只有拖拽距离超过阈值才开始滑动
    if (!hasStartedDragging && absDeltaX < DRAG_THRESHOLD) {
      return
    }

    if (!hasStartedDragging) {
      setHasStartedDragging(true)
    }

    const calendarGrid = document.getElementById('calendar-grid')
    if (!calendarGrid) return

    const containerWidth = calendarGrid.clientWidth

    // 1:1跟手效果 - 移除敏感度调整
    const adjustedDeltaX = deltaX - (deltaX > 0 ? DRAG_THRESHOLD : -DRAG_THRESHOLD)
    const dragPercentage = (adjustedDeltaX / containerWidth) * 100

    // 计算边界限制 - 允许适度超出边界以支持吸附
    const dayWidth = 100 / 7 // 每天占14.29%
    const currentPosition = weekViewOffset * dayWidth

    // 允许拖拽超出边界一定距离，以支持吸附效果
    const extraDragAllowance = dayWidth * 0.5 // 允许额外拖拽半天的距离
    const maxLeftDrag = currentPosition + extraDragAllowance // 向右拖拽限制
    const maxRightDrag = (4 - weekViewOffset) * dayWidth + extraDragAllowance // 向左拖拽限制

    // 限制拖拽范围，但允许适度超出以支持吸附
    const clampedDragPercentage = Math.max(-maxRightDrag, Math.min(maxLeftDrag, dragPercentage))

    setDragOffset(clampedDragPercentage)
  }

  // 保存当前滚动位置
  const saveScrollPosition = () => {
    const calendarGrid = document.getElementById('calendar-grid')
    if (calendarGrid) {
      setSavedScrollPosition(calendarGrid.scrollTop)
      console.log('📍 Saved scroll position:', calendarGrid.scrollTop)
    }
  }

  // 恢复滚动位置
  const restoreScrollPosition = () => {
    const calendarGrid = document.getElementById('calendar-grid')
    if (calendarGrid && savedScrollPosition > 0) {
      setTimeout(() => {
        calendarGrid.scrollTop = savedScrollPosition
        console.log('📍 Restored scroll position:', savedScrollPosition)
      }, 100) // 短暂延迟确保DOM已更新
    }
  }

  const handleDragEnd = () => {
    if (!isDragging) return

    setIsDragging(false)
    setHasStartedDragging(false)

    // 如果没有真正开始拖拽，不改变位置
    if (Math.abs(dragOffset) < 1) {
      setDragOffset(0)
      return
    }

    // 保存当前滚动位置
    saveScrollPosition()

    // 智能吸附逻辑 - 基于1:1跟手效果
    const dayWidth = 100 / 7 // 每天占14.29%

    // 计算拖拽距离（以天为单位）
    const dragDistanceInDays = -dragOffset / dayWidth

    // 计算当前拖拽后的理论位置
    const currentPosition = weekViewOffset * dayWidth
    const draggedPosition = currentPosition - dragOffset
    const theoreticalOffset = draggedPosition / dayWidth

    // 吸附逻辑：吸附到最近的整数位置
    let targetOffset = Math.round(theoreticalOffset)

    // 如果拖拽距离较小（小于0.3天），保持原位置
    if (Math.abs(dragDistanceInDays) < 0.3) {
      targetOffset = weekViewOffset
    }

    // 限制在有效范围内 (0-4，对应7天中的5个可能的3天窗口)
    const clampedOffset = Math.max(0, Math.min(4, targetOffset))

    setWeekViewOffset(clampedOffset)
    setDragOffset(0)
    setCurrentTransform(clampedOffset * dayWidth)
  }

  // 鼠标事件处理
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    handleDragStart(e.clientX, e.clientY)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    handleDragMove(e.clientX)
  }

  const handleMouseUp = () => {
    handleDragEnd()
  }

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0]
    handleDragStart(touch.clientX, touch.clientY)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    const touch = e.touches[0]
    handleDragMove(touch.clientX)
  }

  const handleTouchEnd = () => {
    handleDragEnd()
  }



  // 完整24小时时间轴
  const timeSlots = Array.from({ length: 24 }, (_, i) => {
    const hour = i.toString().padStart(2, '0')
    return `${hour}:00`
  })

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']

  // 自动滚动到当前时间或恢复保存的滚动位置
  useEffect(() => {
    if (view === 'week') {
      const timer = setTimeout(() => {
        const calendarGrid = document.getElementById('calendar-grid')
        if (calendarGrid) {
          // 检查是否有全局滚动位置管理
          const globalScrollPositions = (window as any).pageScrollPositions
          const hasGlobalPosition = globalScrollPositions && globalScrollPositions.has('/calendar')

          if (hasGlobalPosition) {
            // 如果有全局滚动位置，不要干扰它
            console.log('📍 Global scroll position management active, skipping auto-scroll')
          } else if (savedScrollPosition > 0) {
            // 如果有保存的滚动位置，优先恢复它
            calendarGrid.scrollTop = savedScrollPosition
            console.log('📍 Restored scroll position:', savedScrollPosition)
          } else {
            // 否则滚动到当前时间
            const now = new Date()
            const currentHour = now.getHours()
            const scrollPosition = currentHour * 64
            calendarGrid.scrollTop = scrollPosition
            console.log('📍 Auto-scrolled to current time:', currentHour)
          }
        }
      }, 200) // 稍长延迟确保全局滚动位置恢复完成

      return () => clearTimeout(timer)
    }
  }, [view, currentDate, weekViewOffset, savedScrollPosition])

  // 全局鼠标事件监听（支持拖拽到容器外）
  useEffect(() => {
    if (!isDragging) return

    const handleGlobalMouseMove = (e: MouseEvent) => {
      handleDragMove(e.clientX)
    }

    const handleGlobalMouseUp = () => {
      handleDragEnd()
    }

    document.addEventListener('mousemove', handleGlobalMouseMove)
    document.addEventListener('mouseup', handleGlobalMouseUp)

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, dragStart.x, currentTransform])

  // 监听搜索导航的周视图调整事件
  useEffect(() => {
    const handleAdjustWeekView = (event: CustomEvent) => {
      const { targetDate } = event.detail
      console.log('📅 Received week view adjustment request for:', targetDate)

      // 保存当前滚动位置
      saveScrollPosition()

      // 计算目标日期应该在哪个偏移位置
      const weekDays = getWeekDays(currentDate)
      const targetDateStr = targetDate.toDateString()
      const targetDayIndex = weekDays.findIndex(day => day.toDateString() === targetDateStr)

      if (targetDayIndex !== -1) {
        // 计算最佳的weekViewOffset来显示目标日期
        // 目标是让目标日期在3天窗口的中间（索引1）
        let optimalOffset = targetDayIndex - 1

        // 限制在有效范围内 (0-4)
        optimalOffset = Math.max(0, Math.min(4, optimalOffset))

        console.log('📅 Adjusting week view offset from', weekViewOffset, 'to', optimalOffset)
        setWeekViewOffset(optimalOffset)
        setCurrentTransform(optimalOffset * (100/7))
      }
    }

    window.addEventListener('adjustWeekView', handleAdjustWeekView as EventListener)

    return () => {
      window.removeEventListener('adjustWeekView', handleAdjustWeekView as EventListener)
    }
  }, [currentDate, weekViewOffset, saveScrollPosition])

  if (isLoading && calendarEntries.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-amber-700 dark:text-amber-600 mb-2 font-normal">Error loading calendar events</div>
        <p className="text-stone-500 dark:text-stone-400">{error}</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Interactive Page Header */}
      <InteractivePageHeader
        title="Calendar"
        description="Your schedule and upcoming events"
        onAddClick={() => openMagicInputWithContext('calendar')}
        addLabel="Add Event"
        rightContent={
          <div className="flex flex-col gap-1">
            <button
              onClick={() => setView('month')}
              className={cn(
                'px-2 py-1 text-xs font-normal uppercase tracking-wider transition-colors text-center',
                view === 'month'
                  ? 'bg-emerald-700 dark:bg-emerald-600 text-white'
                  : 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400 hover:bg-stone-200 dark:hover:bg-stone-600'
              )}
            >
              Month
            </button>
            <button
              onClick={() => setView('week')}
              className={cn(
                'px-2 py-1 text-xs font-normal uppercase tracking-wider transition-colors text-center',
                view === 'week'
                  ? 'bg-emerald-700 dark:bg-emerald-600 text-white'
                  : 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400 hover:bg-stone-200 dark:hover:bg-stone-600'
              )}
            >
              Week
            </button>
          </div>
        }
      />

      {/* Calendar Header - Scandinavian Clean */}
      <div className="flex items-center justify-between">
        {/* Left: Time Period */}
        <h3 className="text-xl font-normal text-stone-800 dark:text-stone-200 uppercase tracking-wider">
          {view === 'month'
            ? `${monthNames[currentMonth]} ${currentYear}`
            : (() => {
                // Week视图显示完整7天的范围
                const weekDays = getWeekDays(currentDate)
                const startDate = weekDays[0] // 周日
                const endDate = weekDays[6]   // 周六
                const startMonth = monthNames[startDate.getMonth()]
                const endMonth = monthNames[endDate.getMonth()]

                if (startDate.getMonth() === endDate.getMonth()) {
                  return `${startMonth} ${startDate.getDate()}-${endDate.getDate()}, ${currentYear}`
                } else {
                  return `${startMonth} ${startDate.getDate()} - ${endMonth} ${endDate.getDate()}, ${currentYear}`
                }
              })()
          }
        </h3>

        {/* Right: Navigation Buttons */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('prev')}
            className="p-2"
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('next')}
            className="p-2"
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </div>

      {/* Calendar Grid - Scandinavian Clean */}
      {view === 'month' ? (
        <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 overflow-hidden">
          {/* Week Days Header */}
          <div className="grid grid-cols-7 border-b border-stone-200 dark:border-stone-700">
            {weekDays.map((day) => (
              <div
                key={day}
                className="p-3 text-center text-sm font-normal text-stone-600 dark:text-stone-400 bg-stone-50 dark:bg-stone-700 uppercase tracking-wider"
              >
                {day}
              </div>
            ))}
          </div>

          {/* Calendar Days - Apple Calendar Style */}
          <div className="grid grid-cols-7">
            {calendarDays.map((calendarDay, index) => {
              const dayEvents = getEventsForDate(calendarDay.date)
              const eventCount = dayEvents.length
              const isSelected = selectedDate?.toDateString() === calendarDay.date.toDateString()

              return (
                <div
                  key={index}
                  onClick={() => handleDateClick(calendarDay.date)}
                  className={cn(
                    'min-h-[100px] p-3 border-r border-b border-stone-200 dark:border-stone-700',
                    'hover:bg-stone-50 dark:hover:bg-stone-700 transition-all duration-200 cursor-pointer',
                    'flex flex-col items-center justify-start',
                    !calendarDay.isCurrentMonth && 'text-stone-400 dark:text-stone-500 bg-stone-50/30 dark:bg-stone-800/30',
                    isToday(calendarDay.date) && 'bg-emerald-50 dark:bg-emerald-900/20',
                    isSelected && 'bg-emerald-100 dark:bg-emerald-900/30 ring-2 ring-emerald-500/20'
                  )}
                >
                  {/* Date Number */}
                  <div className={cn(
                    'w-8 h-8 flex items-center justify-center text-sm font-normal mb-2',
                    'transition-all duration-200',
                    isToday(calendarDay.date) && 'bg-emerald-700 dark:bg-emerald-600 text-white',
                    !isToday(calendarDay.date) && calendarDay.isCurrentMonth && 'text-stone-800 dark:text-stone-200',
                    !calendarDay.isCurrentMonth && 'text-stone-400 dark:text-stone-500'
                  )}>
                    {calendarDay.date.getDate()}
                  </div>

                  {/* Event Indicators */}
                  {eventCount > 0 && (
                    <div className="flex flex-col items-center gap-1">
                      {/* Event Dots */}
                      <div className="flex gap-1">
                        {dayEvents.slice(0, 3).map((event) => (
                          <div
                            key={event.id}
                            className={cn(
                              'w-2 h-2',
                              event.priority === 'high' && 'bg-amber-700 dark:bg-amber-600',
                              event.priority === 'medium' && 'bg-emerald-700 dark:bg-emerald-600',
                              event.priority === 'low' && 'bg-stone-500 dark:bg-stone-400',
                              !event.priority && 'bg-stone-500 dark:bg-stone-400'
                            )}
                          />
                        ))}
                      </div>

                      {/* Event Count Badge */}
                      {eventCount > 3 && (
                        <div className="text-xs px-1.5 py-0.5 bg-stone-200 dark:bg-stone-600 text-stone-600 dark:text-stone-400">
                          +{eventCount - 3}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      ) : (
        /* 7-Day View with 3-Day Window - Scandinavian Clean */
        <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 overflow-hidden">
          {/* 7-Day Header with 3-day visible window */}
          <div className="relative overflow-hidden border-b border-stone-200 dark:border-stone-700 h-16">
            {/* Fixed Time column header - 确保与右侧表头高度完全对齐 */}
            <div className="absolute left-0 top-0 z-10 w-20 h-16 flex items-center justify-center text-sm font-normal text-stone-600 dark:text-stone-400 bg-stone-50 dark:bg-stone-700 border-r border-stone-200 dark:border-stone-700 uppercase tracking-wider">
              Time
            </div>

            {/* Scrollable days header */}
            <div className="ml-20 overflow-hidden h-16">
              <div
                className={cn(
                  "flex h-16",
                  isDragging ? "" : "transition-transform duration-300 ease-out"
                )}
                style={{
                  transform: `translateX(-${(weekViewOffset * (100/7)) - dragOffset}%)`,
                  width: 'calc(700% / 3)' // 7天 ÷ 3天可见，更精确的计算
                }}
              >
                {/* All 7 days headers - 统一高度和宽度 */}
                {getWeekDays(currentDate).map((day, index) => {
                  const isSelected = selectedDate?.toDateString() === day.toDateString()

                  return (
                    <div
                      key={index}
                      onClick={() => handleDateClick(day)}
                      className={cn(
                        'flex-shrink-0 p-3 text-center border-r border-stone-200 dark:border-stone-700 bg-stone-50 dark:bg-stone-700 h-16 cursor-pointer',
                        'hover:bg-stone-100 dark:hover:bg-stone-600 transition-colors duration-200',
                        isToday(day) && 'bg-emerald-50 dark:bg-emerald-900/20',
                        isSelected && 'bg-emerald-100 dark:bg-emerald-900/30 ring-2 ring-emerald-500/50'
                      )}
                      style={{ width: 'calc(100% / 7)' }} // 确保每天占据相等宽度
                    >
                      <div className="text-xs text-stone-500 dark:text-stone-400 uppercase tracking-wider">
                        {weekDays[day.getDay()]}
                      </div>
                      <div className={cn(
                        'text-sm font-normal mt-1',
                        isToday(day) && 'text-emerald-700 dark:text-emerald-600',
                        isSelected && 'text-emerald-700 dark:text-emerald-600 font-normal'
                      )}>
                        {day.getDate()}
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>

          {/* 7-Day Grid with 3-day visible window and drag support */}
          <div className="max-h-96 overflow-y-auto relative" id="calendar-grid">
            {/* Fixed Time slots column - 确保与表头对齐 */}
            <div className="absolute left-0 top-0 z-10 w-20 border-r border-stone-200 dark:border-stone-700 bg-white dark:bg-stone-800">
              {timeSlots.map((time) => (
                <div
                  key={time}
                  className="h-16 p-2 text-xs text-stone-500 dark:text-stone-400 border-b border-stone-200/50 dark:border-stone-700/50 flex items-center justify-center bg-white dark:bg-stone-800"
                >
                  {time}
                </div>
              ))}
            </div>

            {/* Scrollable days grid */}
            <div className="ml-20 overflow-hidden">
              <div
                className={cn(
                  "flex select-none",
                  isDragging ? "cursor-grabbing" : "cursor-grab",
                  isDragging ? "" : "transition-transform duration-300 ease-out"
                )}
                style={{
                  transform: `translateX(-${(weekViewOffset * (100/7)) - dragOffset}%)`,
                  width: 'calc(700% / 3)', // 7天 ÷ 3天可见，更精确的计算
                  minHeight: 'fit-content' // 确保内容高度适应
                }}
                onMouseDown={handleMouseDown}
                onMouseMove={isDragging ? handleMouseMove : undefined}
                onMouseUp={handleMouseUp}
                onMouseLeave={handleMouseUp}
                onTouchStart={handleTouchStart}
                onTouchMove={handleTouchMove}
                onTouchEnd={handleTouchEnd}
              >
                {/* All 7 days columns */}
                {getWeekDays(currentDate).map((day, dayIndex) => {
                  const dayEvents = getEventsForDate(day)

                  return (
                    <div
                      key={dayIndex}
                      data-day-date={day.toISOString().split('T')[0]} // Add date attribute for search navigation
                      className="flex-shrink-0 border-r border-stone-200 dark:border-stone-700"
                      style={{ width: 'calc(100% / 7)' }} // 确保每天列占据相等宽度
                    >
                      {timeSlots.map((time, timeIndex) => {
                        const hour = parseInt(time.split(':')[0])
                        const eventsAtTime = dayEvents.filter(event => {
                          if (!event.time) return false
                          const eventHour = parseInt(event.time.split(':')[0])
                          return eventHour === hour
                        })

                        return (
                          <div
                            key={timeIndex}
                            onClick={() => handleTimeSlotClick(day, time)}
                            className={cn(
                              'h-16 p-1 border-b border-stone-200/50 dark:border-stone-700/50 relative cursor-pointer',
                              'hover:bg-stone-50 dark:hover:bg-stone-700 transition-colors',
                              isToday(day) && 'bg-emerald-50/50 dark:bg-emerald-900/10'
                            )}
                          >
                            {eventsAtTime.map((event, eventIndex) => {
                              const isSelectedEvent = selectedEvent?.id === event.id

                              return (
                                <div
                                  key={event.id}
                                  data-event-id={event.id}
                                  onClick={(e) => handleEventClick(event, e)}
                                  className={cn(
                                    'absolute inset-x-1 text-xs px-1.5 py-0.5',
                                    'text-white font-normal cursor-pointer',
                                    'hover:shadow-md transition-all duration-200',
                                    'search-highlight',
                                    highlightedItemId === event.id && 'search-highlight-active',
                                    'overflow-hidden',
                                    event.priority === 'high' && 'bg-amber-700 dark:bg-amber-600',
                                    event.priority === 'medium' && 'bg-emerald-700 dark:bg-emerald-600',
                                    event.priority === 'low' && 'bg-stone-500 dark:bg-stone-400',
                                    !event.priority && 'bg-stone-500 dark:bg-stone-400',
                                    isSelectedEvent && 'ring-2 ring-white/50 shadow-lg scale-105 z-10'
                                  )}
                                  style={{
                                    top: `${4 + eventIndex * 18}px`, // 堆叠多个事件
                                    height: '16px',
                                    fontSize: '10px', // 减小字体
                                    lineHeight: '16px'
                                  }}
                                  title={`${event.title} at ${event.time}`}
                                >
                                  <div className="truncate">
                                    {event.title}
                                  </div>
                                </div>
                              )
                            })}
                          </div>
                        )
                      })}
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Selected Date Events - Works for both month and week views - Scandinavian Clean */}
      {selectedDate && (
        <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-4">
          <h3 className="text-lg font-normal text-stone-800 dark:text-stone-200 mb-3 uppercase tracking-wider">
            Events for {selectedDate.toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </h3>

          {(() => {
            const selectedDateEvents = getEventsForDate(selectedDate)
            return selectedDateEvents.length > 0 ? (
              <div className="space-y-2">
                {selectedDateEvents.map((event) => {
                  const isSelectedEvent = selectedEvent?.id === event.id

                  return (
                    <div
                      key={event.id}
                      data-event-id={event.id}
                      onClick={() => handleEventClick(event)}
                      className={cn(
                        'flex items-center gap-3 p-3 border cursor-pointer transition-all duration-200',
                        'hover:shadow-md hover:border-emerald-500/50',
                        'search-highlight',
                        highlightedItemId === event.id && 'search-highlight-active',
                        isSelectedEvent
                          ? 'bg-emerald-50 dark:bg-emerald-900/20 border-emerald-500 shadow-md'
                          : 'bg-stone-50 dark:bg-stone-700 border-stone-200 dark:border-stone-600'
                      )}
                    >
                      <div className={cn(
                        'w-3 h-3',
                        event.priority === 'high' && 'bg-amber-700 dark:bg-amber-600',
                        event.priority === 'medium' && 'bg-emerald-700 dark:bg-emerald-600',
                        event.priority === 'low' && 'bg-stone-500 dark:bg-stone-400',
                        !event.priority && 'bg-stone-500 dark:bg-stone-400'
                      )} />

                      <div className="flex-1">
                        <h4 className={cn(
                          'font-normal',
                          isSelectedEvent ? 'text-emerald-700 dark:text-emerald-600' : 'text-stone-800 dark:text-stone-200'
                        )}>
                          {event.title}
                        </h4>
                        {event.time && (
                          <p className="text-sm text-stone-500 dark:text-stone-400">at {event.time}</p>
                        )}
                      </div>

                      <div className={cn(
                        'text-xs px-2 py-1 uppercase tracking-wider',
                        event.priority === 'high' && 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400',
                        event.priority === 'medium' && 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400',
                        event.priority === 'low' && 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400',
                        !event.priority && 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400'
                      )}>
                        {event.priority || 'normal'}
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-6 text-stone-500 dark:text-stone-400">
                <div className="w-8 h-8 bg-stone-300 dark:bg-stone-600 mx-auto mb-2"></div>
                <p className="text-sm uppercase tracking-wider">No events on this date</p>
              </div>
            )
          })()}
        </div>
      )}

      {/* Selected Event Details - Scandinavian Clean */}
      {selectedEvent && (
        <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-4">
          <div className="flex items-start justify-between mb-4">
            <h3 className="text-lg font-normal text-stone-800 dark:text-stone-200 uppercase tracking-wider">Event Details</h3>
            <div className="flex items-center gap-1">
              {/* Compact Action Buttons */}
              <button
                onClick={() => startEditEvent(selectedEvent)}
                className="w-8 h-8 flex items-center justify-center text-stone-400 dark:text-stone-500 hover:text-emerald-600 dark:hover:text-emerald-400 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors"
                title="Edit Event"
              >
                <Edit2 className="w-4 h-4" />
              </button>
              <button
                onClick={() => confirmDeleteEvent(selectedEvent.id)}
                className="w-8 h-8 flex items-center justify-center text-stone-400 dark:text-stone-500 hover:text-amber-600 dark:hover:text-amber-400 hover:bg-amber-50 dark:hover:bg-amber-900/20 transition-colors"
                title="Delete Event"
              >
                <Trash2 className="w-4 h-4" />
              </button>
              <button
                onClick={() => setSelectedEvent(null)}
                className="w-8 h-8 flex items-center justify-center text-stone-500 dark:text-stone-400 hover:text-stone-800 dark:hover:text-stone-200 transition-colors ml-1"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* View Mode */}
            <div className="space-y-4">
              {/* Event Title and Type */}
              <div className="flex items-center gap-3">
                <div className={cn(
                  'w-4 h-4',
                  selectedEvent.priority === 'high' && 'bg-amber-700 dark:bg-amber-600',
                  selectedEvent.priority === 'medium' && 'bg-emerald-700 dark:bg-emerald-600',
                  selectedEvent.priority === 'low' && 'bg-stone-500 dark:bg-stone-400',
                  !selectedEvent.priority && 'bg-stone-500 dark:bg-stone-400'
                )} />
                <h4 className="text-xl font-normal text-stone-800 dark:text-stone-200">{selectedEvent.title}</h4>
                <span className={cn(
                  'text-xs px-2 py-1 capitalize uppercase tracking-wider',
                  selectedEvent.type === 'meeting' && 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400',
                  selectedEvent.type === 'task' && 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400',
                  selectedEvent.type === 'reminder' && 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400'
                )}>
                  {selectedEvent.type}
                </span>
              </div>

              {/* Date and Time */}
              <div className="flex items-center gap-2 text-stone-500 dark:text-stone-400">
                <div className="w-4 h-4 bg-stone-400 dark:bg-stone-500"></div>
                <span>
                  {selectedEvent.date.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                  {selectedEvent.time && ` at ${selectedEvent.time}`}
                </span>
              </div>

              {/* Priority */}
              {selectedEvent.priority && (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-stone-500 dark:text-stone-400 uppercase tracking-wider">Priority:</span>
                  <div className={cn(
                    'text-xs px-2 py-1 capitalize uppercase tracking-wider',
                    selectedEvent.priority === 'high' && 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400',
                    selectedEvent.priority === 'medium' && 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400',
                    selectedEvent.priority === 'low' && 'bg-stone-100 dark:bg-stone-700 text-stone-600 dark:text-stone-400'
                  )}>
                    {selectedEvent.priority}
                  </div>
                </div>
              )}

              {/* Description */}
              {(() => {
                const originalEntry = calendarEntries.find(e => e.id === selectedEvent.id)
                const description = originalEntry?.content

                return description ? (
                  <div>
                    <h5 className="text-sm font-normal text-stone-700 dark:text-stone-300 mb-2 uppercase tracking-wider">Description</h5>
                    <p className="text-sm text-stone-500 dark:text-stone-400 leading-relaxed">
                      {description}
                    </p>
                  </div>
                ) : null
              })()}


            </div>
        </div>
      )}

      {/* Delete Confirmation Dialog - Scandinavian Clean */}
      {deleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 animate-in fade-in duration-200">
          <div className="bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700 p-6 max-w-md mx-4 animate-in scale-in duration-200">
            <h3 className="text-lg font-normal text-stone-800 dark:text-stone-200 mb-3 uppercase tracking-wider">Delete Event</h3>
            <p className="text-stone-500 dark:text-stone-400 mb-6">
              Are you sure you want to delete this event? This action cannot be undone.
            </p>
            <div className="flex gap-3">
              <Button
                variant="secondary"
                onClick={cancelDeleteEvent}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={executeDeleteEvent}
                className="flex-1 bg-amber-700 dark:bg-amber-600 hover:bg-amber-800 dark:hover:bg-amber-700 text-white"
              >
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Upcoming Events - Scandinavian Clean */}
      <div>
        <h3 className="text-lg font-normal text-stone-800 dark:text-stone-200 mb-4 uppercase tracking-wider">
          Upcoming Events
        </h3>
        <div className="space-y-3">
          {filteredUpcomingEvents.length > 0 ? (
            filteredUpcomingEvents.slice(0, 5).map((entry) => (
              <div
                key={entry.id}
                className="flex items-center gap-3 p-3 bg-white dark:bg-stone-800 border border-stone-200 dark:border-stone-700"
              >
                <div className={cn(
                  'w-3 h-3',
                  entry.metadata.priority === 'high' && 'bg-amber-700 dark:bg-amber-600',
                  entry.metadata.priority === 'medium' && 'bg-emerald-700 dark:bg-emerald-600',
                  entry.metadata.priority === 'low' && 'bg-stone-500 dark:bg-stone-400',
                  !entry.metadata.priority && 'bg-stone-500 dark:bg-stone-400'
                )} />

                <div className="flex-1">
                  <h4 className="font-normal text-stone-800 dark:text-stone-200">{entry.title}</h4>
                  <p className="text-sm text-stone-500 dark:text-stone-400">
                    {entry.metadata.dueDate && new Date(entry.metadata.dueDate).toLocaleDateString()}
                    {entry.metadata.time && ` at ${entry.metadata.time}`}
                  </p>
                </div>

                <div className="w-4 h-4 bg-stone-400 dark:bg-stone-500"></div>
              </div>
            ))
          ) : (
            <div className="text-center py-8 text-stone-500 dark:text-stone-400">
              <div className="w-8 h-8 bg-stone-300 dark:bg-stone-600 mx-auto mb-2"></div>
              <p className="text-sm uppercase tracking-wider">No upcoming events</p>
              <p className="text-xs mt-1">Add calendar events using the magic input</p>
            </div>
          )}
        </div>
      </div>

      {/* Edit Event Modal */}
      <EditEventModal
        event={editingEvent}
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSave={saveEditedEvent}
        isLoading={isLoading}
      />
    </div>
  )
}

export default CalendarPage
