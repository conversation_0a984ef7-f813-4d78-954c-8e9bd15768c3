import React from 'react'
import <PERSON><PERSON><PERSON><PERSON> from 'react-dom/client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-router-dom'
import { <PERSON><PERSON>rovider } from '@clerk/clerk-react'
import { DEV_CONFIG } from './config/development'
import { env } from './config/environment'
import { clerkConfig, isClerkConfigured } from './config/clerk'
import App from './App.tsx'
import './index.css'

// Validate environment configuration
if (env.features.debugLogging) {
  console.log('🚀 Starting Synapse AI Notes...')
  console.log('🔧 Environment:', env.environment)
  console.log('🔐 Auth enabled:', env.clerk.enableAuth)
  console.log('🤖 AI enabled:', env.ai.enableAI)
}

// Check Clerk configuration
if (env.clerk.enableAuth && !isClerkConfigured()) {
  throw new Error("Clerk authentication is enabled but not properly configured. Please check your VITE_CLERK_PUBLISHABLE_KEY.")
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    {DEV_CONFIG.SKIP_AUTH ? (
      <BrowserRouter>
        <App />
      </BrowserRouter>
    ) : (
      <ClerkProvider
        publishableKey={env.clerk.publishableKey}
        appearance={clerkConfig.appearance}
        localization={clerkConfig.localization}
      >
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </ClerkProvider>
    )}
  </React.StrictMode>,
)
