@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light theme colors */
    --color-bg-primary: 249 250 251;
    --color-bg-secondary: 255 255 255;
    --color-bg-tertiary: 243 244 246;
    --color-text-primary: 31 41 55;
    --color-text-secondary: 107 114 128;
    --color-text-tertiary: 156 163 175;
    --color-border-primary: 229 231 235;
    --color-border-secondary: 209 213 219;
  }

  .dark {
    /* Dark theme colors */
    --color-bg-primary: 17 24 39;
    --color-bg-secondary: 31 41 55;
    --color-bg-tertiary: 55 65 81;
    --color-text-primary: 249 250 251;
    --color-text-secondary: 209 213 219;
    --color-text-tertiary: 156 163 175;
    --color-border-primary: 55 65 81;
    --color-border-secondary: 75 85 99;
  }

  * {
    @apply box-border;
  }

  html {
    @apply h-full;
  }

  body {
    @apply h-full font-sans overflow-x-hidden bg-stone-50 dark:bg-stone-900 text-stone-800 dark:text-stone-200;
    line-height: 1.6;
  }

  #root {
    @apply h-full;
  }
}

@layer components {
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    @apply w-1;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded-full;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-500;
  }
  

  
  /* Animation keyframes */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideUp {
    from {
      transform: translateX(-50%) translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateX(-50%) translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideDown {
    from {
      transform: translateX(-50%) translateY(0);
      opacity: 1;
    }
    to {
      transform: translateX(-50%) translateY(100%);
      opacity: 0;
    }
  }

  @keyframes fabToPanel {
    0% {
      transform: translateX(-50%) translateY(100%);
      opacity: 0;
    }
    100% {
      transform: translateX(-50%) translateY(0);
      opacity: 1;
    }
  }

  @keyframes panelToFab {
    0% {
      transform: translateX(-50%) translateY(0);
      opacity: 1;
    }
    100% {
      transform: translateX(-50%) translateY(100%);
      opacity: 0;
    }
  }

  @keyframes pulseRing {
    0% {
      transform: scale(0.33);
      opacity: 1;
    }
    80%, 100% {
      transform: scale(1);
      opacity: 0;
    }
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-in;
  }

  .animate-fab-to-panel {
    animation: fabToPanel 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  .animate-panel-to-fab {
    animation: panelToFab 0.3s cubic-bezier(0.36, 0, 0.66, -0.56);
  }

  .animate-pulse-ring {
    animation: pulseRing 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@layer utilities {
  /* Mobile-first container */
  .mobile-container {
    @apply w-full max-w-md mx-auto h-screen relative overflow-x-hidden bg-stone-50 dark:bg-stone-900;
  }

  /* Responsive container for larger screens */
  @media (min-width: 768px) {
    .mobile-container {
      @apply max-w-md shadow-lg;
    }
  }
  
  /* Safe area utilities for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Backdrop blur utilities */
  .backdrop-blur-glass {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }

  .dark .backdrop-blur-glass {
    background: rgba(28, 25, 23, 0.95);
  }

  /* Scandinavian Clean Animations */
  @keyframes slide-in-from-top {
    from {
      opacity: 0;
      transform: translateY(-8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slide-in-from-bottom {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scale-in {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  /* Smooth page transition without flickering */
  @keyframes page-fade-in {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  /* Enhanced fade-in for page transitions */
  .page-transition-fade {
    animation: page-fade-in 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  }

  .animate-in {
    animation-fill-mode: both;
  }

  .slide-in-from-top-2 {
    animation: slide-in-from-top 0.2s ease-out;
  }

  .slide-in-from-bottom-2 {
    animation: slide-in-from-bottom 0.2s ease-out;
  }

  .scale-in {
    animation: scale-in 0.2s ease-out;
  }

  .fade-in {
    animation: fade-in 0.2s ease-out;
  }

  /* Scroll to top animation */
  @keyframes scroll-to-top-pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
    100% {
      transform: scale(1);
    }
  }

  .scroll-to-top-pulse {
    animation: scroll-to-top-pulse 0.3s ease-out;
  }

  /* Filter tabs - no horizontal scroll */
  .filter-tabs {
    @apply flex gap-2 overflow-x-auto pb-2;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .filter-tabs::-webkit-scrollbar {
    display: none;
  }

  .filter-tabs > * {
    @apply flex-shrink-0;
  }

  /* Search highlight animations */
  .search-highlight {
    position: relative;
    transition: all 0.3s ease-in-out;
  }

  .search-highlight-active {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 51, 234, 0.15)) !important;
    border: 2px solid rgba(59, 130, 246, 0.6) !important;
    box-shadow:
      0 0 0 3px rgba(59, 130, 246, 0.3),
      0 8px 25px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-2px) scale(1.02);
    z-index: 10;
  }

  /* Fix for Notes page title color conflict */
  .search-highlight-active h3 {
    color: rgb(var(--text-primary)) !important;
  }

  .search-highlight-active .group:hover h3 {
    color: rgb(var(--text-primary)) !important;
  }

  .search-pulse {
    animation: searchPulse 0.8s ease-in-out 3;
  }

  @keyframes searchPulse {
    0% {
      box-shadow:
        0 0 0 3px rgba(59, 130, 246, 0.3),
        0 8px 25px rgba(59, 130, 246, 0.2),
        0 0 0 0 rgba(59, 130, 246, 0.6);
    }
    50% {
      box-shadow:
        0 0 0 3px rgba(59, 130, 246, 0.3),
        0 8px 25px rgba(59, 130, 246, 0.2),
        0 0 0 12px rgba(59, 130, 246, 0.2);
    }
    100% {
      box-shadow:
        0 0 0 3px rgba(59, 130, 246, 0.3),
        0 8px 25px rgba(59, 130, 246, 0.2),
        0 0 0 0 rgba(59, 130, 246, 0);
    }
  }

  /* Toast animations */
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  /* Dark mode search highlight */
  .dark .search-highlight-active {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2)) !important;
    border: 2px solid rgba(59, 130, 246, 0.7) !important;
    box-shadow:
      0 0 0 3px rgba(59, 130, 246, 0.4),
      0 8px 25px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.05) !important;
  }

  .dark .search-pulse {
    animation: searchPulseDark 0.8s ease-in-out 3;
  }

  @keyframes searchPulseDark {
    0% {
      box-shadow:
        0 0 0 3px rgba(59, 130, 246, 0.4),
        0 8px 25px rgba(59, 130, 246, 0.3),
        0 0 0 0 rgba(59, 130, 246, 0.7);
    }
    50% {
      box-shadow:
        0 0 0 3px rgba(59, 130, 246, 0.4),
        0 8px 25px rgba(59, 130, 246, 0.3),
        0 0 0 12px rgba(59, 130, 246, 0.3);
    }
    100% {
      box-shadow:
        0 0 0 3px rgba(59, 130, 246, 0.4),
        0 8px 25px rgba(59, 130, 246, 0.3),
        0 0 0 0 rgba(59, 130, 246, 0);
    }
  }
}
