<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Synapse</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }
        
        .container {
            max-width: 400px;
            width: 100%;
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        p {
            font-size: 1.1rem;
            opacity: 0.9;
            line-height: 1.6;
            margin-bottom: 2rem;
        }
        
        .features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }
        
        .feature {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
        }
        
        .feature:last-child {
            margin-bottom: 0;
        }
        
        .feature-icon {
            font-size: 1.2rem;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .status {
            margin-top: 1rem;
            font-size: 0.9rem;
            opacity: 0.7;
        }
        
        .online {
            color: #4ade80;
        }
        
        .offline {
            color: #f87171;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">🧠</div>
        <h1>You're Offline</h1>
        <p>Don't worry! Synapse works offline too. Your notes are safely stored locally and will sync when you're back online.</p>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">📝</span>
                <span>Create and edit notes offline</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔄</span>
                <span>Auto-sync when connection returns</span>
            </div>
            <div class="feature">
                <span class="feature-icon">💾</span>
                <span>All data saved locally</span>
            </div>
        </div>
        
        <button class="retry-btn" onclick="retryConnection()">
            Try Again
        </button>
        
        <div class="status">
            Connection status: <span id="status" class="offline">Offline</span>
        </div>
    </div>

    <script>
        function retryConnection() {
            if (navigator.onLine) {
                window.location.href = '/'
            } else {
                updateStatus()
            }
        }
        
        function updateStatus() {
            const statusEl = document.getElementById('status')
            if (navigator.onLine) {
                statusEl.textContent = 'Online'
                statusEl.className = 'online'
                setTimeout(() => {
                    window.location.href = '/'
                }, 1000)
            } else {
                statusEl.textContent = 'Offline'
                statusEl.className = 'offline'
            }
        }
        
        // Listen for online/offline events
        window.addEventListener('online', updateStatus)
        window.addEventListener('offline', updateStatus)
        
        // Check status on load
        updateStatus()
        
        // Auto-retry every 30 seconds
        setInterval(() => {
            if (navigator.onLine) {
                window.location.href = '/'
            }
        }, 30000)
    </script>
</body>
</html>
