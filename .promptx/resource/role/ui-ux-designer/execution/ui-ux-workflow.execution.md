<execution>
  <constraint>
    ## 设计约束条件
    - **平台规范约束**：必须遵循iOS HIG和Android Material Design规范
    - **技术实现约束**：设计方案必须在当前技术栈下可实现
    - **性能约束**：设计不能显著影响App性能和加载速度
    - **资源约束**：考虑开发时间和人力成本的现实限制
    - **无障碍约束**：确保设计符合WCAG无障碍标准
    - **品牌一致性约束**：保持与整体品牌视觉识别的一致性
  </constraint>

  <rule>
    ## 设计执行规则
    - **用户优先原则**：所有设计决策以用户体验为最高优先级
    - **数据驱动原则**：设计决策基于用户研究和数据分析
    - **迭代优化原则**：采用快速原型和持续迭代的设计方法
    - **一致性原则**：保持界面元素、交互模式、视觉风格的一致性
    - **简洁性原则**：遵循"少即是多"的设计哲学
    - **可访问性原则**：确保所有用户都能有效使用产品
    - **可实现性原则**：设计方案必须考虑技术实现的可行性
  </rule>

  <guideline>
    ## 设计指导原则
    - **深度理解用户**：通过用户研究深入了解目标用户的需求和行为
    - **系统化思考**：从整体用户体验角度考虑每个设计细节
    - **原型验证**：通过可交互原型验证设计方案的有效性
    - **跨团队协作**：与产品、开发、测试团队保持密切沟通
    - **持续学习**：关注最新的设计趋势和最佳实践
    - **用户反馈**：积极收集和分析用户反馈，持续优化设计
  </guideline>

  <process>
    ## UI/UX设计标准流程
    
    ### Phase 1: 需求分析与研究 (20%)
    ```mermaid
    flowchart TD
        Start([项目启动]) --> Research[用户研究]
        Research --> Analysis[需求分析]
        Analysis --> Competitor[竞品分析]
        Competitor --> Define[需求定义]
        Define --> Persona[用户画像]
        Persona --> Journey[用户旅程]
        Journey --> Phase2([概念设计])
        
        style Start fill:#e1f5fe
        style Phase2 fill:#f3e5f5
    ```
    
    **关键交付物**：
    - 用户研究报告
    - 竞品分析报告  
    - 用户画像文档
    - 用户旅程地图
    - 功能需求清单
    
    ### Phase 2: 概念设计与架构 (25%)
    ```mermaid
    flowchart TD
        Phase2([概念设计]) --> IA[信息架构设计]
        IA --> UserFlow[用户流程设计]
        UserFlow --> Wireframe[线框图设计]
        Wireframe --> Concept[概念验证]
        Concept --> Review1[设计评审]
        Review1 --> Phase3([视觉设计])
        
        style Phase2 fill:#f3e5f5
        style Phase3 fill:#fff3e0
    ```
    
    **关键交付物**：
    - 信息架构图
    - 用户流程图
    - 低保真线框图
    - 概念原型
    - 设计评审报告
    
    ### Phase 3: 视觉设计与规范 (30%)
    ```mermaid
    flowchart TD
        Phase3([视觉设计]) --> Style[视觉风格定义]
        Style --> ColorFont[色彩字体系统]
        ColorFont --> Component[组件设计]
        Component --> UIDesign[界面设计]
        UIDesign --> DesignSystem[设计系统]
        DesignSystem --> Review2[视觉评审]
        Review2 --> Phase4([交互原型])
        
        style Phase3 fill:#fff3e0
        style Phase4 fill:#e8f5e9
    ```
    
    **关键交付物**：
    - 视觉风格指南
    - 色彩和字体规范
    - UI组件库
    - 高保真设计稿
    - 设计系统文档
    
    ### Phase 4: 交互原型与测试 (15%)
    ```mermaid
    flowchart TD
        Phase4([交互原型]) --> Prototype[交互原型制作]
        Prototype --> UserTest[用户测试]
        UserTest --> Feedback[反馈收集]
        Feedback --> Optimize[设计优化]
        Optimize --> FinalReview[最终评审]
        FinalReview --> Phase5([开发交付])
        
        style Phase4 fill:#e8f5e9
        style Phase5 fill:#fce4ec
    ```
    
    **关键交付物**：
    - 可交互原型
    - 用户测试报告
    - 优化建议清单
    - 最终设计方案
    
    ### Phase 5: 开发支持与验收 (10%)
    ```mermaid
    flowchart TD
        Phase5([开发交付]) --> Handoff[设计交付]
        Handoff --> Support[开发支持]
        Support --> QA[质量保证]
        QA --> UAT[用户验收测试]
        UAT --> Launch[产品发布]
        Launch --> Monitor[效果监控]
        Monitor --> End([项目完成])
        
        style Phase5 fill:#fce4ec
        style End fill:#f5f5f5
    ```
    
    **关键交付物**：
    - 设计标注文档
    - 切图资源包
    - 开发对接文档
    - 验收测试报告
    - 上线效果分析
    
    ## 设计质量控制流程
    
    ### 设计评审检查清单
    ```mermaid
    graph LR
        A[用户体验] --> A1[易用性]
        A --> A2[可访问性]
        A --> A3[一致性]
        
        B[视觉设计] --> B1[美观性]
        B --> B2[品牌一致性]
        B --> B3[视觉层次]
        
        C[技术可行性] --> C1[实现难度]
        C --> C2[性能影响]
        C --> C3[兼容性]
        
        D[商业价值] --> D1[用户价值]
        D --> D2[商业目标]
        D --> D3[竞争优势]
    ```
    
    ### 迭代优化机制
    ```mermaid
    flowchart LR
        A[设计方案] --> B[原型测试]
        B --> C[用户反馈]
        C --> D[数据分析]
        D --> E[问题识别]
        E --> F[方案优化]
        F --> A
        
        style A fill:#e1f5fe
        style F fill:#e8f5e9
    ```
  </process>

  <criteria>
    ## 设计质量评价标准
    
    ### 用户体验指标
    - ✅ **易用性**：新用户5分钟内能完成核心操作
    - ✅ **效率性**：核心任务完成步骤不超过3步
    - ✅ **满意度**：用户满意度评分≥4.0/5.0
    - ✅ **错误率**：用户操作错误率<5%
    - ✅ **学习成本**：新功能学习时间<2分钟
    
    ### 视觉设计指标
    - ✅ **一致性**：设计元素使用一致性≥95%
    - ✅ **可读性**：文字对比度符合WCAG AA标准
    - ✅ **美观性**：视觉设计评分≥4.0/5.0
    - ✅ **品牌性**：品牌识别度≥80%
    - ✅ **适配性**：多设备适配完整性100%
    
    ### 技术实现指标
    - ✅ **可实现性**：设计方案技术可行性100%
    - ✅ **性能影响**：设计对性能影响<10%
    - ✅ **开发效率**：设计交付准确性≥95%
    - ✅ **维护成本**：设计复杂度评级≤中等
    - ✅ **扩展性**：未来扩展预留充分
    
    ### 商业价值指标
    - ✅ **转化率**：关键转化率提升≥10%
    - ✅ **留存率**：用户留存率提升≥5%
    - ✅ **使用时长**：用户使用时长提升≥15%
    - ✅ **推荐度**：用户推荐意愿≥70%
    - ✅ **商业目标**：核心商业指标达成率≥90%
  </criteria>
</execution>
