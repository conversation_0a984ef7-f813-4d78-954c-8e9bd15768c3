<execution>
  <constraint>
    ## Synapse项目技术约束
    - **技术栈固定**：必须使用React + Vite + TypeScript + Tailwind CSS + Zustand
    - **原型位置固定**：UI原型位于`/Users/<USER>/Documents/myProj/Synapse/html/`目录
    - **API规范约束**：必须适配已定义的后端API接口
    - **PWA要求**：必须实现完整的PWA功能，包括离线支持
    - **Cloudflare部署**：代码必须兼容Cloudflare Pages部署环境
  </constraint>

  <rule>
    ## 强制执行规则
    - **原型优先**：所有UI实现必须以HTML原型为准，不得随意修改设计
    - **TypeScript严格**：所有代码必须通过TypeScript严格模式检查
    - **组件化强制**：重复使用的UI元素必须抽象为组件
    - **状态管理统一**：全局状态必须使用Zustand，局部状态使用useState
    - **API集成规范**：所有API调用必须包含JWT认证头
  </rule>

  <guideline>
    ## 实现指导原则
    - **渐进式开发**：先静态UI，再交互逻辑，最后API集成
    - **移动优先**：采用移动优先的响应式设计策略
    - **性能优先**：关注首屏加载时间和运行时性能
    - **用户体验**：确保加载状态、错误处理、离线提示等用户体验细节
    - **代码质量**：保持代码的可读性、可维护性和可测试性
  </guideline>

  <process>
    ## UI转代码完整流程
    
    ### Phase 1: 原型分析与规划 (30分钟)
    
    ```mermaid
    flowchart TD
        A[读取HTML原型] --> B[分析页面结构]
        B --> C[识别组件边界]
        C --> D[设计状态管理]
        D --> E[规划API集成点]
        E --> F[制定实现计划]
    ```
    
    **具体步骤**：
    1. **文件清单**：列出`html/`目录下的所有原型文件
    2. **页面分析**：分析每个HTML文件的结构和功能
    3. **组件拆解**：识别可复用的UI组件
    4. **状态设计**：设计Zustand store结构
    5. **路由规划**：设计React Router路由结构
    
    ### Phase 2: 项目初始化与配置 (20分钟)
    
    ```mermaid
    graph LR
        A[Vite项目创建] --> B[依赖安装]
        B --> C[TypeScript配置]
        C --> D[Tailwind配置]
        D --> E[PWA配置]
        E --> F[目录结构]
    ```
    
    **关键配置**：
    - **vite.config.ts**：PWA插件配置
    - **tailwind.config.js**：自定义主题配置
    - **tsconfig.json**：严格模式配置
    - **src/store/**：Zustand store结构
    - **src/components/**：组件目录结构
    
    ### Phase 3: 基础组件开发 (60分钟)
    
    ```mermaid
    graph TD
        A[原子组件] --> A1[Button]
        A --> A2[Input]
        A --> A3[Icon]
        A --> A4[Loading]
        
        B[分子组件] --> B1[SearchBar]
        B --> B2[NoteCard]
        B --> B3[UserMenu]
        
        C[有机体组件] --> C1[Header]
        C --> C2[Sidebar]
        C --> C3[NoteList]
    ```
    
    **开发顺序**：
    1. **原子组件**：Button、Input、Icon等基础元素
    2. **分子组件**：组合原子组件的功能单元
    3. **有机体组件**：复杂的功能区域
    4. **模板组件**：页面布局模板
    
    ### Phase 4: 页面组件开发 (90分钟)
    
    ```mermaid
    flowchart LR
        A[登录页面] --> B[主页面]
        B --> C[笔记编辑页]
        C --> D[设置页面]
        D --> E[离线页面]
    ```
    
    **实现策略**：
    - **静态布局**：先实现静态的页面布局
    - **交互逻辑**：添加用户交互和状态变化
    - **数据绑定**：连接Zustand store
    - **路由集成**：配置React Router
    
    ### Phase 5: 状态管理集成 (45分钟)
    
    ```mermaid
    graph TD
        A[用户状态] --> A1[认证信息]
        A --> A2[用户配置]
        
        B[应用状态] --> B1[笔记列表]
        B --> B2[当前笔记]
        B --> B3[UI状态]
        
        C[离线状态] --> C1[离线队列]
        C --> C2[同步状态]
    ```
    
    **Store设计**：
    ```typescript
    interface AppState {
      // 用户相关
      user: User | null;
      isAuthenticated: boolean;
      
      // 笔记相关
      notes: Note[];
      currentNote: Note | null;
      
      // UI状态
      isLoading: boolean;
      error: string | null;
      
      // 离线相关
      offlineQueue: OfflineAction[];
      isOnline: boolean;
    }
    ```
    
    ### Phase 6: API集成 (60分钟)
    
    ```mermaid
    flowchart TD
        A[API客户端] --> B[认证拦截器]
        B --> C[错误处理]
        C --> D[离线检测]
        D --> E[数据同步]
    ```
    
    **API集成要点**：
    - **JWT认证**：所有请求自动添加Authorization头
    - **错误处理**：统一的错误处理和用户提示
    - **离线支持**：离线时将操作加入队列
    - **数据同步**：网络恢复时自动同步
    
    ### Phase 7: PWA功能实现 (45分钟)
    
    ```mermaid
    graph LR
        A[Service Worker] --> B[缓存策略]
        B --> C[离线页面]
        C --> D[安装提示]
        D --> E[更新通知]
    ```
    
    **PWA功能清单**：
    - **应用清单**：manifest.json配置
    - **Service Worker**：缓存和离线支持
    - **安装提示**：PWA安装引导
    - **离线体验**：离线时的用户界面
    - **后台同步**：数据的后台同步
    
    ### Phase 8: 测试与优化 (30分钟)
    
    ```mermaid
    flowchart LR
        A[功能测试] --> B[性能测试]
        B --> C[响应式测试]
        C --> D[PWA测试]
        D --> E[部署测试]
    ```
    
    **测试重点**：
    - **功能完整性**：所有功能按原型要求实现
    - **响应式适配**：不同设备的显示效果
    - **性能指标**：首屏加载时间、运行时性能
    - **PWA功能**：离线使用、安装体验
    - **API集成**：数据的正确获取和提交
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 功能完整性
    - ✅ UI与原型100%一致
    - ✅ 所有交互功能正常
    - ✅ API集成完全正常
    - ✅ PWA功能完整可用
    
    ### 代码质量
    - ✅ TypeScript无类型错误
    - ✅ 组件复用率>80%
    - ✅ 代码覆盖率>90%
    - ✅ 性能指标达标
    
    ### 用户体验
    - ✅ 首屏加载<3秒
    - ✅ 交互响应<100ms
    - ✅ 离线功能正常
    - ✅ 错误处理友好
    
    ### 部署就绪
    - ✅ Cloudflare Pages兼容
    - ✅ 生产环境配置正确
    - ✅ 安全配置完整
    - ✅ 监控日志完善
  </criteria>
</execution>
