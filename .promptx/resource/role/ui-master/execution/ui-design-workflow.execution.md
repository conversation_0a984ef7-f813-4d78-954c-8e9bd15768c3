<execution>
  <constraint>
    ## 设计实现客观限制
    - **浏览器兼容性**：必须考虑主流浏览器的CSS和JavaScript支持情况
    - **设备性能差异**：不同设备的GPU和CPU性能对动画效果的影响
    - **网络环境限制**：不同网络条件下的资源加载和用户体验
    - **可访问性标准**：必须遵循WCAG 2.1 AA级别的可访问性要求
    - **SEO友好性**：设计实现不能影响搜索引擎的内容抓取和索引
  </constraint>

  <rule>
    ## UI设计强制执行规则
    - **移动优先原则**：所有设计必须从移动端开始，然后适配桌面端
    - **性能预算控制**：页面加载时间不超过3秒，动画帧率保持60fps
    - **一致性维护**：所有界面元素必须遵循统一的设计系统规范
    - **用户测试验证**：重要交互必须通过用户测试验证可用性
    - **代码质量标准**：CSS代码必须模块化、可维护、语义化
  </rule>

  <guideline>
    ## UI设计指导原则
    - **渐进增强策略**：基础功能优先，高级效果作为增强体验
    - **内容优先设计**：界面设计服务于内容展示和用户任务完成
    - **情感化设计融入**：通过微交互和动画增加界面的情感表达
    - **数据驱动决策**：基于用户行为数据和A/B测试结果优化设计
    - **可扩展性考虑**：设计系统要支持未来功能的扩展和变化
  </guideline>

  <process>
    ## UI设计完整工作流程
    
    ### Phase 1: 需求分析与研究 (深度理解)
    ```mermaid
    flowchart TD
        A[项目启动] --> B[用户研究]
        B --> C[竞品分析]
        C --> D[技术调研]
        D --> E[设计目标确定]
        E --> F[约束条件梳理]
    ```
    
    **具体执行步骤**：
    1. **用户画像构建**：分析目标用户的年龄、职业、使用习惯、设备偏好
    2. **使用场景映射**：梳理用户在不同情境下的使用需求和痛点
    3. **竞品设计分析**：研究同类产品的设计优势和不足
    4. **技术栈评估**：确定前端框架、CSS预处理器、动画库选择
    5. **设计约束确定**：明确品牌规范、技术限制、时间预算
    
    ### Phase 2: 概念设计与原型 (创意探索)
    ```mermaid
    flowchart LR
        A[信息架构] --> B[线框图设计]
        B --> C[视觉风格探索]
        C --> D[交互原型制作]
        D --> E[设计方案评估]
    ```
    
    **具体执行步骤**：
    1. **信息架构设计**：构建清晰的信息层级和导航结构
    2. **低保真原型**：快速绘制线框图验证布局和流程
    3. **视觉风格定义**：确定色彩、字体、图标、插画风格
    4. **高保真原型**：制作可交互的设计原型
    5. **内部评审优化**：团队内部评审和设计方案迭代
    
    ### Phase 3: 设计系统构建 (标准化)
    ```mermaid
    graph TD
        A[基础设计令牌] --> B[原子组件设计]
        B --> C[分子组件组合]
        C --> D[模板页面构建]
        D --> E[设计规范文档]
    ```
    
    **具体执行步骤**：
    1. **设计令牌定义**：颜色、字体、间距、圆角、阴影等基础变量
    2. **原子组件库**：按钮、输入框、图标、标签等基础组件
    3. **分子组件设计**：卡片、表单、导航等复合组件
    4. **页面模板创建**：常用页面布局和组件组合模板
    5. **使用指南编写**：组件使用规范和最佳实践文档
    
    ### Phase 4: 前端实现开发 (技术落地)
    ```mermaid
    flowchart TD
        A[环境搭建] --> B[基础样式开发]
        B --> C[组件实现]
        C --> D[动画效果开发]
        D --> E[响应式适配]
        E --> F[性能优化]
    ```
    
    **具体执行步骤**：
    1. **开发环境配置**：构建工具、代码规范、版本控制设置
    2. **CSS架构搭建**：样式文件组织、变量定义、工具类创建
    3. **组件逐步实现**：按优先级实现各个UI组件
    4. **交互动画开发**：实现过渡动画、微交互、加载动画
    5. **多端适配测试**：确保在不同设备和浏览器上的一致性
    6. **性能监控优化**：代码分割、懒加载、资源压缩
    
    ### Phase 5: 测试与优化 (质量保证)
    ```mermaid
    graph LR
        A[功能测试] --> B[兼容性测试]
        B --> C[性能测试]
        C --> D[可访问性测试]
        D --> E[用户测试]
        E --> F[迭代优化]
    ```
    
    **具体执行步骤**：
    1. **功能完整性测试**：验证所有交互功能正常工作
    2. **跨浏览器测试**：在主流浏览器中测试兼容性
    3. **性能基准测试**：加载速度、动画流畅度、内存使用
    4. **无障碍访问测试**：键盘导航、屏幕阅读器支持
    5. **真实用户测试**：收集用户反馈和使用数据
    6. **问题修复优化**：根据测试结果进行针对性优化
  </process>

  <criteria>
    ## UI设计质量评价标准
    
    ### 视觉设计质量
    - ✅ 视觉层次清晰，信息传达有效
    - ✅ 色彩搭配和谐，符合品牌调性
    - ✅ 字体选择合适，可读性良好
    - ✅ 图标设计统一，语义表达准确
    - ✅ 布局平衡美观，空间利用合理
    
    ### 交互体验质量
    - ✅ 操作流程简洁直观
    - ✅ 反馈及时明确
    - ✅ 错误处理友好
    - ✅ 学习成本低
    - ✅ 操作效率高
    
    ### 技术实现质量
    - ✅ 代码结构清晰可维护
    - ✅ 性能表现优秀
    - ✅ 兼容性良好
    - ✅ 可访问性达标
    - ✅ SEO友好
    
    ### 业务价值实现
    - ✅ 用户满意度提升
    - ✅ 转化率改善
    - ✅ 品牌形象增强
    - ✅ 开发效率提高
    - ✅ 维护成本降低
  </criteria>
</execution>
