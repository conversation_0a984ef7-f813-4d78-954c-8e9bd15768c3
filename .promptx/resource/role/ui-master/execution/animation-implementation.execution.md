<execution>
  <constraint>
    ## 动画实现客观限制
    - **性能预算约束**：动画必须保持60fps，避免掉帧和卡顿
    - **电池续航考虑**：移动设备上的动画不能过度消耗电池
    - **网络带宽限制**：动画资源大小要控制在合理范围内
    - **硬件加速支持**：必须考虑不同设备的GPU加速能力差异
    - **可访问性要求**：支持用户的动画偏好设置（减少动画）
  </constraint>

  <rule>
    ## 动画实现强制规则
    - **GPU加速优先**：优先使用transform和opacity属性实现动画
    - **时长控制标准**：微交互动画200-500ms，页面转场300-800ms
    - **缓动函数规范**：使用符合物理直觉的缓动曲线
    - **性能监控必须**：所有动画必须通过性能测试验证
    - **降级策略必备**：低性能设备必须有动画降级方案
  </rule>

  <guideline>
    ## 动画设计指导原则
    - **有意义的动画**：每个动画都要有明确的功能目的
    - **连续性保持**：动画要维持界面元素的空间连续性
    - **反馈及时性**：用户操作后的动画反馈要即时响应
    - **情感化表达**：通过动画传达品牌个性和情感氛围
    - **渐进增强**：动画作为体验增强，不影响基础功能
  </guideline>

  <process>
    ## 动画实现完整流程
    
    ### Phase 1: 动画策略规划 (概念设计)
    ```mermaid
    flowchart TD
        A[动画需求分析] --> B[动画类型分类]
        B --> C[性能预算分配]
        C --> D[技术方案选择]
        D --> E[动画规范制定]
    ```
    
    **动画类型分类**：
    1. **微交互动画**：按钮悬停、点击反馈、状态切换
    2. **页面转场动画**：路由切换、模态框显示、页面滚动
    3. **数据可视化动画**：图表动画、数据变化、进度展示
    4. **装饰性动画**：背景动效、粒子效果、氛围营造
    5. **功能性动画**：加载动画、引导动画、错误提示
    
    **技术方案选择矩阵**：
    | 动画复杂度 | CSS Transitions | CSS Animations | JavaScript | 动画库 |
    |------------|----------------|----------------|------------|--------|
    | 简单状态变化 | ✅ 首选 | ⚠️ 可选 | ❌ 过度 | ❌ 过度 |
    | 复杂序列动画 | ❌ 不足 | ✅ 适合 | ✅ 适合 | ✅ 推荐 |
    | 交互式动画 | ❌ 不足 | ❌ 不足 | ✅ 必须 | ✅ 推荐 |
    | 物理动画 | ❌ 不支持 | ❌ 不支持 | ⚠️ 复杂 | ✅ 首选 |
    
    ### Phase 2: 动画原型设计 (效果验证)
    ```mermaid
    graph LR
        A[动画草图] --> B[原型制作]
        B --> C[时序调整]
        C --> D[缓动优化]
        D --> E[效果验证]
    ```
    
    **原型制作工具链**：
    1. **Figma/Principle**：界面动画原型设计
    2. **After Effects**：复杂动画效果设计
    3. **Lottie**：矢量动画导出和优化
    4. **CodePen**：CSS/JS动画快速验证
    5. **Framer**：高保真交互原型
    
    ### Phase 3: 动画技术实现 (代码开发)
    ```mermaid
    flowchart TD
        A[基础动画系统] --> B[组件动画实现]
        B --> C[页面转场开发]
        C --> D[性能优化调整]
        D --> E[兼容性处理]
    ```
    
    **CSS动画最佳实践**：
    ```css
    /* GPU加速优化 */
    .animated-element {
      will-change: transform, opacity;
      transform: translateZ(0); /* 强制GPU加速 */
    }
    
    /* 高性能动画属性 */
    .fade-in {
      animation: fadeIn 0.3s ease-out forwards;
    }
    
    @keyframes fadeIn {
      from { 
        opacity: 0; 
        transform: translateY(20px); 
      }
      to { 
        opacity: 1; 
        transform: translateY(0); 
      }
    }
    
    /* 响应用户偏好 */
    @media (prefers-reduced-motion: reduce) {
      .animated-element {
        animation: none;
        transition: none;
      }
    }
    ```
    
    **JavaScript动画框架选择**：
    ```javascript
    // Framer Motion (React)
    import { motion } from 'framer-motion'
    
    const variants = {
      hidden: { opacity: 0, y: 20 },
      visible: { opacity: 1, y: 0 }
    }
    
    // GSAP (通用)
    gsap.timeline()
      .from('.element', { duration: 0.5, y: 50, opacity: 0 })
      .to('.element', { duration: 0.3, scale: 1.1 })
      .to('.element', { duration: 0.2, scale: 1 })
    ```
    
    ### Phase 4: 性能优化与测试 (质量保证)
    ```mermaid
    graph TD
        A[性能基准测试] --> B[动画性能分析]
        B --> C[优化策略实施]
        C --> D[兼容性测试]
        D --> E[用户体验验证]
    ```
    
    **性能优化策略**：
    1. **减少重排重绘**：避免修改layout属性，优先使用transform
    2. **合理使用will-change**：提前告知浏览器动画属性
    3. **动画分层管理**：复杂动画拆分为多个简单动画
    4. **资源预加载**：关键动画资源提前加载
    5. **动画池管理**：复用动画实例，避免频繁创建销毁
    
    **性能监控指标**：
    ```javascript
    // 帧率监控
    let lastTime = performance.now()
    let frameCount = 0
    
    function measureFPS() {
      frameCount++
      const currentTime = performance.now()
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        console.log(`FPS: ${fps}`)
        frameCount = 0
        lastTime = currentTime
      }
      requestAnimationFrame(measureFPS)
    }
    ```
  </process>

  <criteria>
    ## 动画质量评价标准
    
    ### 性能表现
    - ✅ 动画帧率稳定在60fps
    - ✅ CPU使用率保持在合理范围
    - ✅ 内存占用无明显泄漏
    - ✅ 电池消耗控制在可接受水平
    - ✅ 网络资源加载优化
    
    ### 用户体验
    - ✅ 动画时机恰当自然
    - ✅ 动画时长符合用户期望
    - ✅ 缓动曲线符合物理直觉
    - ✅ 动画反馈及时明确
    - ✅ 支持用户动画偏好设置
    
    ### 技术实现
    - ✅ 代码结构清晰可维护
    - ✅ 动画系统模块化设计
    - ✅ 兼容性覆盖主流浏览器
    - ✅ 降级策略完善
    - ✅ 调试和监控工具完备
    
    ### 设计一致性
    - ✅ 动画风格统一协调
    - ✅ 时序节奏保持一致
    - ✅ 缓动函数规范统一
    - ✅ 动画语义表达准确
    - ✅ 品牌个性体现充分
  </criteria>
</execution>
