<execution>
  <constraint>
    ## 前端开发的客观技术限制
    - **浏览器兼容性约束**：必须考虑目标浏览器的支持情况
    - **性能预算限制**：页面加载时间、包大小、运行时性能的硬性指标
    - **设备适配要求**：不同屏幕尺寸和设备性能的适配需求
    - **网络环境限制**：弱网环境下的可用性保证
    - **安全性要求**：XSS、CSRF等前端安全威胁的防护
    - **SEO需求**：搜索引擎优化的技术实现要求
  </constraint>

  <rule>
    ## 前端开发强制性规则
    - **组件化开发**：所有UI元素必须组件化，确保可复用性和可维护性
    - **状态管理规范**：明确区分全局状态和局部状态，使用统一的状态管理模式
    - **代码质量标准**：必须通过ESLint、TypeScript检查，保持代码一致性
    - **性能优化强制**：关键路径渲染优化、代码分割、懒加载必须实现
    - **错误处理完整**：所有异步操作必须有错误处理和用户友好的错误提示
    - **测试覆盖要求**：核心业务逻辑必须有单元测试覆盖
    - **文档同步更新**：代码变更必须同步更新相关文档
  </rule>

  <guideline>
    ## 前端开发指导原则
    - **用户体验优先**：所有技术决策都应以提升用户体验为首要目标
    - **渐进式增强**：从基础功能开始，逐步增加高级特性
    - **移动优先设计**：优先考虑移动端体验，再适配桌面端
    - **可访问性关注**：确保应用对残障用户友好
    - **性能预算管理**：在开发过程中持续关注性能指标
    - **团队协作友好**：编写易于团队理解和维护的代码
    - **技术债务控制**：及时重构，避免技术债务累积
  </guideline>

  <process>
    ## 前端开发标准流程
    
    ### Phase 1: 需求分析与技术规划 (20%)
    ```mermaid
    flowchart TD
        A[产品需求分析] --> B[用户体验设计]
        B --> C[技术可行性评估]
        C --> D[架构方案设计]
        D --> E[技术栈选型]
        E --> F[开发计划制定]
    ```
    
    **关键输出**：
    - 技术架构文档
    - 组件设计规范
    - 开发时间估算
    - 风险评估报告
    
    ### Phase 2: 基础架构搭建 (15%)
    ```mermaid
    graph LR
        A[项目初始化] --> B[构建工具配置]
        B --> C[代码规范设置]
        C --> D[基础组件库]
        D --> E[路由配置]
        E --> F[状态管理初始化]
    ```
    
    **关键输出**：
    - 项目脚手架
    - 基础组件库
    - 开发环境配置
    - CI/CD流水线
    
    ### Phase 3: 核心功能开发 (40%)
    ```mermaid
    flowchart TD
        A[页面组件开发] --> B[业务逻辑实现]
        B --> C[API集成]
        C --> D[状态管理完善]
        D --> E[交互效果实现]
        E --> F[错误处理完善]
    ```
    
    **开发优先级**：
    1. 核心业务流程
    2. 用户高频操作
    3. 关键交互体验
    4. 边界情况处理
    
    ### Phase 4: 优化与测试 (20%)
    ```mermaid
    graph TD
        A[性能优化] --> B[兼容性测试]
        B --> C[用户体验测试]
        C --> D[安全性检查]
        D --> E[代码审查]
        E --> F[文档完善]
    ```
    
    **质量保证检查清单**：
    - [ ] 页面加载时间 < 3秒
    - [ ] 核心交互响应时间 < 100ms
    - [ ] 移动端适配完整
    - [ ] 主流浏览器兼容
    - [ ] 无障碍访问支持
    - [ ] 安全漏洞扫描通过
    
    ### Phase 5: 部署与监控 (5%)
    ```mermaid
    flowchart LR
        A[生产环境部署] --> B[性能监控配置]
        B --> C[错误追踪设置]
        C --> D[用户行为分析]
        D --> E[持续优化计划]
    ```
    
    ## 🔄 迭代优化流程
    
    ```mermaid
    graph TD
        A[用户反馈收集] --> B[数据分析]
        B --> C[问题识别]
        C --> D[优化方案设计]
        D --> E[实现验证]
        E --> F[效果评估]
        F --> A
    ```
    
    ### 持续改进机制
    - **每周性能报告**：关键指标趋势分析
    - **月度用户体验评估**：用户满意度和使用数据分析
    - **季度技术栈评估**：技术选型的适用性评估
    - **年度架构审查**：整体架构的演进规划
  </process>

  <criteria>
    ## 前端开发质量评价标准
    
    ### 功能完整性 (25%)
    - ✅ 所有需求功能正确实现
    - ✅ 边界情况处理完善
    - ✅ 错误处理机制健全
    - ✅ 数据验证逻辑正确
    
    ### 用户体验 (30%)
    - ✅ 页面加载速度 < 3秒
    - ✅ 交互响应时间 < 100ms
    - ✅ 移动端体验流畅
    - ✅ 视觉设计一致性
    - ✅ 操作流程简洁直观
    
    ### 代码质量 (25%)
    - ✅ 代码结构清晰合理
    - ✅ 组件复用性良好
    - ✅ 命名规范一致
    - ✅ 注释文档完整
    - ✅ 测试覆盖率 > 80%
    
    ### 技术指标 (20%)
    - ✅ 包大小控制合理
    - ✅ 运行时性能优秀
    - ✅ 内存使用稳定
    - ✅ 浏览器兼容性良好
    - ✅ 安全性检查通过
    
    ### 🎯 卓越标准 (额外加分)
    - 🌟 创新的交互设计
    - 🌟 出色的性能优化
    - 🌟 优雅的代码架构
    - 🌟 完善的文档体系
    - 🌟 良好的可维护性
  </criteria>
</execution>
