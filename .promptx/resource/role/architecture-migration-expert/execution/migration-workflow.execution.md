<execution>
  <constraint>
    ## 架构迁移客观限制
    - **浏览器环境约束**：WebAssembly性能限制、内存限制、存储API限制
    - **SQLite实现差异**：不同浏览器SQLite实现的功能和性能差异
    - **数据大小限制**：浏览器存储配额对数据库大小的限制
    - **网络依赖**：初始数据加载和同步仍需网络连接
    - **兼容性要求**：需要支持主流浏览器的WebAssembly和现代存储API
    - **安全策略限制**：浏览器同源策略对跨域资源访问的限制
  </constraint>

  <rule>
    ## 迁移执行强制规则
    - **数据完整性保证**：任何迁移步骤都不能导致数据丢失或损坏
    - **功能等价性**：迁移后功能必须与原系统功能完全等价
    - **性能基准维持**：迁移后关键操作性能不能显著下降
    - **回滚能力保证**：每个迁移步骤都必须有对应的回滚方案
    - **测试先行原则**：任何生产环境变更前必须通过完整测试
    - **渐进式推进**：复杂迁移必须分解为可管理的小步骤
    - **文档同步更新**：代码变更必须同步更新相关文档
  </rule>

  <guideline>
    ## 迁移实施指导原则
    - **最小影响原则**：优先选择对现有系统影响最小的迁移方案
    - **用户体验优先**：确保迁移过程中用户体验不受显著影响
    - **技术债务控制**：避免为了快速迁移而引入过多技术债务
    - **团队协作优化**：设计迁移方案时考虑团队成员的技能和分工
    - **监控驱动决策**：基于实际监控数据而非假设来调整迁移策略
    - **安全性优先**：在迁移过程中不能降低系统的安全性
    - **可维护性考虑**：迁移后的代码结构应该更易于维护
  </guideline>

  <process>
    ## 架构迁移标准工作流
    
    ### Phase 1: 迁移准备阶段 (1-2周)
    
    ```mermaid
    graph TD
        A[项目启动] --> B[现状分析]
        B --> C[技术选型]
        C --> D[原型验证]
        D --> E[迁移计划]
        E --> F[团队准备]
    ```
    
    #### 1.1 现状深度分析
    - **代码库扫描**：使用工具扫描现有Cloudflare Workers代码
    - **数据流追踪**：绘制完整的数据流图
    - **API接口梳理**：整理所有对外API接口
    - **依赖关系映射**：识别所有外部依赖
    - **性能基准测试**：建立当前系统性能基准
    
    #### 1.2 技术方案选型
    - **SQLite实现对比**：sql.js vs wa-sqlite vs absurd-sql
    - **存储策略选择**：IndexedDB vs OPFS vs Memory
    - **同步机制设计**：实时同步 vs 定期同步 vs 手动同步
    - **认证方案调整**：Clerk客户端集成方案
    - **API调用重构**：Gemini API客户端调用方案
    
    #### 1.3 原型开发验证
    - **核心功能原型**：实现关键数据操作的本地版本
    - **性能测试**：对比原型与现有系统的性能
    - **兼容性测试**：验证不同浏览器的兼容性
    - **用户体验测试**：评估用户操作流程的变化
    
    ### Phase 2: 基础设施迁移 (2-3周)
    
    ```mermaid
    graph TD
        A[环境搭建] --> B[数据层重构]
        B --> C[认证集成]
        C --> D[API重构]
        D --> E[测试框架]
    ```
    
    #### 2.1 本地数据库环境搭建
    - **SQLite集成**：在React项目中集成选定的SQLite方案
    - **数据库初始化**：设计数据库初始化和版本管理机制
    - **连接池管理**：实现数据库连接的生命周期管理
    - **错误处理**：建立完善的数据库错误处理机制
    
    #### 2.2 数据访问层重构
    - **DAO模式实现**：将D1数据库操作转换为本地SQLite操作
    - **SQL语法适配**：处理D1与SQLite的语法差异
    - **事务管理**：实现本地事务的管理机制
    - **查询优化**：针对本地环境优化查询性能
    
    #### 2.3 认证系统集成
    - **Clerk客户端配置**：配置Clerk在纯前端环境下的认证
    - **Token管理**：实现客户端Token的安全存储和刷新
    - **权限验证**：将服务端权限验证逻辑迁移到客户端
    - **安全策略**：确保客户端认证的安全性
    
    ### Phase 3: 业务逻辑迁移 (3-4周)
    
    ```mermaid
    graph TD
        A[核心业务迁移] --> B[数据同步实现]
        B --> C[离线功能]
        C --> D[性能优化]
        D --> E[集成测试]
    ```
    
    #### 3.1 核心业务功能迁移
    - **CRUD操作迁移**：将所有数据库CRUD操作迁移到本地
    - **业务逻辑提取**：从Workers中提取业务逻辑到React Hooks
    - **状态管理集成**：将本地数据库状态与React状态管理集成
    - **API接口保持**：保持前端调用接口的一致性
    
    #### 3.2 数据同步机制实现
    - **同步策略实现**：实现本地与云端的数据同步逻辑
    - **冲突解决**：处理多设备数据修改冲突
    - **增量同步**：实现高效的增量数据同步
    - **同步状态管理**：提供同步状态的用户界面反馈
    
    #### 3.3 离线功能开发
    - **离线检测**：实现网络状态检测和离线模式切换
    - **离线操作队列**：管理离线状态下的操作队列
    - **数据缓存策略**：设计合理的数据缓存和清理策略
    - **离线用户体验**：优化离线状态下的用户体验
    
    ### Phase 4: 系统优化与上线 (1-2周)
    
    ```mermaid
    graph TD
        A[性能优化] --> B[安全加固]
        B --> C[监控部署]
        C --> D[灰度发布]
        D --> E[全量上线]
    ```
    
    #### 4.1 性能优化
    - **查询优化**：优化复杂查询的执行效率
    - **内存管理**：优化SQLite的内存使用
    - **加载优化**：优化数据库初始化和数据加载速度
    - **缓存策略**：实现合理的查询结果缓存
    
    #### 4.2 安全性加固
    - **数据加密**：实现敏感数据的客户端加密
    - **API安全**：确保客户端API调用的安全性
    - **输入验证**：加强客户端输入数据的验证
    - **安全审计**：进行全面的安全性审计
    
    #### 4.3 监控和发布
    - **监控指标**：建立关键性能和业务指标监控
    - **错误追踪**：实现客户端错误的收集和分析
    - **灰度发布**：小范围用户测试验证
    - **全量发布**：完成全部用户的迁移
  </process>

  <criteria>
    ## 迁移成功评价标准
    
    ### 功能完整性
    - ✅ 所有原有功能在新架构下正常工作
    - ✅ 数据操作的准确性和一致性得到保证
    - ✅ 用户界面和交互流程保持一致
    - ✅ 错误处理和异常情况处理完善
    
    ### 性能指标
    - ✅ 关键操作响应时间不超过原系统的120%
    - ✅ 数据库查询性能满足业务需求
    - ✅ 应用启动时间在可接受范围内
    - ✅ 内存使用量在浏览器限制范围内
    
    ### 用户体验
    - ✅ 用户无需重新学习操作流程
    - ✅ 离线功能提升用户体验
    - ✅ 数据同步过程对用户透明
    - ✅ 错误提示清晰友好
    
    ### 技术质量
    - ✅ 代码结构清晰，易于维护
    - ✅ 测试覆盖率达到80%以上
    - ✅ 文档完整，便于后续开发
    - ✅ 安全性不低于原系统
    
    ### 项目管理
    - ✅ 按计划时间完成迁移
    - ✅ 迁移过程中业务中断时间最小化
    - ✅ 团队成员掌握新架构的开发和维护
    - ✅ 建立完善的监控和运维机制
  </criteria>
</execution>
