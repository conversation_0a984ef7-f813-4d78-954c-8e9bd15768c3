<execution>
  <constraint>
    ## Cloudflare Workers Technical Constraints
    - **Runtime Limitations**: V8 isolates with 128MB memory limit and 50ms CPU time
    - **Bundle Size Limits**: 1MB compressed script size, 10MB uncompressed
    - **Request Limits**: 1000 subrequests per request, 6 concurrent subrequests
    - **Storage Constraints**: D1 database size limits, R2 object size limits
    - **Network Restrictions**: Outbound requests must use fetch API, no raw sockets
  </constraint>

  <rule>
    ## Mandatory Development Rules
    - **Authentication First**: Every API endpoint must validate Clerk JWT tokens
    - **Error Handling Required**: All external API calls must have proper error handling
    - **Environment Separation**: Use different secrets for development, staging, production
    - **CORS Compliance**: All endpoints must handle CORS properly for web clients
    - **Logging Standard**: Use structured logging with request IDs for traceability
    - **Rate Limiting**: Implement rate limiting for all public endpoints
  </rule>

  <guideline>
    ## Development Best Practices
    - **TypeScript First**: Use TypeScript for all Workers code with strict type checking
    - **Modular Architecture**: Separate concerns into modules (auth, db, storage, ai)
    - **Configuration Management**: Use environment variables for all configurable values
    - **Testing Strategy**: Unit tests for business logic, integration tests for APIs
    - **Performance Monitoring**: Track response times, error rates, and resource usage
    - **Security Headers**: Always include appropriate security headers in responses
  </guideline>

  <process>
    ## Cloudflare Workers Development Workflow
    
    ### Step 1: Project Setup and Configuration
    ```bash
    # Initialize new Workers project
    wrangler init my-worker --type=typescript
    
    # Configure wrangler.toml
    # Set up environment variables and secrets
    # Configure D1 database bindings
    # Configure R2 bucket bindings
    ```
    
    ### Step 2: Authentication Middleware Implementation
    ```typescript
    // Implement Clerk JWT validation
    // Create authentication middleware
    // Handle user context extraction
    // Implement role-based access control
    ```
    
    ### Step 3: Database Layer Development
    ```typescript
    // Design D1 database schema
    // Implement database connection handling
    // Create data access layer with proper error handling
    // Implement transaction management
    ```
    
    ### Step 4: API Endpoint Implementation
    ```typescript
    // Implement RESTful API endpoints
    // Add request validation and sanitization
    // Implement response formatting
    // Add comprehensive error handling
    ```
    
    ### Step 5: External Service Integration
    ```typescript
    // Integrate AI APIs (Gemini/Claude) with rate limiting
    // Implement R2 file operations with streaming
    // Add monitoring and observability
    // Implement caching strategies
    ```
    
    ### Step 6: Testing and Deployment
    ```bash
    # Run local development server
    wrangler dev
    
    # Run tests
    npm test
    
    # Deploy to staging
    wrangler deploy --env staging
    
    # Deploy to production
    wrangler deploy --env production
    ```
  </process>

  <criteria>
    ## Quality Standards
    
    ### Performance Criteria
    - ✅ API response time < 200ms for 95th percentile
    - ✅ Cold start time < 100ms
    - ✅ Memory usage < 64MB per request
    - ✅ Error rate < 0.1% for production traffic
    
    ### Security Criteria
    - ✅ All endpoints require valid authentication
    - ✅ Proper CORS configuration for all origins
    - ✅ Rate limiting prevents abuse
    - ✅ Sensitive data encrypted in transit and at rest
    
    ### Code Quality Criteria
    - ✅ TypeScript strict mode with no any types
    - ✅ 100% test coverage for business logic
    - ✅ ESLint and Prettier compliance
    - ✅ Comprehensive error handling and logging
    
    ### Deployment Criteria
    - ✅ Successful deployment to staging environment
    - ✅ All integration tests pass
    - ✅ Performance benchmarks meet requirements
    - ✅ Security scan passes without critical issues
  </criteria>
</execution>
