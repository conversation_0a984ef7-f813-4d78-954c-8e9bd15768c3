{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-01T01:58:59.748Z", "updatedAt": "2025-08-01T01:58:59.762Z", "resourceCount": 22}, "resources": [{"id": "architecture-migration-expert", "source": "project", "protocol": "role", "name": "Architecture Migration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/architecture-migration-expert/architecture-migration-expert.role.md", "metadata": {"createdAt": "2025-08-01T01:58:59.750Z", "updatedAt": "2025-08-01T01:58:59.750Z", "scannedAt": "2025-08-01T01:58:59.750Z", "path": "role/architecture-migration-expert/architecture-migration-expert.role.md"}}, {"id": "migration-workflow", "source": "project", "protocol": "execution", "name": "Migration Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/architecture-migration-expert/execution/migration-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T01:58:59.751Z", "updatedAt": "2025-08-01T01:58:59.751Z", "scannedAt": "2025-08-01T01:58:59.751Z", "path": "role/architecture-migration-expert/execution/migration-workflow.execution.md"}}, {"id": "architecture-analysis", "source": "project", "protocol": "thought", "name": "Architecture Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/architecture-migration-expert/thought/architecture-analysis.thought.md", "metadata": {"createdAt": "2025-08-01T01:58:59.751Z", "updatedAt": "2025-08-01T01:58:59.751Z", "scannedAt": "2025-08-01T01:58:59.751Z", "path": "role/architecture-migration-expert/thought/architecture-analysis.thought.md"}}, {"id": "migration-strategy", "source": "project", "protocol": "thought", "name": "Migration Strategy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/architecture-migration-expert/thought/migration-strategy.thought.md", "metadata": {"createdAt": "2025-08-01T01:58:59.752Z", "updatedAt": "2025-08-01T01:58:59.752Z", "scannedAt": "2025-08-01T01:58:59.752Z", "path": "role/architecture-migration-expert/thought/migration-strategy.thought.md"}}, {"id": "cloudflare-workers-expert", "source": "project", "protocol": "role", "name": "Cloudflare Workers Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/cloudflare-workers-expert/cloudflare-workers-expert.role.md", "metadata": {"createdAt": "2025-08-01T01:58:59.753Z", "updatedAt": "2025-08-01T01:58:59.753Z", "scannedAt": "2025-08-01T01:58:59.753Z", "path": "role/cloudflare-workers-expert/cloudflare-workers-expert.role.md"}}, {"id": "cloudflare-workflow", "source": "project", "protocol": "execution", "name": "Cloudflare Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/cloudflare-workers-expert/execution/cloudflare-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T01:58:59.754Z", "updatedAt": "2025-08-01T01:58:59.754Z", "scannedAt": "2025-08-01T01:58:59.754Z", "path": "role/cloudflare-workers-expert/execution/cloudflare-workflow.execution.md"}}, {"id": "serverless-thinking", "source": "project", "protocol": "thought", "name": "Serverless Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/cloudflare-workers-expert/thought/serverless-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T01:58:59.755Z", "updatedAt": "2025-08-01T01:58:59.755Z", "scannedAt": "2025-08-01T01:58:59.755Z", "path": "role/cloudflare-workers-expert/thought/serverless-thinking.thought.md"}}, {"id": "frontend-workflow", "source": "project", "protocol": "execution", "name": "Frontend Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/frontend-expert/execution/frontend-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T01:58:59.756Z", "updatedAt": "2025-08-01T01:58:59.756Z", "scannedAt": "2025-08-01T01:58:59.756Z", "path": "role/frontend-expert/execution/frontend-workflow.execution.md"}}, {"id": "frontend-expert", "source": "project", "protocol": "role", "name": "Frontend Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/frontend-expert/frontend-expert.role.md", "metadata": {"createdAt": "2025-08-01T01:58:59.756Z", "updatedAt": "2025-08-01T01:58:59.756Z", "scannedAt": "2025-08-01T01:58:59.756Z", "path": "role/frontend-expert/frontend-expert.role.md"}}, {"id": "frontend-thinking", "source": "project", "protocol": "thought", "name": "Frontend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/frontend-expert/thought/frontend-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T01:58:59.757Z", "updatedAt": "2025-08-01T01:58:59.757Z", "scannedAt": "2025-08-01T01:58:59.757Z", "path": "role/frontend-expert/thought/frontend-thinking.thought.md"}}, {"id": "animation-implementation", "source": "project", "protocol": "execution", "name": "Animation Implementation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-master/execution/animation-implementation.execution.md", "metadata": {"createdAt": "2025-08-01T01:58:59.758Z", "updatedAt": "2025-08-01T01:58:59.758Z", "scannedAt": "2025-08-01T01:58:59.758Z", "path": "role/ui-master/execution/animation-implementation.execution.md"}}, {"id": "ui-design-workflow", "source": "project", "protocol": "execution", "name": "Ui Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-master/execution/ui-design-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T01:58:59.758Z", "updatedAt": "2025-08-01T01:58:59.758Z", "scannedAt": "2025-08-01T01:58:59.758Z", "path": "role/ui-master/execution/ui-design-workflow.execution.md"}}, {"id": "interaction-design-thinking", "source": "project", "protocol": "thought", "name": "Interaction Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-master/thought/interaction-design-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T01:58:59.759Z", "updatedAt": "2025-08-01T01:58:59.759Z", "scannedAt": "2025-08-01T01:58:59.759Z", "path": "role/ui-master/thought/interaction-design-thinking.thought.md"}}, {"id": "ui-aesthetic-thinking", "source": "project", "protocol": "thought", "name": "Ui Aesthetic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-master/thought/ui-aesthetic-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T01:58:59.759Z", "updatedAt": "2025-08-01T01:58:59.759Z", "scannedAt": "2025-08-01T01:58:59.759Z", "path": "role/ui-master/thought/ui-aesthetic-thinking.thought.md"}}, {"id": "ui-master", "source": "project", "protocol": "role", "name": "Ui Master 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-master/ui-master.role.md", "metadata": {"createdAt": "2025-08-01T01:58:59.759Z", "updatedAt": "2025-08-01T01:58:59.759Z", "scannedAt": "2025-08-01T01:58:59.759Z", "path": "role/ui-master/ui-master.role.md"}}, {"id": "ui-to-code-workflow", "source": "project", "protocol": "execution", "name": "Ui To Code Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-to-code-expert/execution/ui-to-code-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T01:58:59.760Z", "updatedAt": "2025-08-01T01:58:59.760Z", "scannedAt": "2025-08-01T01:58:59.760Z", "path": "role/ui-to-code-expert/execution/ui-to-code-workflow.execution.md"}}, {"id": "component-design", "source": "project", "protocol": "thought", "name": "Component Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-to-code-expert/thought/component-design.thought.md", "metadata": {"createdAt": "2025-08-01T01:58:59.760Z", "updatedAt": "2025-08-01T01:58:59.760Z", "scannedAt": "2025-08-01T01:58:59.760Z", "path": "role/ui-to-code-expert/thought/component-design.thought.md"}}, {"id": "ui-analysis", "source": "project", "protocol": "thought", "name": "Ui Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-to-code-expert/thought/ui-analysis.thought.md", "metadata": {"createdAt": "2025-08-01T01:58:59.761Z", "updatedAt": "2025-08-01T01:58:59.761Z", "scannedAt": "2025-08-01T01:58:59.761Z", "path": "role/ui-to-code-expert/thought/ui-analysis.thought.md"}}, {"id": "ui-to-code-expert", "source": "project", "protocol": "role", "name": "Ui To Code Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-to-code-expert/ui-to-code-expert.role.md", "metadata": {"createdAt": "2025-08-01T01:58:59.761Z", "updatedAt": "2025-08-01T01:58:59.761Z", "scannedAt": "2025-08-01T01:58:59.761Z", "path": "role/ui-to-code-expert/ui-to-code-expert.role.md"}}, {"id": "ui-ux-workflow", "source": "project", "protocol": "execution", "name": "Ui Ux Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-ux-designer/execution/ui-ux-workflow.execution.md", "metadata": {"createdAt": "2025-08-01T01:58:59.762Z", "updatedAt": "2025-08-01T01:58:59.762Z", "scannedAt": "2025-08-01T01:58:59.762Z", "path": "role/ui-ux-designer/execution/ui-ux-workflow.execution.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-ux-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-08-01T01:58:59.762Z", "updatedAt": "2025-08-01T01:58:59.762Z", "scannedAt": "2025-08-01T01:58:59.762Z", "path": "role/ui-ux-designer/thought/design-thinking.thought.md"}}, {"id": "ui-ux-designer", "source": "project", "protocol": "role", "name": "Ui Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-ux-designer/ui-ux-designer.role.md", "metadata": {"createdAt": "2025-08-01T01:58:59.762Z", "updatedAt": "2025-08-01T01:58:59.762Z", "scannedAt": "2025-08-01T01:58:59.762Z", "path": "role/ui-ux-designer/ui-ux-designer.role.md"}}], "stats": {"totalResources": 22, "byProtocol": {"role": 6, "execution": 7, "thought": 9}, "bySource": {"project": 22}}}