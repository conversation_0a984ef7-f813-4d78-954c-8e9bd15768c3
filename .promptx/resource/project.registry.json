{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-24T11:15:21.524Z", "updatedAt": "2025-07-24T11:15:21.552Z", "resourceCount": 18}, "resources": [{"id": "cloudflare-workers-expert", "source": "project", "protocol": "role", "name": "Cloudflare Workers Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/cloudflare-workers-expert/cloudflare-workers-expert.role.md", "metadata": {"createdAt": "2025-07-24T11:15:21.526Z", "updatedAt": "2025-07-24T11:15:21.526Z", "scannedAt": "2025-07-24T11:15:21.526Z", "path": "role/cloudflare-workers-expert/cloudflare-workers-expert.role.md"}}, {"id": "cloudflare-workflow", "source": "project", "protocol": "execution", "name": "Cloudflare Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/cloudflare-workers-expert/execution/cloudflare-workflow.execution.md", "metadata": {"createdAt": "2025-07-24T11:15:21.527Z", "updatedAt": "2025-07-24T11:15:21.527Z", "scannedAt": "2025-07-24T11:15:21.527Z", "path": "role/cloudflare-workers-expert/execution/cloudflare-workflow.execution.md"}}, {"id": "serverless-thinking", "source": "project", "protocol": "thought", "name": "Serverless Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/cloudflare-workers-expert/thought/serverless-thinking.thought.md", "metadata": {"createdAt": "2025-07-24T11:15:21.527Z", "updatedAt": "2025-07-24T11:15:21.527Z", "scannedAt": "2025-07-24T11:15:21.527Z", "path": "role/cloudflare-workers-expert/thought/serverless-thinking.thought.md"}}, {"id": "frontend-workflow", "source": "project", "protocol": "execution", "name": "Frontend Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/frontend-expert/execution/frontend-workflow.execution.md", "metadata": {"createdAt": "2025-07-24T11:15:21.529Z", "updatedAt": "2025-07-24T11:15:21.529Z", "scannedAt": "2025-07-24T11:15:21.529Z", "path": "role/frontend-expert/execution/frontend-workflow.execution.md"}}, {"id": "frontend-expert", "source": "project", "protocol": "role", "name": "Frontend Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/frontend-expert/frontend-expert.role.md", "metadata": {"createdAt": "2025-07-24T11:15:21.529Z", "updatedAt": "2025-07-24T11:15:21.529Z", "scannedAt": "2025-07-24T11:15:21.529Z", "path": "role/frontend-expert/frontend-expert.role.md"}}, {"id": "frontend-thinking", "source": "project", "protocol": "thought", "name": "Frontend Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/frontend-expert/thought/frontend-thinking.thought.md", "metadata": {"createdAt": "2025-07-24T11:15:21.530Z", "updatedAt": "2025-07-24T11:15:21.530Z", "scannedAt": "2025-07-24T11:15:21.530Z", "path": "role/frontend-expert/thought/frontend-thinking.thought.md"}}, {"id": "animation-implementation", "source": "project", "protocol": "execution", "name": "Animation Implementation 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-master/execution/animation-implementation.execution.md", "metadata": {"createdAt": "2025-07-24T11:15:21.531Z", "updatedAt": "2025-07-24T11:15:21.531Z", "scannedAt": "2025-07-24T11:15:21.531Z", "path": "role/ui-master/execution/animation-implementation.execution.md"}}, {"id": "ui-design-workflow", "source": "project", "protocol": "execution", "name": "Ui Design Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-master/execution/ui-design-workflow.execution.md", "metadata": {"createdAt": "2025-07-24T11:15:21.532Z", "updatedAt": "2025-07-24T11:15:21.532Z", "scannedAt": "2025-07-24T11:15:21.532Z", "path": "role/ui-master/execution/ui-design-workflow.execution.md"}}, {"id": "interaction-design-thinking", "source": "project", "protocol": "thought", "name": "Interaction Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-master/thought/interaction-design-thinking.thought.md", "metadata": {"createdAt": "2025-07-24T11:15:21.533Z", "updatedAt": "2025-07-24T11:15:21.533Z", "scannedAt": "2025-07-24T11:15:21.533Z", "path": "role/ui-master/thought/interaction-design-thinking.thought.md"}}, {"id": "ui-aesthetic-thinking", "source": "project", "protocol": "thought", "name": "Ui Aesthetic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-master/thought/ui-aesthetic-thinking.thought.md", "metadata": {"createdAt": "2025-07-24T11:15:21.533Z", "updatedAt": "2025-07-24T11:15:21.533Z", "scannedAt": "2025-07-24T11:15:21.533Z", "path": "role/ui-master/thought/ui-aesthetic-thinking.thought.md"}}, {"id": "ui-master", "source": "project", "protocol": "role", "name": "Ui Master 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-master/ui-master.role.md", "metadata": {"createdAt": "2025-07-24T11:15:21.533Z", "updatedAt": "2025-07-24T11:15:21.533Z", "scannedAt": "2025-07-24T11:15:21.533Z", "path": "role/ui-master/ui-master.role.md"}}, {"id": "ui-to-code-workflow", "source": "project", "protocol": "execution", "name": "Ui To Code Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-to-code-expert/execution/ui-to-code-workflow.execution.md", "metadata": {"createdAt": "2025-07-24T11:15:21.534Z", "updatedAt": "2025-07-24T11:15:21.534Z", "scannedAt": "2025-07-24T11:15:21.534Z", "path": "role/ui-to-code-expert/execution/ui-to-code-workflow.execution.md"}}, {"id": "component-design", "source": "project", "protocol": "thought", "name": "Component Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-to-code-expert/thought/component-design.thought.md", "metadata": {"createdAt": "2025-07-24T11:15:21.535Z", "updatedAt": "2025-07-24T11:15:21.535Z", "scannedAt": "2025-07-24T11:15:21.535Z", "path": "role/ui-to-code-expert/thought/component-design.thought.md"}}, {"id": "ui-analysis", "source": "project", "protocol": "thought", "name": "Ui Analysis 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-to-code-expert/thought/ui-analysis.thought.md", "metadata": {"createdAt": "2025-07-24T11:15:21.535Z", "updatedAt": "2025-07-24T11:15:21.535Z", "scannedAt": "2025-07-24T11:15:21.535Z", "path": "role/ui-to-code-expert/thought/ui-analysis.thought.md"}}, {"id": "ui-to-code-expert", "source": "project", "protocol": "role", "name": "Ui To Code Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-to-code-expert/ui-to-code-expert.role.md", "metadata": {"createdAt": "2025-07-24T11:15:21.536Z", "updatedAt": "2025-07-24T11:15:21.536Z", "scannedAt": "2025-07-24T11:15:21.536Z", "path": "role/ui-to-code-expert/ui-to-code-expert.role.md"}}, {"id": "ui-ux-workflow", "source": "project", "protocol": "execution", "name": "Ui Ux Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ui-ux-designer/execution/ui-ux-workflow.execution.md", "metadata": {"createdAt": "2025-07-24T11:15:21.541Z", "updatedAt": "2025-07-24T11:15:21.541Z", "scannedAt": "2025-07-24T11:15:21.541Z", "path": "role/ui-ux-designer/execution/ui-ux-workflow.execution.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ui-ux-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-24T11:15:21.548Z", "updatedAt": "2025-07-24T11:15:21.548Z", "scannedAt": "2025-07-24T11:15:21.548Z", "path": "role/ui-ux-designer/thought/design-thinking.thought.md"}}, {"id": "ui-ux-designer", "source": "project", "protocol": "role", "name": "Ui Ux Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ui-ux-designer/ui-ux-designer.role.md", "metadata": {"createdAt": "2025-07-24T11:15:21.550Z", "updatedAt": "2025-07-24T11:15:21.550Z", "scannedAt": "2025-07-24T11:15:21.550Z", "path": "role/ui-ux-designer/ui-ux-designer.role.md"}}], "stats": {"totalResources": 18, "byProtocol": {"role": 5, "execution": 6, "thought": 7}, "bySource": {"project": 18}}}