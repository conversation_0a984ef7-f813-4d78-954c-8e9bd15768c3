# ===========================================
# SYNAPSE AI NOTES - PRODUCTION ENVIRONMENT TEMPLATE
# ===========================================
# Copy this file to .env.production and fill in your production values
# DO NOT commit .env.production to version control

# ===========================================
# CLERK AUTHENTICATION
# ===========================================
# Clerk Publishable Key (safe for client-side)
VITE_CLERK_PUBLISHABLE_KEY=your_production_clerk_publishable_key

# ===========================================
# APPLICATION CONFIGURATION
# ===========================================
# API Base URL for production (your Cloudflare Workers domain)
VITE_API_BASE_URL=https://your-app.your-subdomain.workers.dev

# Production mode flag
VITE_DEV_MODE=false

# Environment identifier
VITE_ENVIRONMENT=production

# ===========================================
# FEATURE FLAGS
# ===========================================
# Enable authentication (should be true in production)
VITE_ENABLE_AUTH=true

# Enable real AI processing (should be true in production)
VITE_ENABLE_AI=true

# Disable debug logging in production
VITE_DEBUG_LOGGING=false

# ===========================================
# CLOUDFLARE WORKERS ENVIRONMENT VARIABLES
# ===========================================
# These should be set in Cloudflare Dashboard, not in .env files
# CLERK_SECRET_KEY=your_production_clerk_secret_key
# GEMINI_API_KEY=your_production_gemini_api_key
# CLERK_JWT_KEY=your_clerk_jwt_verification_key
