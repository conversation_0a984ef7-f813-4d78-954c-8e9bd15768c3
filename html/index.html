<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synapse - AI-Native Note App</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="assets/css/themes.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/components.css">
    
    <!-- Meta Tags -->
    <meta name="description" content="Synapse - AI-powered note-taking app that automatically organizes your thoughts">
    <meta name="theme-color" content="#3B82F6">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧠</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- Optimized Compact Header -->
        <header class="header" id="appHeader">
            <div class="header-content">
                <div class="header-brand">
                    <h1>Synapse</h1>
                    <span class="subtitle">AI-Native Note App</span>
                </div>
                <div class="header-actions">
                    <button class="header-action-btn" id="themeToggle" title="Toggle Theme">
                        <i class="fas fa-moon"></i>
                    </button>
                    <button class="header-action-btn" id="searchBtn" title="Search">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="main-content">
            <div id="page-content">
                <!-- Page content will be loaded here dynamically -->
                <div class="empty-state">
                    <i class="fas fa-brain"></i>
                    <h3>Welcome to Synapse</h3>
                    <p>Loading your intelligent workspace...</p>
                </div>
            </div>
        </main>

        <!-- Magic Input System -->
        <div class="magic-fab-container" id="magicFabContainer">
            <!-- Main FAB Button -->
            <div class="magic-fab-main" id="magicFabMain">
                <div class="fab-icon-wrapper">
                    <i class="fas fa-magic fab-icon" id="fabIcon"></i>
                    <div class="ai-processing-ring" id="aiProcessingRing"></div>
                </div>
            </div>

            <!-- Unified Input Panel -->
            <div class="magic-input-panel" id="magicInputPanel">
                <div class="input-panel-header">
                    <div class="input-method-indicator" id="inputMethodIndicator">
                        <i class="fas fa-layer-group"></i>
                        <span>Mixed Content</span>
                    </div>
                    <div class="content-counter" id="contentCounter">
                        <span class="counter-badge">0</span>
                    </div>
                    <button class="panel-close-btn" id="panelCloseBtn">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Large Unified Content Preview Area -->
                <div class="unified-content-area-large" id="unifiedContentArea">
                    <!-- Input Method Quick Actions with Image Options -->
                    <div class="quick-input-actions">
                        <div class="input-actions-left">
                            <button class="quick-action-btn" id="quickTextBtn" title="Add Text">
                                <i class="fas fa-keyboard"></i>
                            </button>
                            <button class="quick-action-btn" id="quickVoiceBtn" title="Voice Input">
                                <i class="fas fa-microphone"></i>
                            </button>
                            <button class="quick-action-btn" id="quickClipboardBtn" title="Paste">
                                <i class="fas fa-clipboard"></i>
                            </button>
                            <button class="quick-action-btn" id="quickImageBtn" title="Add Image">
                                <i class="fas fa-image"></i>
                            </button>
                        </div>

                        <!-- Image Processing Options - Always Visible -->
                        <div class="image-processing-options">
                            <label class="image-option-compact" title="Extract text from document images">
                                <input type="checkbox" id="analyzeImagesCheck" checked>
                                <i class="fas fa-search"></i>
                                <span>Analyze</span>
                            </label>
                            <label class="image-option-compact" title="Attach images as reference files">
                                <input type="checkbox" id="attachImagesCheck" checked>
                                <i class="fas fa-paperclip"></i>
                                <span>Attach</span>
                            </label>
                        </div>
                    </div>

                    <!-- Content Blocks Container -->
                    <div class="content-blocks-container" id="contentBlocks">
                        <!-- Content blocks will be dynamically added here -->
                    </div>

                    <!-- Empty State -->
                    <div class="empty-content-state" id="emptyContentState">
                        <i class="fas fa-layer-group"></i>
                        <p>Start building your content</p>
                        <span>Use the icons above to add text, voice, clipboard, or images</span>
                    </div>

                    <!-- Inline Text Input (appears when text button is clicked) -->
                    <div class="inline-text-input" id="inlineTextInput" style="display: none;">
                        <textarea
                            class="inline-textarea"
                            id="inlineTextarea"
                            placeholder="Type your text here..."
                            rows="3"
                        ></textarea>
                        <div class="inline-text-actions">
                            <button class="inline-btn cancel" id="cancelTextBtn">Cancel</button>
                            <button class="inline-btn add" id="addInlineTextBtn">Add</button>
                        </div>
                    </div>

                    <!-- Voice Status (appears when recording) -->
                    <div class="voice-recording-status" id="voiceRecordingStatus" style="display: none;">
                        <div class="voice-visualizer-inline">
                            <div class="voice-wave-inline"></div>
                            <div class="voice-wave-inline"></div>
                            <div class="voice-wave-inline"></div>
                            <div class="voice-wave-inline"></div>
                        </div>
                        <span class="voice-status-text" id="voiceStatusText">Listening...</span>
                        <button class="stop-recording-btn" id="stopRecordingBtn">
                            <i class="fas fa-stop"></i>
                        </button>
                    </div>
                </div>

                <!-- Unified Content Processing Actions -->
                <div class="processing-actions-simple" id="processingActionsNormal">
                    <div class="unified-content-info">
                        <div class="content-summary" id="contentSummary">
                            <i class="fas fa-layer-group"></i>
                            <span>Ready to process unified content</span>
                        </div>
                    </div>

                    <div class="processing-buttons">
                        <button class="process-btn secondary" id="clearAllBtn">
                            <i class="fas fa-trash"></i>
                            Clear All
                        </button>
                        <button class="process-btn tertiary" id="saveUnifiedBtn" disabled>
                            <i class="fas fa-save"></i>
                            Save Draft
                        </button>
                        <button class="process-btn primary" id="processUnifiedBtn" disabled>
                            <i class="fas fa-magic"></i>
                            Process & Organize
                        </button>
                    </div>
                </div>

                <!-- Post-Processing Actions (shown after successful processing) -->
                <div class="processing-actions-simple post-processing" id="processingActionsPost" style="display: none;">
                    <div class="processing-success-message">
                        <i class="fas fa-check-circle"></i>
                        <span>Content processed successfully!</span>
                    </div>

                    <div class="processing-buttons">
                        <button class="process-btn secondary" id="saveContentBtn">
                            <i class="fas fa-save"></i>
                            Keep Content
                        </button>
                        <button class="process-btn primary" id="clearAfterProcessBtn">
                            <i class="fas fa-check"></i>
                            Done & Clear
                        </button>
                    </div>
                </div>

                <!-- Hidden File Input -->
                <input type="file" id="imageFileInput" accept="image/*" multiple hidden>

                <!-- Quick Suggestions -->
                <div class="quick-suggestions" id="quickSuggestions">
                    <div class="suggestion-chip" data-text="Schedule meeting tomorrow at 2pm">
                        <i class="fas fa-calendar"></i>
                        Meeting
                    </div>
                    <div class="suggestion-chip" data-text="Buy groceries for dinner">
                        <i class="fas fa-shopping-cart"></i>
                        Shopping
                    </div>
                    <div class="suggestion-chip" data-text="Ideas for weekend project">
                        <i class="fas fa-lightbulb"></i>
                        Ideas
                    </div>
                </div>
            </div>

            <!-- Backdrop -->
            <div class="magic-backdrop" id="magicBackdrop"></div>
        </div>

        <!-- Tab Bar Navigation -->
        <nav class="tab-bar" role="navigation" aria-label="Main navigation">
            <div class="tab-item active" data-page="inbox" role="button" tabindex="0" aria-label="Inbox">
                <i class="fas fa-inbox" aria-hidden="true"></i>
                <span>Inbox</span>
            </div>
            <div class="tab-item" data-page="calendar" role="button" tabindex="0" aria-label="Calendar">
                <i class="fas fa-calendar" aria-hidden="true"></i>
                <span>Calendar</span>
            </div>
            <div class="tab-item" data-page="todos" role="button" tabindex="0" aria-label="Todos">
                <i class="fas fa-check-square" aria-hidden="true"></i>
                <span>Todos</span>
            </div>
            <div class="tab-item" data-page="notes" role="button" tabindex="0" aria-label="Notes">
                <i class="fas fa-sticky-note" aria-hidden="true"></i>
                <span>Notes</span>
            </div>
        </nav>

        <!-- AI Processing Modal -->
        <div class="ai-processing" id="aiProcessing" role="dialog" aria-label="AI Processing">
            <div class="spinner" aria-hidden="true"></div>
            <p>AI is processing your content...</p>
        </div>
    </div>

    <!-- Scripts -->
    <script src="assets/js/theme.js"></script>
    <script src="assets/js/header.js"></script>
    <script src="assets/js/tabbar.js"></script>
    <script src="assets/js/navigation.js"></script>
    <script src="assets/js/main.js"></script>

    <!-- Service Worker Registration (for future PWA support) -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>

    <!-- Keyboard Shortcuts Help -->
    <script>
        document.addEventListener('keydown', (e) => {
            // Show help with Ctrl/Cmd + ?
            if ((e.ctrlKey || e.metaKey) && e.key === '?') {
                e.preventDefault();
                showKeyboardShortcuts();
            }
        });

        function showKeyboardShortcuts() {
            const helpModal = document.createElement('div');
            helpModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 2000;
            `;
            
            helpModal.innerHTML = `
                <div style="
                    background: var(--bg-secondary);
                    border-radius: 16px;
                    padding: 24px;
                    max-width: 400px;
                    width: 90%;
                    color: var(--text-primary);
                ">
                    <h3 style="margin-bottom: 16px; color: var(--accent-primary);">Keyboard Shortcuts</h3>
                    <div style="font-size: 14px; line-height: 1.6;">
                        <p><strong>Alt + 1-4:</strong> Switch between pages</p>
                        <p><strong>Ctrl/Cmd + Shift + T:</strong> Toggle theme</p>
                        <p><strong>Escape:</strong> Clear magic input</p>
                        <p><strong>Enter:</strong> Process magic input</p>
                        <p><strong>Ctrl/Cmd + ?:</strong> Show this help</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" style="
                        margin-top: 16px;
                        padding: 8px 16px;
                        background: var(--accent-primary);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        cursor: pointer;
                    ">Close</button>
                </div>
            `;
            
            helpModal.addEventListener('click', (e) => {
                if (e.target === helpModal) {
                    helpModal.remove();
                }
            });
            
            document.body.appendChild(helpModal);
        }
    </script>
</body>
</html>
