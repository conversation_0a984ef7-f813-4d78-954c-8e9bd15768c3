<!-- Todos Page Content -->
<div class="page-header">
    <h2 style="font-size: 24px; margin-bottom: 8px; color: var(--text-primary);">Todo List</h2>
    <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 20px;">
        Your tasks, automatically organized by AI
    </p>
</div>

<!-- Stats -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-number">3</div>
        <div class="stat-label">Today</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">7</div>
        <div class="stat-label">This Week</div>
    </div>
</div>

<!-- Today Section -->
<h3 class="section-title">Today</h3>
<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Complete design mockups for new feature</div>
    <div class="todo-priority high"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox checked"></div>
    <div class="todo-text completed">Reply to client emails</div>
    <div class="todo-priority medium"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Review code pull requests</div>
    <div class="todo-priority medium"></div>
</div>

<!-- Tomorrow Section -->
<h3 class="section-title spaced">Tomorrow</h3>
<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Attend team meeting</div>
    <div class="todo-priority low"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Prepare presentation slides</div>
    <div class="todo-priority high"></div>
</div>

<!-- This Week Section -->
<h3 class="section-title spaced">This Week</h3>
<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Finish quarterly report</div>
    <div class="todo-priority medium"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Schedule team building event</div>
    <div class="todo-priority low"></div>
</div>

<!-- Later Section -->
<h3 class="section-title spaced">Later</h3>
<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Plan vacation for next month</div>
    <div class="todo-priority low"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Research new design tools and frameworks</div>
    <div class="todo-priority medium"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Organize team building event for Q2</div>
    <div class="todo-priority low"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Update personal portfolio website</div>
    <div class="todo-priority medium"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Learn new programming language (Rust or Go)</div>
    <div class="todo-priority low"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox"></div>
    <div class="todo-text">Set up automated backup system for projects</div>
    <div class="todo-priority high"></div>
</div>

<!-- Completed Section -->
<h3 class="section-title spaced">Recently Completed</h3>
<div class="todo-item">
    <div class="todo-checkbox checked"></div>
    <div class="todo-text completed">Set up new development environment</div>
    <div class="todo-priority medium"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox checked"></div>
    <div class="todo-text completed">Complete onboarding documentation</div>
    <div class="todo-priority high"></div>
</div>

<div class="todo-item">
    <div class="todo-checkbox checked"></div>
    <div class="todo-text completed">Review and approve design system updates</div>
    <div class="todo-priority medium"></div>
</div>

<!-- Empty State -->
<div class="empty-state" id="todos-empty" style="display: none;">
    <i class="fas fa-check-square"></i>
    <h3>All caught up!</h3>
    <p>No pending tasks. Great job!</p>
</div>

<script>
(function() {
    // Todo checkbox functionality
    const todoCheckboxes = document.querySelectorAll('.todo-checkbox');
    todoCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('click', () => {
            checkbox.classList.toggle('checked');
            const todoText = checkbox.nextElementSibling;
            if (todoText) {
                todoText.classList.toggle('completed');
            }
            
            // Update stats
            updateStats();
            
            // Add completion animation
            if (checkbox.classList.contains('checked')) {
                const todoItem = checkbox.closest('.todo-item');
                todoItem.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    todoItem.style.transform = '';
                }, 200);
            }
        });
    });
    
    // Update statistics
    function updateStats() {
        const todayTodos = document.querySelectorAll('.todo-item');
        const completedTodos = document.querySelectorAll('.todo-checkbox.checked');
        
        // Update stat cards (simplified)
        const statNumbers = document.querySelectorAll('.stat-number');
        if (statNumbers.length >= 2) {
            const remainingToday = Math.max(0, 3 - completedTodos.length);
            statNumbers[0].textContent = remainingToday;
        }
    }
    
    // Priority color coding
    const priorityDots = document.querySelectorAll('.todo-priority');
    priorityDots.forEach(dot => {
        dot.addEventListener('click', () => {
            // Cycle through priorities
            if (dot.classList.contains('high')) {
                dot.className = 'todo-priority medium';
            } else if (dot.classList.contains('medium')) {
                dot.className = 'todo-priority low';
            } else {
                dot.className = 'todo-priority high';
            }
        });
    });
    
    // Drag and drop reordering (simplified)
    let draggedItem = null;
    
    const todoItems = document.querySelectorAll('.todo-item');
    todoItems.forEach(item => {
        item.draggable = true;
        
        item.addEventListener('dragstart', (e) => {
            draggedItem = item;
            item.style.opacity = '0.5';
        });
        
        item.addEventListener('dragend', () => {
            item.style.opacity = '';
            draggedItem = null;
        });
        
        item.addEventListener('dragover', (e) => {
            e.preventDefault();
        });
        
        item.addEventListener('drop', (e) => {
            e.preventDefault();
            if (draggedItem && draggedItem !== item) {
                const parent = item.parentNode;
                const draggedIndex = Array.from(parent.children).indexOf(draggedItem);
                const targetIndex = Array.from(parent.children).indexOf(item);
                
                if (draggedIndex < targetIndex) {
                    parent.insertBefore(draggedItem, item.nextSibling);
                } else {
                    parent.insertBefore(draggedItem, item);
                }
            }
        });
    });
})();
</script>
