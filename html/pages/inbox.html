<!-- Inbox Page Content -->
<div class="page-header">
    <h2 style="font-size: 24px; margin-bottom: 8px; color: var(--text-primary);">Inbox</h2>
    <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 20px;">
        All your recent content, automatically organized by AI
    </p>
</div>

<!-- Filter Tabs -->
<div class="filter-tabs">
    <div class="filter-tab active" data-filter="all">All</div>
    <div class="filter-tab" data-filter="calendar">Calendar</div>
    <div class="filter-tab" data-filter="todo">Todos</div>
    <div class="filter-tab" data-filter="note">Notes</div>
</div>

<!-- Content Stream -->
<div class="content-stream">
    <div class="content-card" data-type="calendar">
        <div class="card-header">
            <span class="card-type calendar">Calendar</span>
            <span class="card-time">2 hours ago</span>
        </div>
        <div class="card-content">
            <h3>Team Meeting</h3>
            <p>Weekly team sync scheduled for tomorrow at 2 PM to discuss new feature planning</p>
        </div>
    </div>

    <div class="content-card" data-type="todo">
        <div class="card-header">
            <span class="card-type todo">Todo</span>
            <span class="card-time">3 hours ago</span>
        </div>
        <div class="card-content">
            <h3>Complete Design</h3>
            <p>Need to finish the user interface design by Friday deadline</p>
        </div>
    </div>

    <div class="content-card" data-type="note">
        <div class="card-header">
            <span class="card-type note">Note</span>
            <span class="card-time">1 day ago</span>
        </div>
        <div class="card-content">
            <h3>AI Design Thoughts</h3>
            <p>Some ideas about how to better integrate AI into user experience design patterns...</p>
        </div>
    </div>

    <div class="content-card" data-type="calendar">
        <div class="card-header">
            <span class="card-type calendar">Calendar</span>
            <span class="card-time">2 days ago</span>
        </div>
        <div class="card-content">
            <h3>Client Presentation</h3>
            <p>Quarterly review meeting with stakeholders scheduled for Friday at 10 AM</p>
        </div>
    </div>

    <div class="content-card" data-type="todo">
        <div class="card-header">
            <span class="card-type todo">Todo</span>
            <span class="card-time">2 days ago</span>
        </div>
        <div class="card-content">
            <h3>Update Documentation</h3>
            <p>Review and update API documentation for the new authentication system</p>
        </div>
    </div>

    <div class="content-card" data-type="note">
        <div class="card-header">
            <span class="card-type note">Note</span>
            <span class="card-time">3 days ago</span>
        </div>
        <div class="card-content">
            <h3>Market Research Insights</h3>
            <p>Key findings from competitor analysis: focus on mobile-first approach and AI integration</p>
        </div>
    </div>

    <div class="content-card" data-type="calendar">
        <div class="card-header">
            <span class="card-type calendar">Calendar</span>
            <span class="card-time">4 days ago</span>
        </div>
        <div class="card-content">
            <h3>Workshop: Design Systems</h3>
            <p>Internal workshop on building scalable design systems - great insights on component libraries</p>
        </div>
    </div>

    <div class="content-card" data-type="todo">
        <div class="card-header">
            <span class="card-type todo">Todo</span>
            <span class="card-time">5 days ago</span>
        </div>
        <div class="card-content">
            <h3>Code Review</h3>
            <p>Review pull requests for the new dashboard feature implementation</p>
        </div>
    </div>

    <div class="content-card" data-type="note">
        <div class="card-header">
            <span class="card-type note">Note</span>
            <span class="card-time">1 week ago</span>
        </div>
        <div class="card-content">
            <h3>User Feedback Summary</h3>
            <p>Compiled user feedback from beta testing: users love the AI features but want more customization</p>
        </div>
    </div>
</div>

<script>
(function() {
    const filterTabs = document.querySelectorAll('.filter-tab');
    const contentCards = document.querySelectorAll('.content-card');

    filterTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            filterTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            const filter = tab.getAttribute('data-filter');
            filterContent(filter);
        });
    });

    function filterContent(filter) {
        contentCards.forEach(card => {
            const cardType = card.getAttribute('data-type');

            if (filter === 'all' || cardType === filter) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }
})();
</script>
