<!-- Notes Page Content -->
<div class="page-header">
    <h2 style="font-size: 24px; margin-bottom: 8px; color: var(--text-primary);">Notes</h2>
    <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 20px;">
        Your thoughts and ideas, beautifully organized
    </p>
</div>

<!-- Search Bar -->
<input type="text" class="search-bar" placeholder="Search notes..." id="notes-search">

<!-- Filter Tabs -->
<div class="filter-tabs">
    <div class="filter-tab active" data-filter="all">All Notes</div>
    <div class="filter-tab" data-filter="recent">Recent</div>
    <div class="filter-tab" data-filter="favorites">Favorites</div>
</div>

<!-- Notes Grid -->
<div class="notes-grid">
    <div class="note-card" data-category="work">
        <div class="note-title">AI Design Principles</div>
        <div class="note-preview">Key principles for designing AI-powered user interfaces that feel natural and intuitive...</div>
        <div class="note-meta">
            <div class="note-date">2 days ago</div>
            <div class="note-tags">
                <span class="note-tag">design</span>
                <span class="note-tag">ai</span>
            </div>
        </div>
    </div>
    
    <div class="note-card" data-category="meeting">
        <div class="note-title">Team Meeting Notes</div>
        <div class="note-preview">Discussion about new feature priorities and timeline adjustments for Q1...</div>
        <div class="note-meta">
            <div class="note-date">3 days ago</div>
            <div class="note-tags">
                <span class="note-tag">meeting</span>
                <span class="note-tag">planning</span>
            </div>
        </div>
    </div>
    
    <div class="note-card" data-category="idea">
        <div class="note-title">App Feature Ideas</div>
        <div class="note-preview">Collection of innovative features that could enhance user experience and engagement...</div>
        <div class="note-meta">
            <div class="note-date">5 days ago</div>
            <div class="note-tags">
                <span class="note-tag">ideas</span>
                <span class="note-tag">features</span>
            </div>
        </div>
    </div>
    
    <div class="note-card" data-category="technical">
        <div class="note-title">Technical Architecture</div>
        <div class="note-preview">Notes on system architecture decisions and technical implementation details...</div>
        <div class="note-meta">
            <div class="note-date">1 week ago</div>
            <div class="note-tags">
                <span class="note-tag">tech</span>
                <span class="note-tag">architecture</span>
            </div>
        </div>
    </div>
    
    <div class="note-card" data-category="personal">
        <div class="note-title">Learning Goals</div>
        <div class="note-preview">Personal development goals and learning objectives for this quarter...</div>
        <div class="note-meta">
            <div class="note-date">1 week ago</div>
            <div class="note-tags">
                <span class="note-tag">personal</span>
                <span class="note-tag">goals</span>
            </div>
        </div>
    </div>
    
    <div class="note-card" data-category="research">
        <div class="note-title">Market Research</div>
        <div class="note-preview">Competitive analysis and market trends in the AI productivity space...</div>
        <div class="note-meta">
            <div class="note-date">2 weeks ago</div>
            <div class="note-tags">
                <span class="note-tag">research</span>
                <span class="note-tag">market</span>
            </div>
        </div>
    </div>

    <div class="note-card" data-category="inspiration">
        <div class="note-title">Design Inspiration</div>
        <div class="note-preview">Collection of beautiful UI patterns and interactions from top apps...</div>
        <div class="note-meta">
            <div class="note-date">3 weeks ago</div>
            <div class="note-tags">
                <span class="note-tag">design</span>
                <span class="note-tag">inspiration</span>
            </div>
        </div>
    </div>

    <div class="note-card" data-category="code">
        <div class="note-title">Code Snippets</div>
        <div class="note-preview">Useful JavaScript utilities and React hooks for common use cases...</div>
        <div class="note-meta">
            <div class="note-date">1 month ago</div>
            <div class="note-tags">
                <span class="note-tag">code</span>
                <span class="note-tag">javascript</span>
            </div>
        </div>
    </div>

    <div class="note-card" data-category="book">
        <div class="note-title">Book Notes: Atomic Habits</div>
        <div class="note-preview">Key takeaways about building good habits and breaking bad ones...</div>
        <div class="note-meta">
            <div class="note-date">1 month ago</div>
            <div class="note-tags">
                <span class="note-tag">book</span>
                <span class="note-tag">habits</span>
            </div>
        </div>
    </div>

    <div class="note-card" data-category="project">
        <div class="note-title">Side Project Ideas</div>
        <div class="note-preview">Brainstorming session results for potential weekend projects...</div>
        <div class="note-meta">
            <div class="note-date">1 month ago</div>
            <div class="note-tags">
                <span class="note-tag">projects</span>
                <span class="note-tag">ideas</span>
            </div>
        </div>
    </div>

    <div class="note-card" data-category="travel">
        <div class="note-title">Travel Planning</div>
        <div class="note-preview">Research and itinerary for upcoming vacation to Japan...</div>
        <div class="note-meta">
            <div class="note-date">2 months ago</div>
            <div class="note-tags">
                <span class="note-tag">travel</span>
                <span class="note-tag">planning</span>
            </div>
        </div>
    </div>
</div>

<!-- Empty State -->
<div class="empty-state" id="notes-empty" style="display: none;">
    <i class="fas fa-sticky-note"></i>
    <h3>No notes found</h3>
    <p>Start capturing your thoughts with the magic input below</p>
</div>

<script>
(function() {
    const searchBar = document.getElementById('notes-search');
    const filterTabs = document.querySelectorAll('.filter-tab');
    const noteCards = document.querySelectorAll('.note-card');
    const emptyState = document.getElementById('notes-empty');
    
    // Search functionality
    searchBar.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        filterNotes(searchTerm);
    });
    
    // Filter tabs
    filterTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            filterTabs.forEach(t => t.classList.remove('active'));
            tab.classList.add('active');
            
            const filter = tab.getAttribute('data-filter');
            applyFilter(filter);
        });
    });
    
    function filterNotes(searchTerm) {
        let visibleCount = 0;
        
        noteCards.forEach(card => {
            const title = card.querySelector('.note-title').textContent.toLowerCase();
            const preview = card.querySelector('.note-preview').textContent.toLowerCase();
            const tags = Array.from(card.querySelectorAll('.note-tag'))
                .map(tag => tag.textContent.toLowerCase()).join(' ');
            
            const matches = title.includes(searchTerm) || 
                          preview.includes(searchTerm) || 
                          tags.includes(searchTerm);
            
            if (matches || searchTerm === '') {
                card.style.display = 'block';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });
        
        emptyState.style.display = visibleCount === 0 ? 'block' : 'none';
    }
    
    function applyFilter(filter) {
        let visibleCount = 0;
        
        noteCards.forEach(card => {
            let shouldShow = false;
            
            switch(filter) {
                case 'all':
                    shouldShow = true;
                    break;
                case 'recent':
                    // Show notes from last week
                    const dateText = card.querySelector('.note-date').textContent;
                    shouldShow = dateText.includes('days ago') || dateText.includes('hours ago');
                    break;
                case 'favorites':
                    // This would check for favorited notes
                    shouldShow = card.classList.contains('favorited');
                    break;
            }
            
            if (shouldShow) {
                card.style.display = 'block';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });
        
        emptyState.style.display = visibleCount === 0 ? 'block' : 'none';
    }
    
    // Note card interactions
    noteCards.forEach(card => {
        card.addEventListener('click', () => {
            // Add click animation
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = '';
            }, 150);
            
            // Could open note in detail view
            console.log('Note clicked:', card.querySelector('.note-title').textContent);
        });
        
        // Double-click to favorite
        card.addEventListener('dblclick', () => {
            card.classList.toggle('favorited');
            
            // Visual feedback
            if (card.classList.contains('favorited')) {
                card.style.borderColor = 'var(--accent-warning)';
                card.style.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)';
            } else {
                card.style.borderColor = '';
                card.style.boxShadow = '';
            }
        });
    });
    
    // Masonry layout adjustment (simplified)
    function adjustLayout() {
        // This would implement a masonry layout for better visual organization
        // For now, we'll just ensure consistent spacing
        noteCards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    }
    
    // Initialize layout
    adjustLayout();
    
    // Re-adjust on window resize
    window.addEventListener('resize', adjustLayout);
})();
</script>
