<!-- Calendar Page Content -->
<div class="calendar-header">
    <h2>January 2024</h2>
    <div class="calendar-nav">
        <button class="active">Month View</button>
        <button>Agenda</button>
    </div>
</div>

<div class="calendar-grid">
    <div class="calendar-day">Sun</div>
    <div class="calendar-day">Mon</div>
    <div class="calendar-day">Tue</div>
    <div class="calendar-day">Wed</div>
    <div class="calendar-day">Thu</div>
    <div class="calendar-day">Fri</div>
    <div class="calendar-day">Sat</div>
    
    <div class="calendar-day">31</div>
    <div class="calendar-day">1</div>
    <div class="calendar-day">2</div>
    <div class="calendar-day">3</div>
    <div class="calendar-day">4</div>
    <div class="calendar-day">5</div>
    <div class="calendar-day">6</div>
    
    <div class="calendar-day">7</div>
    <div class="calendar-day">8</div>
    <div class="calendar-day">9</div>
    <div class="calendar-day">10</div>
    <div class="calendar-day">11</div>
    <div class="calendar-day">12</div>
    <div class="calendar-day">13</div>
    
    <div class="calendar-day">14</div>
    <div class="calendar-day today">15</div>
    <div class="calendar-day has-event">16</div>
    <div class="calendar-day">17</div>
    <div class="calendar-day has-event">18</div>
    <div class="calendar-day">19</div>
    <div class="calendar-day">20</div>
    
    <div class="calendar-day">21</div>
    <div class="calendar-day">22</div>
    <div class="calendar-day">23</div>
    <div class="calendar-day">24</div>
    <div class="calendar-day">25</div>
    <div class="calendar-day">26</div>
    <div class="calendar-day">27</div>
    
    <div class="calendar-day">28</div>
    <div class="calendar-day">29</div>
    <div class="calendar-day">30</div>
    <div class="calendar-day">31</div>
    <div class="calendar-day">1</div>
    <div class="calendar-day">2</div>
    <div class="calendar-day">3</div>
</div>

<!-- Today's Agenda -->
<div class="content-card">
    <div class="card-header">
        <span class="card-type calendar">Today's Agenda</span>
    </div>
    <div class="card-content">
        <h3>Team Standup</h3>
        <p>9:00 AM - 9:30 AM • Daily sync with development team</p>
    </div>
</div>

<div class="content-card">
    <div class="card-header">
        <span class="card-type calendar">Today's Agenda</span>
    </div>
    <div class="card-content">
        <h3>Design Review</h3>
        <p>2:00 PM - 3:30 PM • Review new feature mockups with stakeholders</p>
    </div>
</div>

<div class="content-card">
    <div class="card-header">
        <span class="card-type calendar">Today's Agenda</span>
    </div>
    <div class="card-content">
        <h3>1:1 with Manager</h3>
        <p>4:00 PM - 4:30 PM • Weekly check-in and goal alignment</p>
    </div>
</div>

<div class="content-card">
    <div class="card-header">
        <span class="card-type calendar">Tomorrow</span>
    </div>
    <div class="card-content">
        <h3>Client Presentation</h3>
        <p>10:00 AM - 11:00 AM • Quarterly review with key stakeholders</p>
    </div>
</div>

<div class="content-card">
    <div class="card-header">
        <span class="card-type calendar">Tomorrow</span>
    </div>
    <div class="card-content">
        <h3>Product Planning</h3>
        <p>2:00 PM - 4:00 PM • Q2 roadmap planning session</p>
    </div>
</div>

<div class="content-card">
    <div class="card-header">
        <span class="card-type calendar">This Week</span>
    </div>
    <div class="card-content">
        <h3>Design Workshop</h3>
        <p>Thursday 1:00 PM • Team workshop on design systems</p>
    </div>
</div>

<div class="content-card">
    <div class="card-header">
        <span class="card-type calendar">This Week</span>
    </div>
    <div class="card-content">
        <h3>All Hands Meeting</h3>
        <p>Friday 3:00 PM • Company-wide updates and announcements</p>
    </div>
</div>

<!-- Empty State -->
<div class="empty-state" id="calendar-empty" style="display: none;">
    <i class="fas fa-calendar"></i>
    <h3>No events scheduled</h3>
    <p>Your calendar events will appear here when you add them via the magic input</p>
</div>

<script>
(function() {
    // Calendar navigation
    const calendarNavButtons = document.querySelectorAll('.calendar-nav button');
    calendarNavButtons.forEach(btn => {
        btn.addEventListener('click', () => {
            calendarNavButtons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Toggle between month view and agenda view
            const isAgenda = btn.textContent === 'Agenda';
            const calendarGrid = document.querySelector('.calendar-grid');
            
            if (isAgenda) {
                calendarGrid.style.display = 'none';
                // Show agenda view (could be implemented)
            } else {
                calendarGrid.style.display = 'grid';
            }
        });
    });
    
    // Calendar day selection
    const calendarDays = document.querySelectorAll('.calendar-day');
    calendarDays.forEach(day => {
        if (!isNaN(day.textContent)) {
            day.addEventListener('click', () => {
                calendarDays.forEach(d => d.classList.remove('selected'));
                day.classList.add('selected');
                
                // Could show events for selected day
                console.log('Selected day:', day.textContent);
            });
        }
    });
})();
</script>
