# Synapse - AI-Native Note App

A modular, theme-aware HTML prototype for an AI-powered note-taking application.

## 🏗️ Project Structure

```
html/
├── index.html                 # Main entry point
├── pages/                     # Individual page components
│   ├── inbox.html            # Inbox page with filtering
│   ├── calendar.html         # Calendar with month/agenda views
│   ├── todos.html            # Todo list with priorities
│   └── notes.html            # Notes grid with search
├── assets/
│   ├── css/
│   │   ├── themes.css        # Light/Dark theme system
│   │   ├── main.css          # Core application styles
│   │   └── components.css    # Component-specific styles
│   └── js/
│       ├── theme.js          # Theme management
│       ├── navigation.js     # Page routing and navigation
│       └── main.js           # Core application logic
└── components/               # Reusable components (future)
```

## ✨ Features

### 🎨 Dual Theme Support
- **Light Theme**: Clean, minimal design with bright backgrounds
- **Dark Theme**: Eye-friendly dark mode with proper contrast
- **Auto Theme**: Respects system preference
- **Theme Toggle**: Fixed button for instant switching
- **Keyboard Shortcut**: `Ctrl/Cmd + Shift + T`

### 📱 Responsive Design
- Mobile-first approach (375px base width)
- Optimized for phone screens
- Touch-friendly interactions
- Smooth animations and transitions

### 🧠 Magic FAB Input System
- **Compact Design**: Non-intrusive floating action button (FAB) that doesn't block content
- **Multiple Input Methods**:
  - 📝 **Text Input**: Expandable textarea with rich editing
  - 🎤 **Voice Input**: Speech-to-text with visual feedback
  - 📋 **Clipboard**: One-click paste from system clipboard
  - 🖼️ **Image Input**: Drag-and-drop or click to upload images
- **Expandable Interface**: FAB expands to show input options, then opens full panel
- **Smart Processing**: AI processing ring animation around FAB
- **Quick Suggestions**: Pre-defined suggestion chips for common inputs
- **Success Feedback**: Color-coded FAB animation based on content type
- **Keyboard Shortcuts**: Quick access via Ctrl+K/J/V/I

### 🗂️ Modular Architecture
- Each page is a separate HTML file
- Dynamic content loading via AJAX
- Shared CSS and JavaScript resources
- Easy to maintain and extend

## 🚀 Getting Started

1. **Open the app**: Open `html/index.html` in your browser
2. **Navigate**: Use the bottom tab bar to switch between pages
3. **Try the magic input**: Type anything in the input box and press Enter
4. **Toggle theme**: Click the theme button in the top-right corner

## ⌨️ Keyboard Shortcuts

- `Alt + 1-4`: Switch between pages (Inbox, Calendar, Todos, Notes)
- `Ctrl/Cmd + Shift + T`: Toggle light/dark theme
- `Ctrl/Cmd + ?`: Show keyboard shortcuts help
- `Escape`: Close input panel or FAB options
- `Ctrl/Cmd + K`: Open text input panel
- `Ctrl/Cmd + J`: Open voice input panel
- `Ctrl/Cmd + V`: Open clipboard input panel
- `Ctrl/Cmd + I`: Open image input panel
- `Ctrl/Cmd + Enter`: Process input (when in text area)
- `Any key`: Auto-open text input (when not focused on input)

## 🎯 Page Features

### 📥 Inbox (Enhanced)
- **Mixed Content Stream**: 8+ sample items across all categories
- **Smart Filtering**: Filter tabs for content types with real-time updates
- **Rich Content Cards**: Detailed descriptions and timestamps
- **Interactive Elements**: Hover effects and click animations

### 📅 Calendar (Enhanced)
- **Comprehensive Schedule**: Multiple daily events and upcoming items
- **Month View**: Interactive calendar grid with event indicators
- **Detailed Events**: Time slots, descriptions, and context
- **Agenda Sections**: Today, Tomorrow, This Week organization
- **Event Categories**: Different types of meetings and activities

### ✅ Todos (Enhanced)
- **Priority System**: Visual priority indicators (high/medium/low)
- **Progress Tracking**: Statistics and completion states
- **Rich Task Details**: Comprehensive task descriptions
- **Section Organization**: Today, Tomorrow, This Week, Later, Completed
- **Interactive Features**: Drag-and-drop reordering and priority cycling
- **10+ Sample Tasks**: Realistic work and personal tasks

### 📝 Notes (Enhanced)
- **Diverse Content**: 12+ note cards across different categories
- **Advanced Search**: Real-time search across titles, content, and tags
- **Rich Metadata**: Dates, categories, and tag systems
- **Content Variety**: Work notes, ideas, research, books, travel, code
- **Smart Filtering**: Filter by recency, favorites, and categories

## 🎨 Design System

### Colors (CSS Variables)
```css
/* Light Theme */
--bg-primary: #F9FAFB
--bg-secondary: #FFFFFF
--accent-primary: #3B82F6
--text-primary: #1F2937

/* Dark Theme */
--bg-primary: #111827
--bg-secondary: #1F2937
--accent-primary: #60A5FA
--text-primary: #F9FAFB
```

### Typography
- Font Family: Inter (Google Fonts)
- Weights: 300, 400, 500, 600, 700
- Responsive sizing with proper line heights

### Components
- Cards with hover effects
- Buttons with multiple variants
- Form inputs with focus states
- Tab navigation
- Filter tabs
- Priority indicators

## 🔧 Technical Details

### Theme System
- CSS custom properties for dynamic theming
- LocalStorage persistence
- System preference detection
- Smooth transitions between themes

### Navigation
- Hash-based routing
- Dynamic content loading
- Page caching for performance
- Keyboard navigation support

### AI Simulation
- Content categorization based on keywords
- Processing animations
- Dynamic content addition
- Realistic delays and feedback

## 📦 Dependencies

- **Font Awesome 6.4.0**: Icons
- **Inter Font**: Typography
- **No JavaScript frameworks**: Vanilla JS only

## 🌟 Future Enhancements

- [ ] Real AI integration
- [ ] Data persistence (localStorage/IndexedDB)
- [ ] Offline support (Service Worker)
- [ ] Push notifications
- [ ] Export functionality
- [ ] Collaboration features
- [ ] Plugin system

## 🎯 For Developers

This prototype is designed to be easily converted to any modern framework:

- **React/Vue**: Each page can become a component
- **CSS-in-JS**: Theme variables can be converted to styled-components
- **State Management**: Add Redux/Vuex for complex state
- **API Integration**: Replace simulation with real AI services
- **Mobile Apps**: Use as reference for React Native/Flutter

The modular structure and clean separation of concerns make it ideal for rapid development and iteration.
