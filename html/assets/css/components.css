/* Component Specific Styles */

/* Calendar Components */
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.calendar-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.calendar-nav {
  background: var(--bg-tertiary);
  border-radius: 12px;
  padding: 8px;
  display: flex;
}

.calendar-nav button {
  padding: 8px 16px;
  border: none;
  background: transparent;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.calendar-nav button.active {
  background: var(--accent-primary);
  color: var(--bg-secondary);
}

.calendar-nav button:hover:not(.active) {
  background: var(--bg-secondary);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 20px;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  color: var(--text-primary);
}

.calendar-day:hover {
  background: var(--bg-tertiary);
}

.calendar-day.today {
  background: var(--accent-primary);
  color: var(--bg-secondary);
  font-weight: 600;
}

.calendar-day.has-event::after {
  content: '';
  position: absolute;
  bottom: 4px;
  width: 6px;
  height: 6px;
  background: var(--accent-success);
  border-radius: 50%;
}

.calendar-day.today.has-event::after {
  background: var(--bg-secondary);
}

/* Todo Components */
.section-title {
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 600;
}

.section-title.spaced {
  margin-top: 24px;
}

.todo-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: 12px;
  margin-bottom: 12px;
  border: 1px solid var(--border-primary);
  transition: all 0.2s ease;
}

.todo-item:hover {
  box-shadow: var(--shadow-sm);
}

.todo-checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-secondary);
  border-radius: 4px;
  margin-right: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  flex-shrink: 0;
}

.todo-checkbox:hover {
  border-color: var(--accent-success);
}

.todo-checkbox.checked {
  background: var(--accent-success);
  border-color: var(--accent-success);
}

.todo-checkbox.checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.todo-text {
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
  transition: all 0.2s ease;
}

.todo-text.completed {
  text-decoration: line-through;
  color: var(--text-tertiary);
}

.todo-priority {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
  flex-shrink: 0;
}

.todo-priority.high {
  background: #EF4444;
}

.todo-priority.medium {
  background: var(--accent-warning);
}

.todo-priority.low {
  background: var(--accent-success);
}

/* Notes Components */
.notes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.note-card {
  background: var(--bg-secondary);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid var(--border-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 120px;
  display: flex;
  flex-direction: column;
}

.note-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.note-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
  line-height: 1.3;
}

.note-preview {
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
}

.note-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--border-primary);
}

.note-date {
  font-size: 11px;
  color: var(--text-tertiary);
}

.note-tags {
  display: flex;
  gap: 4px;
}

.note-tag {
  padding: 2px 6px;
  background: var(--bg-tertiary);
  border-radius: 8px;
  font-size: 10px;
  color: var(--text-secondary);
}

/* Search and Filter Components */
.search-bar {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  font-size: 14px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  margin-bottom: 20px;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.search-bar:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-bar::placeholder {
  color: var(--text-tertiary);
}

.filter-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 20px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.filter-tab {
  padding: 8px 16px;
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.filter-tab.active {
  background: var(--accent-primary);
  color: var(--bg-secondary);
  border-color: var(--accent-primary);
}

.filter-tab:hover:not(.active) {
  background: var(--bg-tertiary);
}

/* Stats Components */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.stat-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Action Buttons */
.action-button {
  padding: 12px 20px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.action-button.primary {
  background: var(--accent-primary);
  color: var(--bg-secondary);
}

.action-button.primary:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

.action-button.secondary {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.action-button.secondary:hover {
  background: var(--bg-secondary);
  box-shadow: var(--shadow-sm);
}

/* Responsive Grid Adjustments */
@media (max-width: 480px) {
  .notes-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .calendar-grid {
    gap: 4px;
  }
  
  .calendar-day {
    font-size: 12px;
  }
}
