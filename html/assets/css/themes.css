/* Theme System - Light and Dark Mode */

:root {
  /* Light Theme (Default) */
  --bg-primary: #F9FAFB;
  --bg-primary-rgb: 249, 250, 251;
  --bg-secondary: #FFFFFF;
  --bg-secondary-rgb: 255, 255, 255;
  --bg-tertiary: #F3F4F6;
  
  --text-primary: #1F2937;
  --text-secondary: #6B7280;
  --text-tertiary: #9CA3AF;
  
  --border-primary: #E5E7EB;
  --border-secondary: #D1D5DB;
  
  --accent-primary: #3B82F6;
  --accent-secondary: #60A5FA;
  --accent-success: #22C55E;
  --accent-warning: #F59E0B;
  --accent-purple: #8B5CF6;
  
  --shadow-sm: 0 2px 8px rgba(0,0,0,0.04);
  --shadow-md: 0 4px 16px rgba(0,0,0,0.08);
  --shadow-lg: 0 4px 25px rgba(59, 130, 246, 0.2);
  
  --gradient-magic: linear-gradient(90deg, #3B82F6, #8B5CF6, #3B82F6);
}

[data-theme="dark"] {
  /* Dark Theme */
  --bg-primary: #111827;
  --bg-primary-rgb: 17, 24, 39;
  --bg-secondary: #1F2937;
  --bg-secondary-rgb: 31, 41, 55;
  --bg-tertiary: #374151;
  
  --text-primary: #F9FAFB;
  --text-secondary: #D1D5DB;
  --text-tertiary: #9CA3AF;
  
  --border-primary: #374151;
  --border-secondary: #4B5563;
  
  --accent-primary: #60A5FA;
  --accent-secondary: #93C5FD;
  --accent-success: #34D399;
  --accent-warning: #FBBF24;
  --accent-purple: #A78BFA;
  
  --shadow-sm: 0 2px 8px rgba(0,0,0,0.3);
  --shadow-md: 0 4px 16px rgba(0,0,0,0.4);
  --shadow-lg: 0 4px 25px rgba(96, 165, 250, 0.3);
  
  --gradient-magic: linear-gradient(90deg, #60A5FA, #A78BFA, #60A5FA);
}

/* Theme Transition */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Theme Toggle Button */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--bg-secondary);
  border: 2px solid var(--border-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  transition: all 0.3s ease;
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.theme-toggle i {
  transition: transform 0.3s ease;
}

.theme-toggle:hover i {
  transform: rotate(180deg);
}

/* Light theme icon */
[data-theme="light"] .theme-toggle .dark-icon {
  display: none;
}

[data-theme="light"] .theme-toggle .light-icon {
  display: block;
}

/* Dark theme icon */
[data-theme="dark"] .theme-toggle .light-icon {
  display: none;
}

[data-theme="dark"] .theme-toggle .dark-icon {
  display: block;
}

/* Card Type Colors for Dark Theme */
[data-theme="dark"] .card-type.calendar {
  background: rgba(96, 165, 250, 0.2);
  color: var(--accent-primary);
}

[data-theme="dark"] .card-type.todo {
  background: rgba(52, 211, 153, 0.2);
  color: var(--accent-success);
}

[data-theme="dark"] .card-type.note {
  background: rgba(167, 139, 250, 0.2);
  color: var(--accent-purple);
}

/* Tab Bar Active State for Dark Theme */
[data-theme="dark"] .tab-item.active {
  background: rgba(96, 165, 250, 0.2);
}

/* Calendar Today for Dark Theme */
[data-theme="dark"] .calendar-day.today {
  background: var(--accent-primary);
  color: var(--bg-primary);
}

/* Todo Checkbox for Dark Theme */
[data-theme="dark"] .todo-checkbox {
  border-color: var(--border-secondary);
}

[data-theme="dark"] .todo-checkbox.checked {
  background: var(--accent-success);
  border-color: var(--accent-success);
}

/* Magic FAB System Dark Theme */
[data-theme="dark"] .magic-fab-main {
  box-shadow: 0 4px 16px rgba(0,0,0,0.4);
}

[data-theme="dark"] .magic-fab-main:hover {
  box-shadow: 0 4px 25px rgba(96, 165, 250, 0.3);
}

[data-theme="dark"] .fab-option {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
  box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .fab-option:hover {
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .magic-input-panel {
  background: var(--bg-secondary);
  border-top-color: var(--border-primary);
  box-shadow: 0 -4px 20px rgba(0,0,0,0.3);
}

[data-theme="dark"] .input-panel-header {
  border-bottom-color: var(--border-primary);
}

[data-theme="dark"] .panel-close-btn {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

[data-theme="dark"] .panel-close-btn:hover {
  background: var(--border-secondary);
  color: var(--text-primary);
}

[data-theme="dark"] .magic-textarea {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

[data-theme="dark"] .magic-textarea:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

[data-theme="dark"] .action-btn.secondary {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-color: var(--border-primary);
}

[data-theme="dark"] .action-btn.secondary:hover {
  background: var(--border-secondary);
  color: var(--text-primary);
}

[data-theme="dark"] .voice-wave {
  background: var(--accent-primary);
}

[data-theme="dark"] .voice-record-btn {
  box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .voice-record-btn:hover {
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .clipboard-preview {
  border-color: var(--border-secondary);
  color: var(--text-tertiary);
}

[data-theme="dark"] .clipboard-preview.has-content {
  border-color: var(--accent-primary);
  background: rgba(96, 165, 250, 0.1);
  color: var(--text-primary);
}

[data-theme="dark"] .image-drop-zone {
  border-color: var(--border-secondary);
  color: var(--text-tertiary);
}

[data-theme="dark"] .image-drop-zone:hover,
[data-theme="dark"] .image-drop-zone.dragover {
  border-color: var(--accent-primary);
  background: rgba(96, 165, 250, 0.1);
  color: var(--text-primary);
}

[data-theme="dark"] .suggestion-chip {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
  color: var(--text-secondary);
}

[data-theme="dark"] .suggestion-chip:hover {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

[data-theme="dark"] .quick-suggestions {
  border-top-color: var(--border-primary);
}

/* Post-Processing State Dark Theme */
[data-theme="dark"] .processing-actions-simple.post-processing {
  background: rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.3);
}

[data-theme="dark"] .processing-success-message {
  color: var(--accent-success);
}

/* AI Processing Modal for Dark Theme */
[data-theme="dark"] .ai-processing {
  background: rgba(17, 24, 39, 0.9);
  color: var(--text-primary);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* Focus States */
.magic-input:focus {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-lg);
}

/* Hover Effects */
.content-card:hover,
.note-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.tab-item:hover {
  background: rgba(59, 130, 246, 0.1);
}

[data-theme="dark"] .tab-item:hover {
  background: rgba(96, 165, 250, 0.1);
}

/* Calendar Day Hover */
.calendar-day:hover {
  background: var(--bg-tertiary);
  border-radius: 8px;
}

/* Button Hover Effects */
.calendar-nav button:hover {
  background: var(--bg-tertiary);
}

.calendar-nav button.active {
  background: var(--accent-primary);
  color: var(--bg-secondary);
}

/* Loading Animation */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 480px) {
  .theme-toggle {
    top: 15px;
    right: 15px;
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}

/* Print Styles */
@media print {
  .theme-toggle,
  .tab-bar,
  .magic-input-container {
    display: none !important;
  }
  
  .app-container {
    box-shadow: none;
  }
}
