/* Main Styles for Synapse App */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow-x: hidden;
  line-height: 1.6;
}

.app-container {
  max-width: 375px;
  margin: 0 auto;
  background: var(--bg-secondary);
  min-height: 100vh;
  position: relative;
  box-shadow: var(--shadow-md);
  overflow-x: hidden;
}

/* Optimized Compact Header */
.header {
  background: var(--bg-secondary);
  padding: 20px 20px 12px;
  border-bottom: 1px solid var(--border-primary);
  position: sticky;
  top: 0;
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  background: rgba(var(--bg-secondary-rgb), 0.95);
}

.header.compact {
  padding: 8px 20px;
  transform: translateY(0);
}

.header.hidden {
  transform: translateY(-100%);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 335px;
  margin: 0 auto;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.header h1 {
  font-size: 20px;
  font-weight: 700;
  color: var(--accent-primary);
  margin: 0;
  transition: all 0.3s ease;
}

.header.compact h1 {
  font-size: 16px;
}

.header .subtitle {
  font-size: 11px;
  color: var(--text-secondary);
  opacity: 1;
  transition: all 0.3s ease;
  margin-left: 4px;
}

.header.compact .subtitle {
  opacity: 0;
  transform: scale(0.8);
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  opacity: 1;
  transition: all 0.3s ease;
}

.header.compact .header-actions {
  opacity: 0.7;
}

.header-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s ease;
}

.header-action-btn:hover {
  background: var(--accent-primary);
  color: white;
  transform: scale(1.1);
}

.header.compact .header-action-btn {
  width: 28px;
  height: 28px;
  font-size: 12px;
}

/* Main Content */
.main-content {
  padding: 16px 20px;
  padding-bottom: 80px;
  min-height: calc(100vh - 140px);
  transition: all 0.3s ease;
  position: relative;
}

.main-content.header-compact {
  min-height: calc(100vh - 120px);
  padding-bottom: 75px;
}

.main-content.header-hidden {
  min-height: calc(100vh - 100px);
  padding-bottom: 70px;
}

/* Page Container */
.page {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.page.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Magic FAB System
   Z-Index Hierarchy:
   - Header: 100
   - Tab Bar: 300
   - Magic Backdrop: 450
   - Magic FAB: 400
   - Magic Input Panel: 500
   - Search Overlay: 1000
*/
.magic-fab-container {
  position: fixed;
  bottom: 100px;
  right: 20px;
  z-index: 400;
}

/* Main FAB Button */
.magic-fab-main {
  width: 56px;
  height: 56px;
  background: var(--accent-primary);
  border-radius: 50%;
  box-shadow: var(--shadow-md);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  /* Ensure minimum touch target size */
  min-width: 44px;
  min-height: 44px;
  /* Add padding for larger touch area */
  padding: 8px;
}

.magic-fab-main:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.magic-fab-main.expanded {
  background: var(--accent-secondary);
  transform: rotate(45deg);
}

.fab-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fab-icon {
  font-size: 20px;
  color: white;
  transition: all 0.3s ease;
}

.magic-fab-main.expanded .fab-icon {
  transform: rotate(-45deg);
}

/* Ensure FAB always appears above tab bar */
.magic-fab-container {
  isolation: isolate;
}

/* FAB state when panel is open */
.magic-fab-container.panel-open .magic-fab-main {
  background: var(--accent-secondary);
  box-shadow: var(--shadow-lg);
  transform: scale(0.9);
}

.magic-fab-container.panel-open .fab-icon {
  transform: rotate(45deg);
}

/* AI Processing Ring */
.ai-processing-ring {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid transparent;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ai-processing-ring.active {
  opacity: 1;
  border-top-color: var(--accent-purple);
  border-right-color: var(--accent-purple);
  animation: spin 1s linear infinite;
}

/* Removed FAB Options - Direct panel access now */

/* Input Panel */
.magic-input-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-height: 80vh;
  overflow: hidden;
  z-index: 500;
}

.magic-input-panel.show {
  transform: translateY(0);
}

/* Ensure input panel always appears above tab bar */
.magic-input-panel {
  isolation: isolate;
}

.magic-input-panel.show {
  z-index: 500 !important;
}

.input-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-primary);
}

.input-method-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: var(--text-primary);
}

.input-method-indicator i {
  color: var(--accent-primary);
}

.content-counter {
  display: flex;
  align-items: center;
}

.counter-badge {
  background: var(--accent-primary);
  color: white;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
}

.panel-close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-tertiary);
  border-radius: 50%;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.panel-close-btn:hover {
  background: var(--border-secondary);
  color: var(--text-primary);
}

/* Large Unified Content Area */
.unified-content-area-large {
  min-height: 200px;
  max-height: calc(70vh - 180px);
  overflow-y: auto;
  overflow-x: hidden;
  border-bottom: 1px solid var(--border-primary);
  position: relative;
  padding: 16px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  /* Smooth scrolling */
  scroll-behavior: smooth;
  /* Custom scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: var(--border-secondary) transparent;
}

/* Webkit scrollbar styling */
.unified-content-area-large::-webkit-scrollbar {
  width: 6px;
}

.unified-content-area-large::-webkit-scrollbar-track {
  background: transparent;
}

.unified-content-area-large::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

.unified-content-area-large::-webkit-scrollbar-thumb:hover {
  background: var(--border-primary);
}

/* Quick Input Actions */
.quick-input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  padding: 12px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  border: 1px solid var(--border-primary);
  flex-shrink: 0;
  box-sizing: border-box;
  width: 100%;
}

.input-actions-left {
  display: flex;
  gap: 8px;
}

.quick-action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.quick-action-btn:hover {
  background: var(--accent-primary);
  color: white;
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.quick-action-btn.active {
  background: var(--accent-primary);
  color: white;
}

.quick-action-btn.recording {
  background: #EF4444;
  color: white;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* Image Processing Options */
.image-processing-options {
  display: flex;
  gap: 8px;
  align-items: center;
}

.image-option-compact {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  font-size: 11px;
  font-weight: 500;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
}

.image-option-compact:hover {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.image-option-compact input[type="checkbox"] {
  width: 12px;
  height: 12px;
  margin: 0;
  accent-color: var(--accent-primary);
}

.image-option-compact i {
  font-size: 10px;
}

.image-option-compact span {
  font-size: 10px;
  font-weight: 600;
}

/* Content Blocks Container */
.content-blocks-container {
  min-height: 120px;
  margin-bottom: 16px;
  border: 2px dashed transparent;
  border-radius: 12px;
  transition: all 0.2s ease;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 100%;
  box-sizing: border-box;
}

.content-blocks-container.dragover {
  border-color: var(--accent-primary);
  background: rgba(59, 130, 246, 0.05);
}

.empty-content-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  color: var(--text-tertiary);
  text-align: center;
  min-height: 100px;
  box-sizing: border-box;
}

.empty-content-state i {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.empty-content-state p {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--text-secondary);
}

.empty-content-state span {
  font-size: 12px;
}

.empty-content-state.hidden {
  display: none;
}

/* Inline Text Input */
.inline-text-input {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  flex-shrink: 0;
  box-sizing: border-box;
  width: 100%;
}

.inline-textarea {
  width: 100%;
  min-height: 80px;
  padding: 12px;
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 14px;
  resize: vertical;
  outline: none;
  transition: all 0.2s ease;
  margin-bottom: 12px;
}

.inline-textarea:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.inline-textarea::placeholder {
  color: var(--text-tertiary);
}

.inline-text-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.inline-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.inline-btn.cancel {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
}

.inline-btn.cancel:hover {
  background: var(--border-secondary);
  color: var(--text-primary);
}

.inline-btn.add {
  background: var(--accent-primary);
  color: white;
}

.inline-btn.add:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

/* Voice Recording Status */
.voice-recording-status {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
  box-sizing: border-box;
  width: 100%;
}

.voice-visualizer-inline {
  display: flex;
  align-items: center;
  gap: 3px;
}

.voice-wave-inline {
  width: 3px;
  background: #EF4444;
  border-radius: 2px;
  animation: wave-inline 1.5s ease-in-out infinite;
}

.voice-wave-inline:nth-child(1) { animation-delay: 0s; height: 16px; }
.voice-wave-inline:nth-child(2) { animation-delay: 0.1s; height: 24px; }
.voice-wave-inline:nth-child(3) { animation-delay: 0.2s; height: 20px; }
.voice-wave-inline:nth-child(4) { animation-delay: 0.3s; height: 18px; }

@keyframes wave-inline {
  0%, 100% { transform: scaleY(0.5); opacity: 0.7; }
  50% { transform: scaleY(1); opacity: 1; }
}

.voice-status-text {
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.stop-recording-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: #EF4444;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.stop-recording-btn:hover {
  background: #DC2626;
  transform: scale(1.1);
}

/* Content Blocks */
.content-block {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 12px;
  position: relative;
  transition: all 0.2s ease;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;
  /* Animation for new blocks */
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.content-block:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.content-block-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.content-block-type {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
}

.content-block-type i {
  font-size: 10px;
}

.content-block-type.text { color: var(--accent-primary); }
.content-block-type.voice { color: var(--accent-success); }
.content-block-type.clipboard { color: var(--accent-warning); }
.content-block-type.image { color: var(--accent-purple); }
.content-block-type.analyzed { color: #EF4444; }

.content-block-actions {
  display: flex;
  gap: 4px;
}

.content-block-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  color: var(--text-tertiary);
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  transition: all 0.2s ease;
}

.content-block-btn:hover {
  background: var(--border-secondary);
  color: var(--text-primary);
}

.content-block-content {
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-primary);
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}

.content-block-image {
  max-width: 100%;
  width: auto;
  height: auto;
  max-height: 150px;
  border-radius: 8px;
  margin-top: 8px;
  display: block;
  object-fit: contain;
  box-sizing: border-box;
}

.content-block-metadata {
  font-size: 11px;
  color: var(--text-tertiary);
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid var(--border-primary);
}

/* Unified Content Processing Actions */
.processing-actions-simple {
  padding: 16px;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.unified-content-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.content-summary i {
  color: var(--accent-primary);
  font-size: 14px;
}

.content-summary.has-content {
  color: var(--text-primary);
}

.content-summary.has-content i {
  color: var(--accent-success);
}

.processing-buttons {
  display: flex;
  gap: 8px;
}

.process-btn {
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  min-width: 80px;
  justify-content: center;
}

.process-btn.primary {
  background: var(--accent-primary);
  color: white;
}

.process-btn.primary:hover:not(:disabled) {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

.process-btn.primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.process-btn.secondary {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
}

.process-btn.secondary:hover {
  background: var(--border-secondary);
  color: var(--text-primary);
}

.process-btn.tertiary {
  background: var(--accent-warning);
  color: white;
}

.process-btn.tertiary:hover:not(:disabled) {
  background: #F59E0B;
  transform: translateY(-1px);
}

.process-btn.tertiary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Post-Processing State */
.processing-actions-simple.post-processing {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.processing-success-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--accent-success);
  font-size: 14px;
  font-weight: 600;
}

.processing-success-message i {
  font-size: 16px;
}

/* Temporary Success Message Animation */
@keyframes fadeInOut {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}

.input-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.action-btn {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: var(--accent-primary);
  color: white;
}

.action-btn.primary:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-primary);
}

.action-btn.secondary:hover {
  background: var(--border-secondary);
  color: var(--text-primary);
}

/* Removed old processing actions - replaced with simplified version */

/* Voice Input */
.voice-input-area {
  text-align: center;
  padding: 20px;
}

.voice-visualizer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  height: 60px;
  margin-bottom: 20px;
}

.voice-wave {
  width: 4px;
  background: var(--accent-primary);
  border-radius: 2px;
  animation: wave 1.5s ease-in-out infinite;
  opacity: 0.3;
}

.voice-wave:nth-child(1) { animation-delay: 0s; height: 20px; }
.voice-wave:nth-child(2) { animation-delay: 0.1s; height: 30px; }
.voice-wave:nth-child(3) { animation-delay: 0.2s; height: 40px; }
.voice-wave:nth-child(4) { animation-delay: 0.3s; height: 25px; }

@keyframes wave {
  0%, 100% { transform: scaleY(0.5); opacity: 0.3; }
  50% { transform: scaleY(1); opacity: 1; }
}

.voice-visualizer.active .voice-wave {
  opacity: 1;
}

.voice-status {
  margin-bottom: 20px;
  color: var(--text-secondary);
}

.voice-record-btn {
  width: 64px;
  height: 64px;
  border: none;
  border-radius: 50%;
  background: var(--accent-primary);
  color: white;
  font-size: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.voice-record-btn:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.voice-record-btn.recording {
  background: #EF4444;
  animation: pulse 1s infinite;
}

/* Clipboard Input */
.clipboard-area {
  text-align: center;
  padding: 20px;
}

.clipboard-preview {
  border: 2px dashed var(--border-secondary);
  border-radius: 12px;
  padding: 40px 20px;
  margin-bottom: 20px;
  color: var(--text-tertiary);
  transition: all 0.2s ease;
}

.clipboard-preview i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.clipboard-preview.has-content {
  border-color: var(--accent-primary);
  background: rgba(59, 130, 246, 0.05);
  color: var(--text-primary);
}

.clipboard-paste-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  background: var(--accent-primary);
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
  transition: all 0.2s ease;
}

.clipboard-paste-btn:hover {
  background: var(--accent-secondary);
  transform: translateY(-1px);
}

/* Image Input */
.image-input-area {
  padding: 20px;
}

.image-drop-zone {
  border: 2px dashed var(--border-secondary);
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  color: var(--text-tertiary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.image-drop-zone:hover,
.image-drop-zone.dragover {
  border-color: var(--accent-primary);
  background: rgba(59, 130, 246, 0.05);
  color: var(--text-primary);
}

.image-drop-zone i {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.image-preview {
  text-align: center;
}

.image-preview img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-sm);
}

.image-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

/* Quick Suggestions */
.quick-suggestions {
  display: flex;
  gap: 8px;
  padding: 16px 20px;
  border-top: 1px solid var(--border-primary);
  overflow-x: auto;
}

.suggestion-chip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: 20px;
  font-size: 12px;
  color: var(--text-secondary);
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.suggestion-chip:hover {
  background: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

.suggestion-chip i {
  font-size: 10px;
}

/* Backdrop */
.magic-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 450;
}

.magic-backdrop.show {
  opacity: 1;
  visibility: visible;
}

/* Magic Suggestions */
.magic-suggestions {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  margin-bottom: 8px;
  opacity: 0;
  transform: translateY(10px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.magic-suggestions.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12px;
  margin: 4px;
}

.suggestion-item:hover {
  background: var(--bg-tertiary);
}

.suggestion-item i {
  font-size: 14px;
  margin-right: 12px;
  width: 16px;
  text-align: center;
}

.suggestion-item[data-type="calendar"] i {
  color: var(--accent-primary);
}

.suggestion-item[data-type="todo"] i {
  color: var(--accent-success);
}

.suggestion-item[data-type="note"] i {
  color: var(--accent-purple);
}

.suggestion-item span {
  font-size: 14px;
  color: var(--text-secondary);
}

/* Magic Hints */
.magic-hints {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  box-shadow: var(--shadow-md);
  margin-top: 8px;
  padding: 12px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  pointer-events: none;
}

.magic-hints.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

.hint-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  font-size: 12px;
}

.hint-key {
  font-size: 14px;
  margin-right: 8px;
  width: 20px;
}

.hint-text {
  color: var(--text-tertiary);
  font-style: italic;
}

/* Tab Bar */
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: 375px;
  background: rgba(var(--bg-secondary-rgb), 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-around;
  padding: 12px 0;
  z-index: 300;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 12px;
  min-width: 60px;
}

.tab-item.active {
  background: rgba(59, 130, 246, 0.1);
}

.tab-item i {
  font-size: 20px;
  color: var(--text-secondary);
  margin-bottom: 4px;
  transition: color 0.2s ease;
}

.tab-item.active i {
  color: var(--accent-primary);
}

.tab-item span {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
}

.tab-item.active span {
  color: var(--accent-primary);
  font-weight: 600;
}

/* Tab Bar Scroll States */
.tab-bar.scrolled {
  background: rgba(var(--bg-secondary-rgb), 0.98);
  backdrop-filter: blur(15px);
  box-shadow: 0 -4px 25px rgba(0, 0, 0, 0.15);
  border-top-color: var(--border-secondary);
}

.tab-bar.hidden {
  transform: translateX(-50%) translateY(100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Content fade effect near tab bar */
.main-content::after {
  content: '';
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: linear-gradient(transparent, rgba(var(--bg-primary-rgb), 0.6));
  pointer-events: none;
  z-index: 250;
  transition: opacity 0.3s ease;
  opacity: 0;
}

.main-content.show-fade::after {
  opacity: 1;
}

/* Hide fade effect when tab bar is hidden */
.tab-bar.hidden ~ .main-content::after {
  opacity: 0;
}

/* Content Cards */
.content-card {
  background: var(--bg-secondary);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
  cursor: pointer;
}

.content-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.card-type {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-right: 12px;
}

.card-type.calendar {
  background: rgba(59, 130, 246, 0.1);
  color: var(--accent-primary);
}

.card-type.todo {
  background: rgba(34, 197, 94, 0.1);
  color: var(--accent-success);
}

.card-type.note {
  background: rgba(139, 92, 246, 0.1);
  color: var(--accent-purple);
}

.card-time {
  font-size: 12px;
  color: var(--text-secondary);
  margin-left: auto;
}

.card-content h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.card-content p {
  font-size: 14px;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  color: var(--text-tertiary);
}

.empty-state h3 {
  font-size: 18px;
  margin-bottom: 8px;
  color: var(--text-secondary);
}

.empty-state p {
  font-size: 14px;
  line-height: 1.5;
}

/* AI Processing Animation */
.ai-processing {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0,0,0,0.8);
  color: #FFFFFF;
  padding: 20px;
  border-radius: 16px;
  text-align: center;
  z-index: 1000;
  display: none;
}

.ai-processing.show {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255,255,255,0.3);
  border-top: 3px solid #FFFFFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

/* Responsive Design */
@media (max-width: 480px) {
  .app-container {
    max-width: 100%;
  }

  .main-content {
    padding: 15px;
    padding-bottom: 75px;
  }

  .magic-fab-container {
    bottom: 90px;
    right: 15px;
  }

  .magic-fab-main {
    width: 52px;
    height: 52px;
  }

  .fab-icon {
    font-size: 18px;
  }

  .fab-option {
    width: 44px;
    height: 44px;
  }

  .magic-input-panel {
    max-height: 80vh;
  }

  .input-panel-content {
    padding: 16px;
  }

  .magic-textarea {
    min-height: 60px;
    font-size: 16px;
  }

  .voice-visualizer {
    height: 50px;
  }

  .voice-record-btn {
    width: 56px;
    height: 56px;
    font-size: 20px;
  }

  .quick-suggestions {
    padding: 12px 16px;
  }

  .suggestion-chip {
    font-size: 11px;
    padding: 6px 10px;
  }

  .unified-content-area-large {
    min-height: 180px;
    max-height: calc(70vh - 160px);
    padding: 12px;
  }

  .quick-input-actions {
    padding: 8px;
    gap: 12px;
    flex-direction: column;
    align-items: stretch;
  }

  .input-actions-left {
    justify-content: center;
  }

  .quick-action-btn {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  .image-processing-options {
    justify-content: center;
    gap: 6px;
  }

  .image-option-compact {
    padding: 4px 8px;
    font-size: 10px;
  }

  .image-option-compact span {
    font-size: 9px;
  }

  .content-blocks-container {
    min-height: 100px;
  }

  .content-block {
    padding: 10px;
    margin-bottom: 10px;
  }

  .content-block-content {
    font-size: 13px;
  }

  .inline-textarea {
    min-height: 60px;
    font-size: 14px;
  }

  .processing-actions-simple {
    padding: 12px;
    flex-direction: column;
    gap: 12px;
  }

  .processing-options {
    justify-content: center;
  }

  .process-btn {
    min-width: 70px;
    padding: 8px 12px;
    font-size: 11px;
  }

  .processing-success-message {
    font-size: 12px;
    text-align: center;
  }

  .processing-success-message i {
    font-size: 14px;
  }

  /* Compact Header Mobile Optimizations */
  .header {
    padding: 16px 16px 8px;
  }

  .header.compact {
    padding: 6px 16px;
  }

  .header h1 {
    font-size: 18px;
  }

  .header.compact h1 {
    font-size: 14px;
  }

  .header .subtitle {
    font-size: 10px;
  }

  .header-action-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }

  .header.compact .header-action-btn {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }

  .search-container {
    top: 10px;
    width: calc(100% - 20px);
  }

  .search-header {
    padding: 12px;
  }

  .search-input {
    font-size: 14px;
  }
}

/* Search Overlay */
.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.search-overlay.show {
  opacity: 1;
  visibility: visible;
}

.search-container {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% - 40px);
  max-width: 335px;
  background: var(--bg-secondary);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transform: translateX(-50%) translateY(-20px);
  transition: all 0.3s ease;
}

.search-overlay.show .search-container {
  transform: translateX(-50%) translateY(0);
}

.search-header {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-primary);
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 16px;
  color: var(--text-primary);
  outline: none;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.search-close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  transition: all 0.2s ease;
}

.search-close-btn:hover {
  background: var(--accent-primary);
  color: white;
}

.search-results {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
}

.search-empty {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-tertiary);
}

.search-empty i {
  font-size: 32px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.search-empty p {
  font-size: 14px;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Visible */
.tab-item:focus-visible,
.magic-input:focus-visible,
.content-card:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--border-primary);
  border-top: 2px solid var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
