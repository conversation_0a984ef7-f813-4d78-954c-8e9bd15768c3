// Smart Header Management System

class HeaderManager {
  constructor() {
    this.header = document.getElementById('appHeader');
    this.mainContent = document.querySelector('.main-content');
    this.themeToggle = document.getElementById('themeToggle');
    this.searchBtn = document.getElementById('searchBtn');
    
    this.lastScrollY = 0;
    this.scrollThreshold = 50;
    this.isCompact = false;
    this.isHidden = false;
    this.scrollTimeout = null;
    
    this.init();
  }

  init() {
    this.bindScrollEvents();
    this.bindHeaderActions();
    this.bindResizeEvents();
    this.updateThemeIcon();
  }

  bindScrollEvents() {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    // Throttled scroll handler
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Touch events for mobile
    window.addEventListener('touchstart', this.handleTouchStart.bind(this), { passive: true });
    window.addEventListener('touchmove', this.handleTouchMove.bind(this), { passive: true });
  }

  handleScroll() {
    const currentScrollY = window.scrollY;
    const scrollDelta = currentScrollY - this.lastScrollY;
    
    // Clear any pending scroll timeout
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }

    // Determine header state based on scroll position and direction
    if (currentScrollY < this.scrollThreshold) {
      // Near top - show full header
      this.showFullHeader();
    } else if (currentScrollY < this.scrollThreshold * 2) {
      // Moderate scroll - compact header
      this.showCompactHeader();
    } else if (scrollDelta > 5 && currentScrollY > this.scrollThreshold * 3) {
      // Scrolling down significantly - hide header
      this.hideHeader();
    } else if (scrollDelta < -5) {
      // Scrolling up - show compact header
      this.showCompactHeader();
    }

    // Auto-show header after scroll stops
    this.scrollTimeout = setTimeout(() => {
      if (currentScrollY > this.scrollThreshold) {
        this.showCompactHeader();
      }
    }, 1000);

    this.lastScrollY = currentScrollY;
  }

  handleTouchStart(e) {
    this.touchStartY = e.touches[0].clientY;
  }

  handleTouchMove(e) {
    if (!this.touchStartY) return;
    
    const touchY = e.touches[0].clientY;
    const touchDelta = this.touchStartY - touchY;
    
    // Show header on upward swipe
    if (touchDelta < -50 && this.isHidden) {
      this.showCompactHeader();
    }
  }

  showFullHeader() {
    if (!this.isCompact && !this.isHidden) return;
    
    this.header.classList.remove('compact', 'hidden');
    this.mainContent.classList.remove('header-compact', 'header-hidden');
    this.isCompact = false;
    this.isHidden = false;
  }

  showCompactHeader() {
    if (this.isCompact && !this.isHidden) return;
    
    this.header.classList.add('compact');
    this.header.classList.remove('hidden');
    this.mainContent.classList.add('header-compact');
    this.mainContent.classList.remove('header-hidden');
    this.isCompact = true;
    this.isHidden = false;
  }

  hideHeader() {
    if (this.isHidden) return;
    
    this.header.classList.add('hidden');
    this.header.classList.remove('compact');
    this.mainContent.classList.add('header-hidden');
    this.mainContent.classList.remove('header-compact');
    this.isCompact = false;
    this.isHidden = true;
  }

  bindHeaderActions() {
    // Theme toggle
    this.themeToggle.addEventListener('click', () => {
      this.toggleTheme();
    });

    // Search functionality
    this.searchBtn.addEventListener('click', () => {
      this.openSearch();
    });
  }

  toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    localStorage.setItem('theme', newTheme);
    
    this.updateThemeIcon();
    
    // Brief visual feedback
    this.themeToggle.style.transform = 'scale(1.2)';
    setTimeout(() => {
      this.themeToggle.style.transform = '';
    }, 150);
  }

  updateThemeIcon() {
    const theme = document.documentElement.getAttribute('data-theme');
    const icon = this.themeToggle.querySelector('i');
    
    if (theme === 'dark') {
      icon.className = 'fas fa-sun';
      this.themeToggle.title = 'Switch to Light Mode';
    } else {
      icon.className = 'fas fa-moon';
      this.themeToggle.title = 'Switch to Dark Mode';
    }
  }

  openSearch() {
    // Create search overlay
    const searchOverlay = document.createElement('div');
    searchOverlay.className = 'search-overlay';
    searchOverlay.innerHTML = `
      <div class="search-container">
        <div class="search-header">
          <input type="text" class="search-input" placeholder="Search your content..." autofocus>
          <button class="search-close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="search-results">
          <div class="search-empty">
            <i class="fas fa-search"></i>
            <p>Start typing to search your content</p>
          </div>
        </div>
      </div>
    `;
    
    document.body.appendChild(searchOverlay);
    
    // Animate in
    requestAnimationFrame(() => {
      searchOverlay.classList.add('show');
    });
    
    // Bind close events
    const closeBtn = searchOverlay.querySelector('.search-close-btn');
    const searchInput = searchOverlay.querySelector('.search-input');
    
    closeBtn.addEventListener('click', () => this.closeSearch(searchOverlay));
    
    // Close on escape
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        this.closeSearch(searchOverlay);
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);
    
    // Focus input
    setTimeout(() => searchInput.focus(), 100);
  }

  closeSearch(overlay) {
    overlay.classList.remove('show');
    setTimeout(() => {
      if (overlay.parentNode) {
        overlay.parentNode.removeChild(overlay);
      }
    }, 300);
  }

  bindResizeEvents() {
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        // Reset header state on resize
        if (window.scrollY < this.scrollThreshold) {
          this.showFullHeader();
        }
      }, 250);
    });
  }

  // Public methods
  forceShow() {
    this.showFullHeader();
  }

  forceHide() {
    this.hideHeader();
  }

  getCurrentState() {
    return {
      isCompact: this.isCompact,
      isHidden: this.isHidden,
      scrollY: window.scrollY
    };
  }
}

// Auto-initialize header manager
document.addEventListener('DOMContentLoaded', () => {
  window.headerManager = new HeaderManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = HeaderManager;
}
