// Theme Management System

class ThemeManager {
  constructor() {
    this.currentTheme = this.getStoredTheme() || this.getSystemTheme();
    this.init();
  }

  init() {
    this.applyTheme(this.currentTheme);
    // Don't create theme toggle - it's now handled by header
    this.bindEvents();
  }

  getSystemTheme() {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }

  getStoredTheme() {
    return localStorage.getItem('theme');
  }

  storeTheme(theme) {
    localStorage.setItem('theme', theme);
  }

  applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    this.currentTheme = theme;
    this.storeTheme(theme);
    this.updateThemeToggle();
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(newTheme);
    
    // Add a subtle animation effect
    document.body.style.transition = 'background-color 0.3s ease';
    setTimeout(() => {
      document.body.style.transition = '';
    }, 300);
  }

  createThemeToggle() {
    const toggle = document.createElement('button');
    toggle.className = 'theme-toggle';
    toggle.setAttribute('aria-label', 'Toggle theme');
    toggle.innerHTML = `
      <i class="fas fa-sun light-icon"></i>
      <i class="fas fa-moon dark-icon"></i>
    `;
    
    document.body.appendChild(toggle);
    this.themeToggle = toggle;
  }

  updateThemeToggle() {
    if (this.themeToggle) {
      const lightIcon = this.themeToggle.querySelector('.light-icon');
      const darkIcon = this.themeToggle.querySelector('.dark-icon');
      
      if (this.currentTheme === 'dark') {
        lightIcon.style.display = 'none';
        darkIcon.style.display = 'block';
      } else {
        lightIcon.style.display = 'block';
        darkIcon.style.display = 'none';
      }
    }
  }

  bindEvents() {
    // Theme toggle click
    if (this.themeToggle) {
      this.themeToggle.addEventListener('click', () => {
        this.toggleTheme();
      });
    }

    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!this.getStoredTheme()) {
        this.applyTheme(e.matches ? 'dark' : 'light');
      }
    });

    // Keyboard shortcut (Ctrl/Cmd + Shift + T)
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        this.toggleTheme();
      }
    });
  }

  // Public method to get current theme
  getCurrentTheme() {
    return this.currentTheme;
  }

  // Public method to set theme programmatically
  setTheme(theme) {
    if (theme === 'light' || theme === 'dark') {
      this.applyTheme(theme);
    }
  }
}

// Theme-aware utilities
const ThemeUtils = {
  // Get theme-appropriate colors
  getColors() {
    const theme = document.documentElement.getAttribute('data-theme') || 'light';
    
    if (theme === 'dark') {
      return {
        primary: '#60A5FA',
        secondary: '#A78BFA',
        success: '#34D399',
        warning: '#FBBF24',
        background: '#111827',
        surface: '#1F2937',
        text: '#F9FAFB'
      };
    } else {
      return {
        primary: '#3B82F6',
        secondary: '#8B5CF6',
        success: '#22C55E',
        warning: '#F59E0B',
        background: '#F9FAFB',
        surface: '#FFFFFF',
        text: '#1F2937'
      };
    }
  },

  // Check if current theme is dark
  isDark() {
    return document.documentElement.getAttribute('data-theme') === 'dark';
  },

  // Get appropriate icon for current theme
  getIcon(lightIcon, darkIcon) {
    return this.isDark() ? darkIcon : lightIcon;
  },

  // Apply theme-specific styles to elements
  applyThemeStyles(element, lightStyles, darkStyles) {
    const styles = this.isDark() ? darkStyles : lightStyles;
    Object.assign(element.style, styles);
  }
};

// Auto-initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.themeManager = new ThemeManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { ThemeManager, ThemeUtils };
}

// Global theme utilities
window.ThemeUtils = ThemeUtils;
