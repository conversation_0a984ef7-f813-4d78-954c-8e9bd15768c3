// Smart Tab Bar Management System

class TabBarManager {
  constructor() {
    this.tabBar = document.querySelector('.tab-bar');
    this.mainContent = document.querySelector('.main-content');
    this.lastScrollY = 0;
    this.scrollThreshold = 20;
    this.isHidden = false;
    this.scrollTimeout = null;
    
    this.init();
  }

  init() {
    if (!this.tabBar || !this.mainContent) return;
    
    this.bindScrollEvents();
    this.bindTabEvents();
    this.updateTabBarState();
  }

  bindScrollEvents() {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          this.handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    // Throttled scroll handler for main content
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Also listen for scroll on individual page containers
    document.querySelectorAll('.page').forEach(page => {
      page.addEventListener('scroll', handleScroll, { passive: true });
    });
  }

  handleScroll() {
    const currentScrollY = window.scrollY || document.documentElement.scrollTop;
    const scrollDelta = currentScrollY - this.lastScrollY;
    
    // Clear any pending scroll timeout
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }

    // Update tab bar state based on scroll position and direction
    if (currentScrollY < this.scrollThreshold) {
      // Near top - show normal tab bar
      this.showTabBar();
      this.removeScrolledState();
      this.hideFadeEffect();
    } else {
      // Scrolled down - add scrolled state for enhanced backdrop
      this.addScrolledState();
      this.showFadeEffect();

      if (scrollDelta > 5 && currentScrollY > this.scrollThreshold * 2) {
        // Scrolling down significantly - hide tab bar
        this.hideTabBar();
      } else if (scrollDelta < -5) {
        // Scrolling up - show tab bar
        this.showTabBar();
      }
    }

    // Auto-show tab bar after scroll stops
    this.scrollTimeout = setTimeout(() => {
      if (currentScrollY > this.scrollThreshold) {
        this.showTabBar();
      }
    }, 1500);

    this.lastScrollY = currentScrollY;
  }

  showTabBar() {
    if (!this.isHidden) return;
    
    this.tabBar.classList.remove('hidden');
    this.isHidden = false;
  }

  hideTabBar() {
    if (this.isHidden) return;
    
    this.tabBar.classList.add('hidden');
    this.isHidden = true;
  }

  addScrolledState() {
    this.tabBar.classList.add('scrolled');
  }

  removeScrolledState() {
    this.tabBar.classList.remove('scrolled');
  }

  showFadeEffect() {
    this.mainContent.classList.add('show-fade');
  }

  hideFadeEffect() {
    this.mainContent.classList.remove('show-fade');
  }

  bindTabEvents() {
    // Add enhanced visual feedback for tab interactions
    const tabItems = document.querySelectorAll('.tab-item');
    
    tabItems.forEach(tab => {
      tab.addEventListener('click', () => {
        // Ensure tab bar is visible when user interacts
        this.showTabBar();
        this.removeScrolledState();
        
        // Add brief visual feedback
        tab.style.transform = 'scale(0.95)';
        setTimeout(() => {
          tab.style.transform = '';
        }, 150);
      });

      // Add hover effects
      tab.addEventListener('mouseenter', () => {
        if (!tab.classList.contains('active')) {
          tab.style.background = 'rgba(59, 130, 246, 0.05)';
        }
      });

      tab.addEventListener('mouseleave', () => {
        if (!tab.classList.contains('active')) {
          tab.style.background = '';
        }
      });
    });
  }

  updateTabBarState() {
    // Initial state based on current scroll position
    const currentScrollY = window.scrollY || document.documentElement.scrollTop;
    
    if (currentScrollY > this.scrollThreshold) {
      this.addScrolledState();
    } else {
      this.removeScrolledState();
    }
  }

  // Public methods for external control
  forceShow() {
    this.showTabBar();
    this.removeScrolledState();
  }

  forceHide() {
    this.hideTabBar();
  }

  getCurrentState() {
    return {
      isHidden: this.isHidden,
      isScrolled: this.tabBar.classList.contains('scrolled'),
      scrollY: this.lastScrollY
    };
  }

  // Method to handle page transitions
  onPageChange() {
    // Reset tab bar state when page changes
    this.showTabBar();
    this.removeScrolledState();
    this.lastScrollY = 0;
  }

  // Method to handle content updates
  onContentUpdate() {
    // Recalculate scroll behavior when content changes
    setTimeout(() => {
      this.updateTabBarState();
    }, 100);
  }
}

// Auto-initialize tab bar manager
document.addEventListener('DOMContentLoaded', () => {
  window.tabBarManager = new TabBarManager();
  
  // Listen for page changes to update tab bar state
  document.addEventListener('pagechange', () => {
    if (window.tabBarManager) {
      window.tabBarManager.onPageChange();
    }
  });
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TabBarManager;
}
