// Navigation and Page Management System

class NavigationManager {
  constructor() {
    this.currentPage = 'inbox';
    this.pages = ['inbox', 'calendar', 'todos', 'notes'];
    this.pageCache = new Map();
    this.init();
  }

  init() {
    this.bindTabEvents();
    this.loadInitialPage();
    this.setupKeyboardNavigation();
  }

  bindTabEvents() {
    document.addEventListener('click', (e) => {
      const tabItem = e.target.closest('.tab-item');
      if (tabItem) {
        const pageId = tabItem.getAttribute('data-page');
        if (pageId && this.pages.includes(pageId)) {
          this.navigateToPage(pageId);
        }
      }
    });
  }

  async navigateToPage(pageId) {
    if (pageId === this.currentPage) return;

    // Update tab states
    this.updateTabStates(pageId);
    
    // Load and display page content
    await this.loadPageContent(pageId);
    
    // Update current page
    this.currentPage = pageId;
    
    // Update URL without page reload
    this.updateURL(pageId);
    
    // Trigger page change event
    this.triggerPageChangeEvent(pageId);
  }

  updateTabStates(activePageId) {
    document.querySelectorAll('.tab-item').forEach(tab => {
      const pageId = tab.getAttribute('data-page');
      if (pageId === activePageId) {
        tab.classList.add('active');
      } else {
        tab.classList.remove('active');
      }
    });
  }

  async loadPageContent(pageId) {
    const contentContainer = document.getElementById('page-content');
    if (!contentContainer) return;

    // Show loading state
    contentContainer.classList.add('loading');

    try {
      // Check cache first
      if (this.pageCache.has(pageId)) {
        contentContainer.innerHTML = this.pageCache.get(pageId);
      } else {
        // Load page content from file
        const content = await this.fetchPageContent(pageId);
        this.pageCache.set(pageId, content);
        contentContainer.innerHTML = content;
      }

      // Initialize page-specific functionality
      this.initializePageFeatures(pageId);
      
    } catch (error) {
      console.error(`Error loading page ${pageId}:`, error);
      contentContainer.innerHTML = this.getErrorContent(pageId);
    } finally {
      contentContainer.classList.remove('loading');
    }
  }

  async fetchPageContent(pageId) {
    try {
      const response = await fetch(`pages/${pageId}.html`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      return await response.text();
    } catch (error) {
      console.error(`Failed to fetch ${pageId}.html:`, error);
      return this.getFallbackContent(pageId);
    }
  }

  getFallbackContent(pageId) {
    const fallbackContent = {
      inbox: `
        <div class="empty-state">
          <i class="fas fa-inbox"></i>
          <h3>Welcome to Synapse</h3>
          <p>Your AI-powered notes will appear here</p>
        </div>
      `,
      calendar: `
        <div class="empty-state">
          <i class="fas fa-calendar"></i>
          <h3>Calendar</h3>
          <p>Your scheduled events will appear here</p>
        </div>
      `,
      todos: `
        <div class="empty-state">
          <i class="fas fa-check-square"></i>
          <h3>Todo List</h3>
          <p>Your tasks will appear here</p>
        </div>
      `,
      notes: `
        <div class="empty-state">
          <i class="fas fa-sticky-note"></i>
          <h3>Notes</h3>
          <p>Your notes will appear here</p>
        </div>
      `
    };

    return fallbackContent[pageId] || '<div class="empty-state"><p>Page not found</p></div>';
  }

  getErrorContent(pageId) {
    return `
      <div class="empty-state">
        <i class="fas fa-exclamation-triangle"></i>
        <h3>Error Loading Page</h3>
        <p>Failed to load ${pageId} content. Please try again.</p>
        <button class="action-button primary" onclick="navigationManager.loadPageContent('${pageId}')">
          Retry
        </button>
      </div>
    `;
  }

  initializePageFeatures(pageId) {
    switch (pageId) {
      case 'calendar':
        this.initializeCalendar();
        break;
      case 'todos':
        this.initializeTodos();
        break;
      case 'notes':
        this.initializeNotes();
        break;
      case 'inbox':
        this.initializeInbox();
        break;
    }
  }

  initializeCalendar() {
    // Calendar-specific initialization
    const calendarNavButtons = document.querySelectorAll('.calendar-nav button');
    calendarNavButtons.forEach(btn => {
      btn.addEventListener('click', () => {
        calendarNavButtons.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');
      });
    });

    // Calendar day selection
    const calendarDays = document.querySelectorAll('.calendar-day');
    calendarDays.forEach(day => {
      if (!isNaN(day.textContent)) {
        day.addEventListener('click', () => {
          calendarDays.forEach(d => d.classList.remove('selected'));
          day.classList.add('selected');
        });
      }
    });
  }

  initializeTodos() {
    // Todo checkbox functionality
    const todoCheckboxes = document.querySelectorAll('.todo-checkbox');
    todoCheckboxes.forEach(checkbox => {
      checkbox.addEventListener('click', () => {
        checkbox.classList.toggle('checked');
        const todoText = checkbox.nextElementSibling;
        if (todoText) {
          todoText.classList.toggle('completed');
        }
      });
    });
  }

  initializeNotes() {
    // Notes grid functionality
    const noteCards = document.querySelectorAll('.note-card');
    noteCards.forEach(card => {
      card.addEventListener('click', () => {
        // Note card click handler
        console.log('Note card clicked:', card);
      });
    });
  }

  initializeInbox() {
    // Inbox-specific functionality
    const contentCards = document.querySelectorAll('.content-card');
    contentCards.forEach(card => {
      card.addEventListener('click', () => {
        // Content card click handler
        console.log('Content card clicked:', card);
      });
    });
  }

  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // Alt + number keys for quick navigation
      if (e.altKey && !e.ctrlKey && !e.metaKey) {
        const keyMap = {
          '1': 'inbox',
          '2': 'calendar',
          '3': 'todos',
          '4': 'notes'
        };
        
        if (keyMap[e.key]) {
          e.preventDefault();
          this.navigateToPage(keyMap[e.key]);
        }
      }
    });
  }

  loadInitialPage() {
    // Load page from URL hash or default to inbox
    const hash = window.location.hash.slice(1);
    const initialPage = this.pages.includes(hash) ? hash : 'inbox';
    this.navigateToPage(initialPage);
  }

  updateURL(pageId) {
    // Update URL hash without triggering page reload
    history.replaceState(null, null, `#${pageId}`);
  }

  triggerPageChangeEvent(pageId) {
    // Dispatch custom event for page changes
    const event = new CustomEvent('pagechange', {
      detail: { 
        page: pageId, 
        previousPage: this.currentPage 
      }
    });
    document.dispatchEvent(event);
  }

  // Public methods
  getCurrentPage() {
    return this.currentPage;
  }

  refreshCurrentPage() {
    this.pageCache.delete(this.currentPage);
    this.loadPageContent(this.currentPage);
  }

  clearCache() {
    this.pageCache.clear();
  }
}

// Auto-initialize navigation manager
document.addEventListener('DOMContentLoaded', () => {
  window.navigationManager = new NavigationManager();
});

// Handle browser back/forward buttons
window.addEventListener('hashchange', () => {
  const hash = window.location.hash.slice(1);
  if (window.navigationManager && window.navigationManager.pages.includes(hash)) {
    window.navigationManager.navigateToPage(hash);
  }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = NavigationManager;
}
