// Main Application Logic

class SynapseApp {
  constructor() {
    this.aiProcessing = false;
    this.fabExpanded = false;
    this.panelOpen = false;
    this.recognition = null;
    this.contentBlocks = [];
    this.contentData = {
      inbox: [],
      calendar: [],
      todos: [],
      notes: []
    };
    this.init();
  }

  init() {
    this.initializeMagicFAB();
    this.loadSampleData();
    this.bindGlobalEvents();
  }

  initializeMagicFAB() {
    // Get DOM elements
    this.fabMain = document.getElementById('magicFabMain');
    this.inputPanel = document.getElementById('magicInputPanel');
    this.backdrop = document.getElementById('magicBackdrop');
    this.processingRing = document.getElementById('aiProcessingRing');

    // Unified content elements
    this.contentBlocksContainer = document.getElementById('contentBlocks');
    this.emptyContentState = document.getElementById('emptyContentState');
    this.contentCounter = document.getElementById('contentCounter');

    // Quick action buttons
    this.quickTextBtn = document.getElementById('quickTextBtn');
    this.quickVoiceBtn = document.getElementById('quickVoiceBtn');
    this.quickClipboardBtn = document.getElementById('quickClipboardBtn');
    this.quickImageBtn = document.getElementById('quickImageBtn');

    // Inline input elements
    this.inlineTextInput = document.getElementById('inlineTextInput');
    this.inlineTextarea = document.getElementById('inlineTextarea');
    this.addInlineTextBtn = document.getElementById('addInlineTextBtn');
    this.cancelTextBtn = document.getElementById('cancelTextBtn');

    // Voice recording elements
    this.voiceRecordingStatus = document.getElementById('voiceRecordingStatus');
    this.voiceStatusText = document.getElementById('voiceStatusText');
    this.stopRecordingBtn = document.getElementById('stopRecordingBtn');

    // Processing elements
    this.processingActionsNormal = document.getElementById('processingActionsNormal');
    this.processingActionsPost = document.getElementById('processingActionsPost');
    this.contentSummary = document.getElementById('contentSummary');
    this.processUnifiedBtn = document.getElementById('processUnifiedBtn');
    this.saveUnifiedBtn = document.getElementById('saveUnifiedBtn');
    this.clearAllBtn = document.getElementById('clearAllBtn');
    this.saveContentBtn = document.getElementById('saveContentBtn');
    this.clearAfterProcessBtn = document.getElementById('clearAfterProcessBtn');
    this.panelCloseBtn = document.getElementById('panelCloseBtn');

    // File input and options
    this.imageFileInput = document.getElementById('imageFileInput');
    this.analyzeImagesCheck = document.getElementById('analyzeImagesCheck');
    this.attachImagesCheck = document.getElementById('attachImagesCheck');

    if (!this.fabMain) return;

    this.bindFABEvents();
    this.bindQuickActionEvents();
    this.bindInlineInputEvents();
    this.bindVoiceEvents();
    this.bindClipboardEvents();
    this.bindImageEvents();
    this.bindSuggestionEvents();
  }

  bindFABEvents() {
    // Main FAB click - directly opens panel
    this.fabMain.addEventListener('click', () => {
      if (this.panelOpen) {
        this.closePanel();
      } else {
        this.openInputPanel();
      }
    });

    // Close panel button
    this.panelCloseBtn.addEventListener('click', () => {
      this.closePanel();
    });

    // Backdrop click
    this.backdrop.addEventListener('click', () => {
      this.closePanel();
    });
  }

  bindQuickActionEvents() {
    // Quick action buttons
    this.quickTextBtn.addEventListener('click', () => {
      this.showInlineTextInput();
    });

    this.quickVoiceBtn.addEventListener('click', () => {
      this.startVoiceRecording();
    });

    this.quickClipboardBtn.addEventListener('click', () => {
      this.pasteFromClipboard();
    });

    this.quickImageBtn.addEventListener('click', () => {
      this.imageFileInput.click();
    });
  }

  bindInlineInputEvents() {
    // Inline text input events
    this.inlineTextarea.addEventListener('input', () => {
      this.updateInlineAddButton();
    });

    this.inlineTextarea.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        this.addInlineTextContent();
      } else if (e.key === 'Escape') {
        this.hideInlineTextInput();
      }
    });

    this.addInlineTextBtn.addEventListener('click', () => {
      this.addInlineTextContent();
    });

    this.cancelTextBtn.addEventListener('click', () => {
      this.hideInlineTextInput();
    });

    // Processing buttons
    this.processUnifiedBtn.addEventListener('click', () => {
      this.processUnifiedContent();
    });

    this.saveUnifiedBtn.addEventListener('click', () => {
      this.saveUnifiedContentDraft();
    });

    this.clearAllBtn.addEventListener('click', () => {
      this.clearAllContent();
    });

    // Post-processing buttons
    this.saveContentBtn.addEventListener('click', () => {
      this.saveContentForReuse();
    });

    this.clearAfterProcessBtn.addEventListener('click', () => {
      this.clearAllContent();
    });
  }

  openInputPanel() {
    this.panelOpen = true;

    // Show panel and backdrop
    this.inputPanel.classList.add('show');
    this.backdrop.classList.add('show');

    // Add visual state to FAB container
    document.getElementById('magicFabContainer').classList.add('panel-open');

    // Auto-show inline text input by default
    setTimeout(() => {
      this.showInlineTextInput();
    }, 300);
  }

  // Inline Text Input Methods
  showInlineTextInput() {
    this.inlineTextInput.style.display = 'block';
    this.quickTextBtn.classList.add('active');
    setTimeout(() => {
      this.inlineTextarea.focus();
    }, 100);
  }

  hideInlineTextInput() {
    this.inlineTextInput.style.display = 'none';
    this.quickTextBtn.classList.remove('active');
    this.inlineTextarea.value = '';
    this.updateInlineAddButton();
  }

  addInlineTextContent() {
    const text = this.inlineTextarea.value.trim();
    if (!text) return;

    this.addContentBlock({
      type: 'text',
      content: text,
      metadata: { timestamp: new Date() }
    });

    this.hideInlineTextInput();
  }

  updateInlineAddButton() {
    const hasText = this.inlineTextarea.value.trim().length > 0;
    this.addInlineTextBtn.disabled = !hasText;
  }

  // Unified Content Management
  addTextContent() {
    const text = this.unifiedTextarea.value.trim();
    if (!text) return;

    this.addContentBlock({
      type: 'text',
      content: text,
      metadata: { timestamp: new Date() }
    });

    // Text input is handled by inline text input system
    this.hideInlineTextInput();
  }

  addContentBlock(blockData) {
    const blockId = Date.now() + Math.random();
    const block = {
      id: blockId,
      ...blockData
    };

    console.log('Adding content block:', block);
    this.contentBlocks.push(block);
    console.log('Total content blocks:', this.contentBlocks.length);

    this.renderContentBlock(block);
    this.updateContentCounter();
    this.updateProcessButton();
    this.hideEmptyState();
  }

  renderContentBlock(block) {
    console.log('Rendering content block:', block);
    console.log('Content blocks container:', this.contentBlocksContainer);

    const blockElement = document.createElement('div');
    blockElement.className = 'content-block';
    blockElement.setAttribute('data-block-id', block.id);

    const typeIcons = {
      text: 'fas fa-keyboard',
      voice: 'fas fa-microphone',
      clipboard: 'fas fa-clipboard',
      image: 'fas fa-image',
      analyzed: 'fas fa-search'
    };

    blockElement.innerHTML = `
      <div class="content-block-header">
        <div class="content-block-type ${block.type}">
          <i class="${typeIcons[block.type]}"></i>
          <span>${block.type.charAt(0).toUpperCase() + block.type.slice(1)}</span>
        </div>
        <div class="content-block-actions">
          <button class="content-block-btn edit-btn" title="Edit">
            <i class="fas fa-edit"></i>
          </button>
          <button class="content-block-btn delete-btn" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        </div>
      </div>
      <div class="content-block-content">
        ${this.renderBlockContent(block)}
      </div>
      ${block.metadata ? `<div class="content-block-metadata">${this.renderBlockMetadata(block.metadata)}</div>` : ''}
    `;

    // Bind block events
    blockElement.querySelector('.delete-btn').addEventListener('click', () => {
      this.removeContentBlock(block.id);
    });

    blockElement.querySelector('.edit-btn').addEventListener('click', () => {
      this.editContentBlock(block.id);
    });

    this.contentBlocksContainer.appendChild(blockElement);

    // Scroll to the new block after a brief delay for animation
    setTimeout(() => {
      blockElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest'
      });
    }, 100);
  }

  renderBlockContent(block) {
    switch (block.type) {
      case 'text':
      case 'voice':
      case 'clipboard':
        return `<p>${block.content}</p>`;
      case 'image':
        return `
          <img src="${block.content}" class="content-block-image" alt="Content image">
          ${block.analysis ? `<p><strong>Analysis:</strong> ${block.analysis}</p>` : ''}
        `;
      case 'analyzed':
        return `
          <p><strong>Extracted:</strong> ${block.content}</p>
          ${block.originalImage ? `<img src="${block.originalImage}" class="content-block-image" alt="Original image">` : ''}
        `;
      default:
        return `<p>${block.content}</p>`;
    }
  }

  renderBlockMetadata(metadata) {
    const parts = [];
    if (metadata.timestamp) {
      parts.push(`Added: ${metadata.timestamp.toLocaleTimeString()}`);
    }
    if (metadata.source) {
      parts.push(`Source: ${metadata.source}`);
    }
    return parts.join(' • ');
  }

  removeContentBlock(blockId) {
    this.contentBlocks = this.contentBlocks.filter(block => block.id !== blockId);
    const blockElement = document.querySelector(`[data-block-id="${blockId}"]`);
    if (blockElement) {
      blockElement.remove();
    }
    this.updateContentCounter();
    this.updateProcessButton();
    if (this.contentBlocks.length === 0) {
      this.showEmptyState();
    }
  }

  editContentBlock(blockId) {
    const block = this.contentBlocks.find(b => b.id === blockId);
    if (block && block.type === 'text') {
      const newContent = prompt('Edit content:', block.content);
      if (newContent !== null) {
        block.content = newContent.trim();
        this.renderAllContentBlocks();
      }
    }
  }

  updateAddTextButton() {
    // This method is now handled by updateInlineAddButton
    // Keeping for compatibility but redirecting to correct method
    this.updateInlineAddButton();
  }

  updateProcessButton() {
    const hasContent = this.contentBlocks.length > 0;
    console.log('updateProcessButton called:', {
      contentBlocksLength: this.contentBlocks.length,
      hasContent: hasContent,
      processUnifiedBtn: this.processUnifiedBtn,
      saveUnifiedBtn: this.saveUnifiedBtn
    });

    if (this.processUnifiedBtn) {
      this.processUnifiedBtn.disabled = !hasContent;
    }
    if (this.saveUnifiedBtn) {
      this.saveUnifiedBtn.disabled = !hasContent;
    }
    this.updateContentSummary();
  }

  updateContentSummary() {
    const count = this.contentBlocks.length;
    if (count === 0) {
      this.contentSummary.innerHTML = `
        <i class="fas fa-layer-group"></i>
        <span>Ready to process unified content</span>
      `;
      this.contentSummary.classList.remove('has-content');
    } else {
      const types = [...new Set(this.contentBlocks.map(block => block.type))];
      const typeText = types.length === 1 ? types[0] : `${types.length} types`;
      this.contentSummary.innerHTML = `
        <i class="fas fa-layer-group"></i>
        <span>${count} content blocks (${typeText}) ready for unified processing</span>
      `;
      this.contentSummary.classList.add('has-content');
    }
  }

  updateContentCounter() {
    const count = this.contentBlocks.length;
    this.contentCounter.querySelector('.counter-badge').textContent = count;
  }

  hideEmptyState() {
    this.emptyContentState.classList.add('hidden');
  }

  showEmptyState() {
    this.emptyContentState.classList.remove('hidden');
  }

  clearAllContent() {
    this.contentBlocks = [];
    this.contentBlocksContainer.innerHTML = '';
    this.updateContentCounter();
    this.updateProcessButton();
    this.showEmptyState();
    this.hideInlineTextInput();
    this.hidePostProcessingState();
  }

  // Post-Processing State Management
  showPostProcessingState() {
    this.processingActionsNormal.style.display = 'none';
    this.processingActionsPost.style.display = 'flex';
  }

  hidePostProcessingState() {
    this.processingActionsNormal.style.display = 'flex';
    this.processingActionsPost.style.display = 'none';
  }

  saveContentForReuse() {
    // Hide post-processing state and return to normal editing mode
    this.hidePostProcessingState();

    // Show a brief confirmation message
    this.showSuccessMessage('Content saved for editing');

    // Auto-show text input for continued editing
    setTimeout(() => {
      this.showInlineTextInput();
    }, 500);
  }

  showSuccessMessage(message) {
    // Create a temporary success message
    const successMsg = document.createElement('div');
    successMsg.className = 'temp-success-message';
    successMsg.innerHTML = `
      <i class="fas fa-check"></i>
      <span>${message}</span>
    `;
    successMsg.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: var(--accent-success);
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;
      z-index: 1000;
      animation: fadeInOut 2s ease-in-out forwards;
    `;

    this.inputPanel.appendChild(successMsg);

    // Remove after animation
    setTimeout(() => {
      if (successMsg.parentNode) {
        successMsg.parentNode.removeChild(successMsg);
      }
    }, 2000);
  }

  renderAllContentBlocks() {
    this.contentBlocksContainer.innerHTML = '';
    this.contentBlocks.forEach(block => this.renderContentBlock(block));
  }

  processUnifiedContent() {
    if (this.contentBlocks.length === 0) return;

    // Combine all content blocks into a unified content string
    const unifiedContent = this.buildUnifiedContent();
    this.processInput(unifiedContent);
  }

  saveUnifiedContentDraft() {
    if (this.contentBlocks.length === 0) return;

    // Build unified content for saving
    const unifiedContent = this.buildUnifiedContent();
    const timestamp = new Date().toLocaleString();

    // Create a draft object
    const draft = {
      id: Date.now(),
      timestamp: timestamp,
      content: unifiedContent,
      blocks: [...this.contentBlocks],
      blockCount: this.contentBlocks.length,
      types: [...new Set(this.contentBlocks.map(block => block.type))]
    };

    // Save to localStorage (in a real app, this would go to a server)
    const savedDrafts = JSON.parse(localStorage.getItem('synapseContentDrafts') || '[]');
    savedDrafts.unshift(draft); // Add to beginning

    // Keep only last 10 drafts
    if (savedDrafts.length > 10) {
      savedDrafts.splice(10);
    }

    localStorage.setItem('synapseContentDrafts', JSON.stringify(savedDrafts));

    // Show success message
    this.showSuccessMessage(`Draft saved with ${draft.blockCount} content blocks`);

    // Update UI to show saved state
    this.contentSummary.innerHTML = `
      <i class="fas fa-check-circle"></i>
      <span>Draft saved at ${timestamp}</span>
    `;
    this.contentSummary.classList.add('has-content');

    // Reset summary after 3 seconds
    setTimeout(() => {
      this.updateContentSummary();
    }, 3000);
  }

  buildUnifiedContent() {
    let content = '';
    const textBlocks = [];
    const images = [];
    const metadata = {
      totalBlocks: this.contentBlocks.length,
      types: [...new Set(this.contentBlocks.map(block => block.type))],
      timestamp: new Date().toISOString()
    };

    // Process each content block in order to maintain context
    this.contentBlocks.forEach((block, index) => {
      switch (block.type) {
        case 'text':
          textBlocks.push(`[Text Block ${index + 1}]: ${block.content}`);
          break;
        case 'voice':
          textBlocks.push(`[Voice Note ${index + 1}]: ${block.content}`);
          break;
        case 'clipboard':
          textBlocks.push(`[Pasted Content ${index + 1}]: ${block.content}`);
          break;
        case 'analyzed':
          textBlocks.push(`[Document Analysis ${index + 1}]: ${block.content}`);
          break;
        case 'image':
          if (this.analyzeImagesCheck.checked && this.isDocumentImage(block)) {
            textBlocks.push(`[Image Analysis ${index + 1}]: ${block.analysis || 'Document/screenshot content extracted from image'}`);
          }
          if (this.attachImagesCheck.checked) {
            images.push({
              index: index + 1,
              filename: block.metadata?.filename || `image-${index + 1}`,
              data: block.content
            });
          }
          break;
      }
    });

    // Build unified content with clear structure
    content = `=== UNIFIED CONTENT ANALYSIS ===\n`;
    content += `Content Blocks: ${metadata.totalBlocks}\n`;
    content += `Content Types: ${metadata.types.join(', ')}\n`;
    content += `Created: ${new Date().toLocaleString()}\n\n`;

    content += `=== CONTENT BODY ===\n`;
    content += textBlocks.join('\n\n');

    if (images.length > 0) {
      content += `\n\n=== ATTACHED FILES ===\n`;
      images.forEach(img => {
        content += `- ${img.filename} (Image ${img.index})\n`;
      });
    }

    content += `\n\n=== PROCESSING INSTRUCTIONS ===\n`;
    content += `Please analyze this unified content as a single cohesive unit and categorize it appropriately as:\n`;
    content += `- Calendar event (if it contains dates, times, or scheduling information)\n`;
    content += `- Todo item (if it contains tasks, action items, or things to do)\n`;
    content += `- Note (for general information, ideas, or reference material)\n`;
    content += `\nConsider all content blocks together when making this determination.`;

    return content;
  }

  isDocumentImage(block) {
    // Simple heuristic to determine if image is a document
    // In a real app, this would use image analysis
    return block.metadata && (
      block.metadata.source === 'screenshot' ||
      block.content.includes('data:image') // Base64 images are often screenshots
    );
  }

  closePanel() {
    this.panelOpen = false;

    this.inputPanel.classList.remove('show');
    this.backdrop.classList.remove('show');

    // Remove visual state from FAB container
    document.getElementById('magicFabContainer').classList.remove('panel-open');

    // Reset any active states
    this.hideInlineTextInput();
    this.stopVoiceRecording();
    this.quickClipboardBtn.classList.remove('active');
    this.hidePostProcessingState();
  }

  updateInputMethodDisplay(method) {
    const indicator = document.getElementById('inputMethodIndicator');
    const methodConfig = {
      text: { icon: 'fas fa-keyboard', label: 'Text Input' },
      voice: { icon: 'fas fa-microphone', label: 'Voice Input' },
      clipboard: { icon: 'fas fa-clipboard', label: 'Clipboard' },
      image: { icon: 'fas fa-image', label: 'Image Input' }
    };

    const config = methodConfig[method];
    indicator.innerHTML = `<i class="${config.icon}"></i><span>${config.label}</span>`;
  }

  showInputMethodContent(method) {
    // Hide all content
    document.querySelectorAll('.input-method-content').forEach(content => {
      content.classList.remove('active');
    });

    // Show selected content
    document.getElementById(`${method}InputContent`).classList.add('active');
  }

  // Removed duplicate updateProcessButton method - using unified version above

  resetInputStates() {
    // Reset any active input states
    this.hideInlineTextInput();
    this.stopVoiceRecording();

    // Reset quick action button states
    this.quickClipboardBtn.classList.remove('active');
  }

  bindVoiceEvents() {
    this.stopRecordingBtn.addEventListener('click', () => {
      this.stopVoiceRecording();
    });
  }

  bindClipboardEvents() {
    // Clipboard functionality is now handled by the quick action button
    // No additional events needed here
  }

  bindImageEvents() {
    // File input change
    this.imageFileInput.addEventListener('change', (e) => {
      const files = Array.from(e.target.files);
      files.forEach(file => {
        if (file.type.startsWith('image/')) {
          this.handleImageFile(file);
        }
      });
    });

    // Drag and drop on the entire content area
    this.contentBlocksContainer.addEventListener('dragover', (e) => {
      e.preventDefault();
      this.contentBlocksContainer.classList.add('dragover');
    });

    this.contentBlocksContainer.addEventListener('dragleave', (e) => {
      if (!this.contentBlocksContainer.contains(e.relatedTarget)) {
        this.contentBlocksContainer.classList.remove('dragover');
      }
    });

    this.contentBlocksContainer.addEventListener('drop', (e) => {
      e.preventDefault();
      this.contentBlocksContainer.classList.remove('dragover');

      const files = Array.from(e.dataTransfer.files);
      files.forEach(file => {
        if (file.type.startsWith('image/')) {
          this.handleImageFile(file);
        }
      });
    });
  }

  bindSuggestionEvents() {
    document.querySelectorAll('.suggestion-chip').forEach(chip => {
      chip.addEventListener('click', () => {
        const text = chip.getAttribute('data-text');

        // Add suggestion as a content block
        this.addContentBlock({
          type: 'text',
          content: text,
          metadata: { timestamp: new Date(), source: 'suggestion' }
        });
      });
    });
  }

  // Voice Input Methods
  toggleVoiceRecording() {
    if (!this.recognition) {
      this.initializeVoiceRecognition();
    }

    if (this.voiceRecordBtn.classList.contains('recording')) {
      this.stopVoiceRecording();
    } else {
      this.startVoiceRecording();
    }
  }

  initializeVoiceRecognition() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();
      this.recognition.continuous = false;
      this.recognition.interimResults = true;
      this.recognition.lang = 'en-US';

      this.recognition.onstart = () => {
        this.quickVoiceBtn.classList.add('recording');
        this.voiceRecordingStatus.style.display = 'flex';
        this.voiceStatusText.textContent = 'Listening... Speak now';
      };

      this.recognition.onresult = (event) => {
        let transcript = '';
        for (let i = event.resultIndex; i < event.results.length; i++) {
          transcript += event.results[i][0].transcript;
        }
        this.voiceStatusText.textContent = `"${transcript}"`;
      };

      this.recognition.onend = () => {
        this.stopVoiceRecording();
        const finalTranscript = this.voiceStatusText.textContent.replace(/"/g, '');
        if (finalTranscript && finalTranscript !== 'Listening...') {
          this.addContentBlock({
            type: 'voice',
            content: finalTranscript,
            metadata: { timestamp: new Date(), source: 'voice' }
          });
        }
      };

      this.recognition.onerror = (event) => {
        this.stopVoiceRecording();
        this.showError(`Voice recognition error: ${event.error}`);
      };
    } else {
      this.showError('Voice recognition is not supported in this browser.');
    }
  }

  startVoiceRecording() {
    if (this.recognition) {
      this.recognition.start();
    }
  }

  stopVoiceRecording() {
    if (this.recognition) {
      this.recognition.stop();
    }
    this.quickVoiceBtn.classList.remove('recording');
    this.voiceRecordingStatus.style.display = 'none';
    this.voiceStatusText.textContent = 'Listening...';
  }

  // Clipboard Methods
  async pasteFromClipboard() {
    try {
      const text = await navigator.clipboard.readText();
      if (text) {
        // Visual feedback on button
        this.quickClipboardBtn.classList.add('active');

        // Add clipboard content to unified content
        this.addContentBlock({
          type: 'clipboard',
          content: text,
          metadata: { timestamp: new Date(), source: 'clipboard' }
        });

        // Reset button state after a delay
        setTimeout(() => {
          this.quickClipboardBtn.classList.remove('active');
        }, 1000);
      }
    } catch (error) {
      this.showError('Unable to access clipboard. Please paste manually.');
    }
  }

  // Image Methods
  handleImageFile(file) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageData = e.target.result;

      // Determine if this is likely a document image
      const isDocument = file.name.toLowerCase().includes('screenshot') ||
                        file.name.toLowerCase().includes('document') ||
                        file.type === 'image/png'; // Screenshots are often PNG

      if (isDocument && this.analyzeImagesCheck.checked) {
        // Simulate document analysis
        const analysisText = `Document content extracted from ${file.name}: This appears to contain text and structured information that could be relevant for note-taking.`;

        this.addContentBlock({
          type: 'analyzed',
          content: analysisText,
          originalImage: imageData,
          metadata: {
            timestamp: new Date(),
            source: 'image-analysis',
            filename: file.name
          }
        });
      } else {
        // Add as regular image attachment
        this.addContentBlock({
          type: 'image',
          content: imageData,
          metadata: {
            timestamp: new Date(),
            source: 'image-upload',
            filename: file.name
          }
        });
      }
    };
    reader.readAsDataURL(file);
  }

  // Text Processing - now handled by unified content system
  // processTextInput method removed - use processUnifiedContent instead

  async processInput(text) {
    const aiProcessingModal = document.getElementById('aiProcessing');

    if (!aiProcessingModal) return;

    this.aiProcessing = true;

    // Show processing state
    this.processingRing.classList.add('active');
    aiProcessingModal.classList.add('show');

    // Close panel after starting processing
    if (this.panelOpen) {
      this.closePanel();
    }

    try {
      // Simulate AI processing with more realistic feedback
      await this.simulateAIProcessing();

      // Process and categorize the input
      const processedContent = this.categorizeContent(text);

      // Add to appropriate data store
      this.addContent(processedContent);

      // Show success feedback
      this.showSuccessFeedback(processedContent.type);

      // Refresh current page if it's affected
      this.refreshAffectedPages(processedContent.type);

      // Show post-processing options instead of auto-clearing
      this.showPostProcessingState();

    } catch (error) {
      console.error('Error processing input:', error);
      this.showError('Failed to process input. Please try again.');
    } finally {
      // Hide processing state
      this.aiProcessing = false;
      this.processingRing.classList.remove('active');
      aiProcessingModal.classList.remove('show');
    }
  }

  showSuccessFeedback(contentType) {
    const typeColors = {
      calendar: '#3B82F6',
      todo: '#22C55E',
      note: '#8B5CF6'
    };

    // Animate FAB with success color
    const originalBackground = this.fabMain.style.background;
    this.fabMain.style.background = typeColors[contentType] || '#22C55E';
    this.fabMain.style.transform = 'scale(1.2)';

    setTimeout(() => {
      this.fabMain.style.background = originalBackground;
      this.fabMain.style.transform = 'scale(1)';
    }, 1000);
  }

  async simulateAIProcessing() {
    // Simulate AI processing delay
    const processingTime = Math.random() * 1000 + 1500; // 1.5-2.5 seconds
    return new Promise(resolve => setTimeout(resolve, processingTime));
  }

  categorizeContent(text) {
    const lowerText = text.toLowerCase();
    
    // Simple AI categorization logic
    let type = 'note';
    let title = 'AI Processed Content';
    
    // Calendar keywords
    if (lowerText.includes('meeting') || lowerText.includes('appointment') || 
        lowerText.includes('schedule') || lowerText.includes('call') ||
        lowerText.includes('tomorrow') || lowerText.includes('today') ||
        lowerText.includes('next week') || lowerText.includes('at ')) {
      type = 'calendar';
      title = this.extractEventTitle(text);
    }
    // Todo keywords
    else if (lowerText.includes('need to') || lowerText.includes('must') || 
             lowerText.includes('should') || lowerText.includes('task') ||
             lowerText.includes('complete') || lowerText.includes('finish') ||
             lowerText.includes('do ') || lowerText.includes('buy ')) {
      type = 'todo';
      title = this.extractTaskTitle(text);
    }
    // Default to note
    else {
      title = this.extractNoteTitle(text);
    }

    return {
      id: Date.now(),
      type,
      title,
      content: text,
      timestamp: new Date(),
      processed: true
    };
  }

  extractEventTitle(text) {
    // Extract meaningful event title
    const patterns = [
      /meeting with (.+)/i,
      /call with (.+)/i,
      /appointment (.+)/i,
      /schedule (.+)/i
    ];
    
    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) return match[1].trim();
    }
    
    return text.length > 30 ? text.substring(0, 30) + '...' : text;
  }

  extractTaskTitle(text) {
    // Extract meaningful task title
    const cleanText = text.replace(/^(need to|must|should|task:?)\s*/i, '');
    return cleanText.length > 40 ? cleanText.substring(0, 40) + '...' : cleanText;
  }

  extractNoteTitle(text) {
    // Extract meaningful note title
    const firstSentence = text.split('.')[0];
    return firstSentence.length > 30 ? firstSentence.substring(0, 30) + '...' : firstSentence;
  }

  addContent(content) {
    // Add to inbox (always)
    this.contentData.inbox.unshift(content);
    
    // Add to specific category
    if (content.type !== 'inbox') {
      this.contentData[content.type].unshift(content);
    }
    
    // Limit inbox to 50 items
    if (this.contentData.inbox.length > 50) {
      this.contentData.inbox = this.contentData.inbox.slice(0, 50);
    }
  }

  refreshAffectedPages(contentType) {
    // Refresh inbox and the specific content type page
    if (window.navigationManager) {
      const currentPage = window.navigationManager.getCurrentPage();
      
      if (currentPage === 'inbox' || currentPage === contentType) {
        window.navigationManager.refreshCurrentPage();
      }
    }
  }

  loadSampleData() {
    // Load some sample data for demonstration
    const sampleData = [
      {
        id: 1,
        type: 'calendar',
        title: 'Team Meeting',
        content: 'Weekly team sync at 2 PM tomorrow',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        processed: true
      },
      {
        id: 2,
        type: 'todo',
        title: 'Complete Design',
        content: 'Finish the UI design for the new feature by Friday',
        timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
        processed: true
      },
      {
        id: 3,
        type: 'note',
        title: 'AI Design Thoughts',
        content: 'Some thoughts on how to better integrate AI into user experience...',
        timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        processed: true
      }
    ];

    sampleData.forEach(item => {
      this.contentData.inbox.push(item);
      if (item.type !== 'inbox') {
        this.contentData[item.type].push(item);
      }
    });
  }

  bindGlobalEvents() {
    // Listen for page changes
    document.addEventListener('pagechange', (e) => {
      console.log('Page changed to:', e.detail.page);
    });

    // Handle escape key to close panel
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        if (this.panelOpen) {
          this.closePanel();
        }
      }

      // Quick access shortcuts
      if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
          case 'k':
            e.preventDefault();
            this.openInputPanel('text');
            break;
          case 'j':
            e.preventDefault();
            this.openInputPanel('voice');
            break;
          case 'v':
            e.preventDefault();
            this.openInputPanel('clipboard');
            break;
          case 'i':
            e.preventDefault();
            this.openInputPanel('image');
            break;
        }
      }
    });

    // Handle click outside events (removed FAB options handling)

    // Auto-focus shortcuts when typing
    document.addEventListener('keydown', (e) => {
      if (!e.ctrlKey && !e.metaKey && !e.altKey &&
          e.key.length === 1 &&
          document.activeElement.tagName !== 'INPUT' &&
          document.activeElement.tagName !== 'TEXTAREA' &&
          !this.panelOpen) {
        this.openInputPanel();
        setTimeout(() => {
          this.showInlineTextInput();
        }, 300);
      }
    });
  }

  showError(message) {
    // Simple error notification
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-notification';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: #EF4444;
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      z-index: 2000;
      font-size: 14px;
    `;
    
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
      errorDiv.remove();
    }, 3000);
  }

  // Public methods for external access
  getContentData() {
    return this.contentData;
  }

  addManualContent(type, title, content) {
    const newContent = {
      id: Date.now(),
      type,
      title,
      content,
      timestamp: new Date(),
      processed: false
    };

    this.addContent(newContent);
    this.refreshAffectedPages(type);
    return newContent;
  }

  // Debug and test methods
  testAddContentBlocks() {
    console.log('Testing content block system...');

    // Add test content blocks
    this.addContentBlock({
      type: 'text',
      content: 'This is a test text block',
      metadata: { timestamp: new Date(), source: 'test' }
    });

    this.addContentBlock({
      type: 'voice',
      content: 'This is a test voice transcription',
      metadata: { timestamp: new Date(), source: 'test' }
    });

    this.addContentBlock({
      type: 'clipboard',
      content: 'This is test clipboard content',
      metadata: { timestamp: new Date(), source: 'test' }
    });

    console.log('Added 3 test content blocks');
    console.log('Current content blocks:', this.contentBlocks);
    console.log('Process button should now be enabled');
  }

  // Public methods for debugging
  getContentBlocks() {
    return this.contentBlocks;
  }

  getButtonStates() {
    return {
      processUnifiedBtn: this.processUnifiedBtn ? !this.processUnifiedBtn.disabled : 'not found',
      saveUnifiedBtn: this.saveUnifiedBtn ? !this.saveUnifiedBtn.disabled : 'not found',
      contentBlocksLength: this.contentBlocks.length
    };
  }

  // Layout debugging method
  testLayoutWithMixedContent() {
    console.log('Testing layout with mixed content...');

    // Add various content types to test layout
    this.addContentBlock({
      type: 'text',
      content: 'This is a long text block that should wrap properly within the container boundaries and not cause any overflow issues. It contains multiple sentences to test text wrapping behavior.',
      metadata: { timestamp: new Date(), source: 'layout-test' }
    });

    this.addContentBlock({
      type: 'voice',
      content: 'Short voice note for testing.',
      metadata: { timestamp: new Date(), source: 'layout-test' }
    });

    this.addContentBlock({
      type: 'clipboard',
      content: 'Clipboard content with some special characters: !@#$%^&*()_+-=[]{}|;:,.<>?',
      metadata: { timestamp: new Date(), source: 'layout-test' }
    });

    // Simulate image content
    this.addContentBlock({
      type: 'image',
      content: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzMzNzNkYyIvPjx0ZXh0IHg9IjEwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPlRlc3QgSW1hZ2U8L3RleHQ+PC9zdmc+',
      metadata: { timestamp: new Date(), source: 'layout-test', filename: 'test-image.svg' }
    });

    console.log('Added mixed content for layout testing');
    console.log('Check that all content displays within container boundaries');
  }
}

// Auto-initialize app
document.addEventListener('DOMContentLoaded', () => {
  window.synapseApp = new SynapseApp();
  console.log('Synapse app initialized. Use window.synapseApp for debugging.');
  console.log('Test the unified content system with: window.synapseApp.testAddContentBlocks()');
  console.log('Test layout with mixed content: window.synapseApp.testLayoutWithMixedContent()');
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SynapseApp;
}
