<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synapse Demo - Project Overview</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .demo-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        h1 {
            color: #1F2937;
            font-size: 32px;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .subtitle {
            color: #6B7280;
            font-size: 18px;
            margin-bottom: 30px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #F9FAFB;
            padding: 20px;
            border-radius: 12px;
            text-align: left;
        }
        
        .feature h3 {
            color: #3B82F6;
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        .feature p {
            color: #6B7280;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .demo-button {
            display: inline-block;
            background: #3B82F6;
            color: white;
            padding: 16px 32px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            margin: 10px;
            transition: all 0.2s ease;
        }
        
        .demo-button:hover {
            background: #2563EB;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        
        .demo-button.secondary {
            background: #6B7280;
        }
        
        .demo-button.secondary:hover {
            background: #4B5563;
        }
        
        .tech-stack {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 1px solid #E5E7EB;
        }
        
        .tech-stack h3 {
            color: #1F2937;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }
        
        .tech-tag {
            background: #EEF2FF;
            color: #3B82F6;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        
        @media (max-width: 480px) {
            .demo-container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 28px;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="logo">🧠</div>
        <h1>Synapse</h1>
        <p class="subtitle">AI-Native Note App Prototype</p>
        
        <div class="features">
            <div class="feature">
                <h3>🎨 Dual Themes</h3>
                <p>Beautiful light and dark modes with smooth transitions and system preference detection</p>
            </div>
            
            <div class="feature">
                <h3>🪄 Magic Input</h3>
                <p>AI-powered input that automatically categorizes content into calendar, todos, and notes</p>
            </div>
            
            <div class="feature">
                <h3>📱 Mobile First</h3>
                <p>Responsive design optimized for mobile devices with touch-friendly interactions</p>
            </div>
            
            <div class="feature">
                <h3>🗂️ Modular Design</h3>
                <p>Clean architecture with separate HTML files for each page and shared resources</p>
            </div>
        </div>
        
        <div>
            <a href="synapse-complete.html" class="demo-button">Launch Complete App</a>
            <a href="index.html" class="demo-button secondary">Modular Version</a>
            <a href="README.md" class="demo-button secondary">Documentation</a>
        </div>
        
        <div class="tech-stack">
            <h3>Technology Stack</h3>
            <div class="tech-tags">
                <span class="tech-tag">HTML5</span>
                <span class="tech-tag">CSS3</span>
                <span class="tech-tag">Vanilla JavaScript</span>
                <span class="tech-tag">CSS Variables</span>
                <span class="tech-tag">Font Awesome</span>
                <span class="tech-tag">Inter Font</span>
                <span class="tech-tag">Responsive Design</span>
                <span class="tech-tag">Dark Mode</span>
            </div>
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.querySelectorAll('.feature').forEach((feature, index) => {
            feature.style.animationDelay = `${index * 0.1}s`;
            feature.style.animation = 'fadeInUp 0.6s ease forwards';
        });
        
        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(20px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
            
            .feature {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
