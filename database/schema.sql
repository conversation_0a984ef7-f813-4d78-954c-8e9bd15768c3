-- Synapse AI Notes Database Schema for Cloudflare D1
-- Execute this in Cloudflare Dashboard > D1 > Your Database > Console

CREATE TABLE IF NOT EXISTS entries (
  id TEXT PRIMARY KEY,
  userId TEXT NOT NULL,
  createdAt INTEGER NOT NULL,
  updatedAt INTEGER NOT NULL,
  rawText TEXT,
  blocks TEXT NOT NULL
);

-- Create index for faster queries by user
CREATE INDEX IF NOT EXISTS idx_entries_userId ON entries(userId);

-- Create index for sorting by creation date
CREATE INDEX IF NOT EXISTS idx_entries_createdAt ON entries(createdAt DESC);

-- Create index for sorting by update date
CREATE INDEX IF NOT EXISTS idx_entries_updatedAt ON entries(updatedAt DESC);
