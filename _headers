# Cloudflare Pages Headers Configuration

# Security headers for all pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=()

# Cache headers for static assets
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Service Worker
/sw.js
  Cache-Control: public, max-age=0, must-revalidate

# Manifest
/manifest.json
  Cache-Control: public, max-age=86400
  Content-Type: application/manifest+json

# API routes CORS headers
/api/*
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization
  Access-Control-Max-Age: 86400

# PWA related files
/offline.html
  Cache-Control: public, max-age=86400

# Icons and images
*.png
  Cache-Control: public, max-age=2592000
*.jpg
  Cache-Control: public, max-age=2592000
*.svg
  Cache-Control: public, max-age=2592000
*.ico
  Cache-Control: public, max-age=2592000
