# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# API Keys and Secrets
*.key
*.pem
secrets/
.secrets

# Cloudflare
.wrangler/
wrangler.toml

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
.tmp/
temp/
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE
.vscode/settings.json
.vscode/launch.json
.vscode/tasks.json
.idea/
*.swp
*.swo
*~

# Testing
coverage/
.nyc_output/
.coverage/

# Build artifacts
build/
dist/
out/

# Package manager
.pnpm-store/
.yarn/
yarn.lock
package-lock.json
