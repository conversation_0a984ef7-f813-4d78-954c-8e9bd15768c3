# Synapse AI Notes - Environment Setup Guide

## 🚀 Quick Start

### Development Mode (Current Setup)
The application is currently configured for development with mock services:

```bash
# Current state - using mock authentication and AI
npm run dev
```

### Production Mode Setup
To enable real authentication and AI services:

1. **Copy the environment file:**
   ```bash
   cp .env.local.example .env.local
   ```

2. **Update `.env.local` with your keys:**
   ```env
   VITE_ENABLE_AUTH=true
   VITE_ENABLE_AI=true
   VITE_CLERK_PUBLISHABLE_KEY=pk_test_aW5maW5pdGUtZWFnbGUtMjEuY2xlcmsuYWNjb3VudHMuZGV2JA
   VITE_GEMINI_API_KEY=AIzaSyDsK3s3bc0e0CUV3BgG-2etPwffMFP11O0
   ```

3. **Restart the development server:**
   ```bash
   npm run dev
   ```

## 🔧 Environment Variables

### Client-Side Variables (VITE_ prefix)
These are safe to expose to the browser:

- `VITE_CLERK_PUBLISHABLE_KEY` - Clerk authentication publishable key
- `VITE_GEMINI_API_KEY` - Google Gemini AI API key (dev only)
- `VITE_ENABLE_AUTH` - Enable/disable authentication
- `VITE_ENABLE_AI` - Enable/disable AI processing
- `VITE_API_BASE_URL` - API base URL
- `VITE_DEBUG_LOGGING` - Enable debug logging

### Server-Side Variables (Cloudflare Workers)
These should NEVER be exposed to the client:

- `CLERK_SECRET_KEY` - Clerk secret key (server-side only)
- `GEMINI_API_KEY` - Gemini API key (server-side only)

## 🔄 Switching Between Modes

### Mock Mode (Default)
```env
VITE_ENABLE_AUTH=false
VITE_ENABLE_AI=false
```
- Uses mock authentication (auto-login)
- Uses mock AI processing
- No external API calls

### Real Authentication Only
```env
VITE_ENABLE_AUTH=true
VITE_ENABLE_AI=false
VITE_CLERK_PUBLISHABLE_KEY=your_key_here
```
- Real Clerk authentication
- Mock AI processing

### Real AI Only
```env
VITE_ENABLE_AUTH=false
VITE_ENABLE_AI=true
VITE_GEMINI_API_KEY=your_key_here
```
- Mock authentication
- Real Gemini AI processing

### Full Production Mode
```env
VITE_ENABLE_AUTH=true
VITE_ENABLE_AI=true
VITE_CLERK_PUBLISHABLE_KEY=your_clerk_key
VITE_GEMINI_API_KEY=your_gemini_key
```
- Real authentication
- Real AI processing

## 🔐 Security Best Practices

### Development
- API keys in `.env.local` are acceptable for development
- Never commit `.env.local` to version control
- Use test/development keys when possible

### Production
- Store sensitive keys in Cloudflare Workers environment variables
- Use production keys from official providers
- Implement proper CORS and security headers

## 🛠️ Configuration Files

### Environment Files
- `.env` - Base configuration (committed)
- `.env.local` - Local overrides (not committed)
- `.env.production.example` - Production template

### Configuration Modules
- `src/config/environment.ts` - Environment management
- `src/config/clerk.ts` - Clerk authentication config
- `src/config/gemini.ts` - Gemini AI config
- `src/config/development.ts` - Development flags

## 🧪 Testing Different Configurations

### Test Authentication
1. Set `VITE_ENABLE_AUTH=true`
2. Visit the app - you should see Clerk sign-in
3. Sign up/in with a test account

### Test AI Processing
1. Set `VITE_ENABLE_AI=true`
2. Create content through Magic Input
3. Check browser console for AI processing logs
4. **Note**: Gemini API may not be available in all regions

#### Gemini API Troubleshooting
If you see "User location is not supported" errors:
- The Gemini API has geographic restrictions
- Consider using a VPN to a supported region
- Or use the fallback mechanism (which works perfectly)

To diagnose API issues, run in browser console:
```javascript
window.testGemini()
```

### Test Both
1. Enable both features
2. Sign in with Clerk
3. Create content and verify AI processing

## 🚨 Troubleshooting

### Common Issues

**"Missing Publishable Key" Error:**
- Check `VITE_CLERK_PUBLISHABLE_KEY` is set
- Ensure the key starts with `pk_`

**AI Processing Fails:**
- Verify `VITE_GEMINI_API_KEY` is correct
- Check browser console for API errors
- Ensure you have Gemini API quota

**Environment Not Loading:**
- Restart the development server after changing `.env.local`
- Check for typos in variable names
- Ensure variables have `VITE_` prefix for client-side access

### Debug Mode
Enable detailed logging:
```env
VITE_DEBUG_LOGGING=true
```

This will show:
- Environment configuration
- Authentication status
- AI service status
- API call details

## 📚 Next Steps

1. **Development**: Use current mock setup
2. **Testing**: Enable individual services as needed
3. **Production**: Set up Cloudflare Workers with server-side keys
4. **Monitoring**: Implement proper error tracking and analytics
