// ===========================================
// CLOUDFLARE WORKERS MAIN ENTRY POINT (SIMPLIFIED)
// ===========================================

import { GeminiService } from './services/geminiService'
import { DatabaseService } from './services/databaseService'
import { AuthMiddleware, CORSMiddleware, RequestContextMiddleware } from './middleware/auth'
import { ResponseBuilder, RequestValidator, Logger } from './utils/response'
import { WorkersError, ValidationError, AuthenticationError } from './types'
import type { Env, RequestContext, ImageData, AudioData } from './types'

// Main Workers handler
export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    // Initialize middleware with environment-based origins
    const allowedOrigins = env.NODE_ENV === 'development'
      ? ['http://localhost:5173', 'http://127.0.0.1:5173']
      : ['https://synapse-ai-notes.pages.dev', 'https://synapse-ai-notes.com']

    const corsMiddleware = new CORSMiddleware(allowedOrigins)
    const contextMiddleware = new RequestContextMiddleware()

    // Create request context
    const context: RequestContext = contextMiddleware.createContext(request)
    const logger = new Logger(context.requestId)
    
    try {
      // Handle CORS preflight
      if (request.method === 'OPTIONS') {
        return corsMiddleware.handlePreflight(request)
      }

      // Add request ID header and CORS headers to all responses
      const addRequestIdHeader = (response: Response): Response => {
        response.headers.set('X-Request-ID', context.requestId)
        return corsMiddleware.addCORSHeaders(response, request)
      }

      // Parse URL and route to appropriate handler
      const url = new URL(request.url)
      const pathSegments = url.pathname.split('/').filter(Boolean)
      
      let response: Response

      // Route based on path
      if (pathSegments[0] === 'api') {
        if (pathSegments[1] === 'entries') {
          response = await handleEntriesAPI(request, env, context)
        } else if (pathSegments[1] === 'analyze') {
          response = await handleAnalyzeAPI(request, env, context)
        } else if (pathSegments[1] === 'user' && pathSegments[2] === 'profile') {
          response = await handleUserProfileAPI(request, env, context)
        } else if (pathSegments[1] === 'health') {
          response = await handleHealthAPI(request, env, context)
        } else {
          response = ResponseBuilder.notFound(`API endpoint not found: /${pathSegments.slice(1).join('/')}`)
        }
      } else {
        response = ResponseBuilder.notFound('Endpoint not found')
      }

      // Log completion and add headers
      contextMiddleware.logCompletion(context, response)
      return addRequestIdHeader(response)

    } catch (error) {
      logger.error('Unhandled error in Workers', error)
      
      if (error instanceof WorkersError) {
        const response = ResponseBuilder.error(error.message, error.statusCode, error.code)
        return corsMiddleware.addCORSHeaders(response, request)
      }
      
      const response = ResponseBuilder.internalError('An unexpected error occurred')
      return corsMiddleware.addCORSHeaders(response, request)
    }
  }
}


// Handle entries API
async function handleEntriesAPI(request: Request, env: Env, context: RequestContext): Promise<Response> {
  switch (request.method) {
    case 'GET':
      return await handleGetEntries(request, env, context)
    case 'PUT':
    case 'PATCH':
      return await handleUpdateEntry(request, env, context)
    case 'DELETE':
      return await handleDeleteEntry(request, env, context)
    default:
      return ResponseBuilder.methodNotAllowed(['GET', 'PUT', 'PATCH', 'DELETE'])
  }
}

// Handle content analysis API (supports both text and multimodal analysis)
async function handleAnalyzeAPI(request: Request, env: Env, context: RequestContext): Promise<Response> {
  const logger = new Logger(context.requestId)

  if (request.method !== 'POST') {
    return ResponseBuilder.methodNotAllowed(['POST'])
  }

  try {
    // Validate request size first (larger limit for media uploads)
    RequestValidator.validateRequestSize(request, 50 * 1024 * 1024) // 50MB for media

    // Optional authentication for analyze endpoint
    const authMiddleware = new AuthMiddleware(env)
    const user = await authMiddleware.optionalAuthenticate(request, context)
    if (user) {
      context.user = user
    }

    // Check content type to handle both JSON and multipart requests
    const contentType = request.headers.get('Content-Type') || ''
    let content: string
    let imageData: ImageData | undefined
    let audioData: AudioData | undefined
    let analysisContext: string | undefined

    if (contentType.includes('multipart/form-data')) {
      // Handle multipart form data for media uploads
      const formData = await request.formData()
      content = formData.get('content') as string
      analysisContext = formData.get('context') as string | undefined

      // Handle image file
      const imageFile = formData.get('image') as File | null
      if (imageFile) {
        const arrayBuffer = await imageFile.arrayBuffer()
        const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)))
        imageData = {
          data: base64Data,
          mimeType: imageFile.type
        }
      }

      // Handle audio file
      const audioFile = formData.get('audio') as File | null
      if (audioFile) {
        const arrayBuffer = await audioFile.arrayBuffer()
        const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)))
        audioData = {
          data: base64Data,
          mimeType: audioFile.type
        }
      }
    } else {
      // Handle JSON requests (backward compatibility)
      const body = await RequestValidator.validateJSON(request)
      content = body.content
      analysisContext = body.context

      // Check for embedded media data in JSON
      if (body.image) {
        imageData = body.image as ImageData
      }
      if (body.audio) {
        audioData = body.audio as AudioData
      }
    }

    // Validate required fields - content is optional for media-only analysis
    if (!content && !imageData && !audioData) {
      return ResponseBuilder.validationError('Either content, image, or audio is required')
    }

    if (content) {
      RequestValidator.validateStringLength(content, 'content', 1, 10000)
    }

    // Validate context if provided
    if (analysisContext) {
      RequestValidator.validateEnum(
        analysisContext,
        ['general', 'notes', 'todos', 'calendar'],
        'context'
      )
    }

    // Validate image data if provided
    if (imageData) {
      if (!imageData.data || !imageData.mimeType) {
        return ResponseBuilder.validationError('Invalid image data format')
      }
      if (!imageData.mimeType.startsWith('image/')) {
        return ResponseBuilder.validationError('Only image files are supported')
      }
    }

    // Validate audio data if provided
    if (audioData) {
      if (!audioData.data || !audioData.mimeType) {
        return ResponseBuilder.validationError('Invalid audio data format')
      }
      if (!audioData.mimeType.startsWith('audio/')) {
        return ResponseBuilder.validationError('Only audio files are supported')
      }
    }

    logger.info('Analyzing content', {
      userId: user?.sub || 'anonymous',
      contentLength: content?.length || 0,
      context: analysisContext,
      hasImage: !!imageData,
      hasAudio: !!audioData,
      imageType: imageData?.mimeType,
      audioType: audioData?.mimeType
    })

    // Initialize Gemini service and analyze content with media support
    const geminiService = new GeminiService(env)
    const analysis = (imageData || audioData)
      ? await geminiService.analyzeContentWithMedia(content || '', imageData, audioData, analysisContext, undefined, context)
      : await geminiService.analyzeContent(content || '', analysisContext, undefined, context)

    logger.info('Content analysis completed', {
      userId: user?.sub || 'anonymous',
      itemCount: analysis.items.length,
      firstItemType: analysis.items[0]?.type,
      hasImage: !!imageData,
      hasAudio: !!audioData
    })

    // If user is authenticated, save the analysis results as an entry
    if (user) {
      try {
        // Initialize database service and ensure user exists
        const databaseService = new DatabaseService(env)
        await databaseService.createOrUpdateUser(user.sub, context)

        // Create analysis session with normalized data
        const session = await databaseService.createAnalysisSession(
          user.sub,
          content || '[Media Content]', // Use placeholder for media-only content
          analysis.items.map(item => ({
            type: item.type,
            title: item.title || item.content.substring(0, 50),
            content: item.content,
            metadata: item.metadata
          })),
          context
        )

        // Create simplified blocks array for response
        const blocks = session.items.map(item => ({
          id: item.entryId,
          type: item.type,
          content: item.content,
          metadata: item.metadata
        }))

        logger.info('Entry created from analysis', {
          userId: user.sub,
          entryId: session.sessionId,
          blockCount: blocks.length
        })

        // Return simplified structure with blocks array
        return ResponseBuilder.success({
          blocks: blocks
        }, 'Content analyzed and entry created successfully', 201)

      } catch (dbError) {
        logger.error('Failed to save analysis as entry', dbError)
        // Still return analysis results in simplified format even if database save fails
        const blocks = analysis.items.map((item, index) => ({
          id: `temp-${Date.now()}-${index}`, // Temporary ID since save failed
          type: item.type,
          content: item.content,
          metadata: item.metadata
        }))

        return ResponseBuilder.success({
          blocks: blocks,
          warning: 'Analysis completed but failed to save as entry'
        }, 'Content analyzed successfully (save failed)')
      }
    } else {
      // For unauthenticated requests, return simplified blocks format
      const blocks = analysis.items.map((item, index) => ({
        id: `temp-${Date.now()}-${index}`, // Temporary ID for unauthenticated requests
        type: item.type,
        content: item.content,
        metadata: item.metadata
      }))

      return ResponseBuilder.success({
        blocks: blocks
      }, 'Content analyzed successfully')
    }

  } catch (error) {
    logger.error('Content analysis failed', error)

    if (error instanceof ValidationError) {
      return ResponseBuilder.validationError(error.message)
    }

    if (error instanceof AuthenticationError) {
      return ResponseBuilder.authError(error.message)
    }

    throw error
  }
}

// Handle health check API
async function handleHealthAPI(request: Request, env: Env, context: RequestContext): Promise<Response> {
  const logger = new Logger(context.requestId)
  
  if (request.method !== 'GET') {
    return ResponseBuilder.methodNotAllowed(['GET'])
  }

  try {
    logger.info('Health check requested')

    // Check Gemini service health
    const geminiService = new GeminiService(env)
    const geminiHealthy = await geminiService.healthCheck(context)

    // Check database health
    const databaseService = new DatabaseService(env)
    const dbHealthy = await databaseService.healthCheck(context)

    // Check R2 storage health
    let storageHealthy = false
    try {
      // Just check if the binding exists
      if (env.UPLOADS) {
        storageHealthy = true
      }
    } catch (error) {
      logger.warn('Storage health check failed', error)
    }

    const health = {
      status: geminiHealthy && dbHealthy && storageHealthy ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      services: {
        gemini: geminiHealthy ? 'healthy' : 'unhealthy',
        database: dbHealthy ? 'healthy' : 'unhealthy',
        storage: storageHealthy ? 'healthy' : 'unhealthy',
        auth: 'healthy' // Clerk auth is external
      },
      version: '1.0.0',
      environment: env.NODE_ENV || 'unknown'
    }

    logger.info('Health check completed', { 
      status: health.status,
      services: health.services 
    })

    return ResponseBuilder.success(health, 'Health check completed')

  } catch (error) {
    logger.error('Health check failed', error)
    
    const health = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error',
      version: '1.0.0',
      environment: env.NODE_ENV || 'unknown'
    }

    return ResponseBuilder.success(health, 'Health check completed with errors', 503)
  }
}

// Handle get entries (using existing schema)
async function handleGetEntries(request: Request, env: Env, context: RequestContext): Promise<Response> {
  const logger = new Logger(context.requestId)

  try {
    // Authenticate user
    const authMiddleware = new AuthMiddleware(env)
    const user = await authMiddleware.authenticate(request, context)
    context.user = user

    logger.info('Fetching entries', { userId: user.sub })

    // Initialize database service and ensure user exists
    const databaseService = new DatabaseService(env)
    await databaseService.createOrUpdateUser(user.sub, context)

    // Fetch individual user entries from database
    const individualEntries = await databaseService.getUserEntries(user.sub, 50, 0, context)

    // Return individual blocks directly (no session grouping)
    const entries = individualEntries.map(entry => ({
      id: entry.entryId,
      type: entry.type,
      title: entry.title,
      content: entry.content,
      metadata: entry.metadata,
      createdAt: entry.createdAt,
      updatedAt: entry.updatedAt
    }))

    // Sort by creation date (newest first)
    entries.sort((a, b) => b.createdAt - a.createdAt)

    logger.info('Entries fetched successfully', {
      userId: user.sub,
      count: entries.length
    })

    return ResponseBuilder.success(entries)

  } catch (error) {
    logger.error('Failed to fetch entries', error)

    if (error instanceof AuthenticationError) {
      return ResponseBuilder.authError(error.message)
    }

    throw error
  }
}

// Handle update entry
async function handleUpdateEntry(request: Request, env: Env, context: RequestContext): Promise<Response> {
  const logger = new Logger(context.requestId)

  try {
    // Validate request size first
    RequestValidator.validateRequestSize(request)

    // Authenticate user
    const authMiddleware = new AuthMiddleware(env)
    const user = await authMiddleware.authenticate(request, context)
    context.user = user

    // Get entry ID from URL
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/').filter(Boolean)
    const entryId = pathSegments[2] // /api/entries/{id}

    if (!entryId) {
      return ResponseBuilder.validationError('Entry ID is required')
    }

    // Validate request body
    const body = await RequestValidator.validateJSON(request)

    // Validate update fields
    const allowedFields = ['title', 'content', 'metadata']
    const updates: any = {}

    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updates[field] = body[field]
      }
    }

    if (Object.keys(updates).length === 0) {
      return ResponseBuilder.validationError('At least one field must be provided for update')
    }

    // Validate field types and constraints
    if (updates.title !== undefined) {
      RequestValidator.validateStringLength(updates.title, 'title', 1, 200)
    }

    if (updates.content !== undefined) {
      RequestValidator.validateStringLength(updates.content, 'content', 1, 10000)
    }

    if (updates.metadata !== undefined) {
      if (typeof updates.metadata !== 'object' || updates.metadata === null) {
        return ResponseBuilder.validationError('Metadata must be an object')
      }
    }

    logger.info('Updating entry', {
      userId: user.sub,
      entryId,
      updateFields: Object.keys(updates)
    })

    // Initialize database service
    const databaseService = new DatabaseService(env)

    // For the update, we need to extract sessionId from entryId
    // The entryId format is typically sessionId-itemIndex, but we need to handle the case
    // where entryId might be the sessionId itself
    let sessionId = entryId
    let itemEntryId = entryId

    // Try to find the entry first to get the correct sessionId
    const entries = await databaseService.getUserEntries(user.sub, 1000, 0, context)
    let targetEntry = null

    for (const entry of entries) {
      if (entry.entryId === entryId) {
        targetEntry = entry
        sessionId = entry.sessionId
        itemEntryId = entry.entryId
        break
      }
    }

    if (!targetEntry) {
      return ResponseBuilder.notFound('Entry not found')
    }

    // Update the entry
    const updatedEntry = await databaseService.updateSessionEntry(
      user.sub,
      sessionId,
      itemEntryId,
      updates,
      context
    )

    if (!updatedEntry) {
      return ResponseBuilder.notFound('Entry not found or update failed')
    }

    // Convert to legacy format for backward compatibility
    const responseEntry = {
      id: updatedEntry.entryId,
      sessionId: updatedEntry.sessionId,
      type: updatedEntry.type,
      title: updatedEntry.title,
      content: updatedEntry.content,
      metadata: updatedEntry.metadata,
      createdAt: updatedEntry.createdAt,
      updatedAt: updatedEntry.updatedAt
    }

    logger.info('Entry updated successfully', {
      userId: user.sub,
      entryId: updatedEntry.entryId
    })

    return ResponseBuilder.success(responseEntry, 'Entry updated successfully')

  } catch (error) {
    logger.error('Entry update failed', error)

    if (error instanceof ValidationError) {
      return ResponseBuilder.validationError(error.message)
    }

    if (error instanceof AuthenticationError) {
      return ResponseBuilder.authError(error.message)
    }

    throw error
  }
}

// Handle delete entry (using existing schema)
async function handleDeleteEntry(request: Request, env: Env, context: RequestContext): Promise<Response> {
  const logger = new Logger(context.requestId)

  try {
    // Authenticate user
    const authMiddleware = new AuthMiddleware(env)
    const user = await authMiddleware.authenticate(request, context)
    context.user = user

    // Get entry ID from URL
    const url = new URL(request.url)
    const pathSegments = url.pathname.split('/').filter(Boolean)
    const entryId = pathSegments[2] // /api/entries/{id}

    if (!entryId) {
      return ResponseBuilder.validationError('Entry ID is required')
    }

    logger.info('Deleting entry', {
      userId: user.sub,
      entryId
    })

    // Initialize database service and delete individual entry
    const databaseService = new DatabaseService(env)
    const deleted = await databaseService.deleteUserEntry(user.sub, entryId, context)

    if (!deleted) {
      return ResponseBuilder.notFound('Entry not found')
    }

    logger.info('Entry deleted successfully', {
      userId: user.sub,
      entryId
    })

    return ResponseBuilder.success({ id: entryId }, 'Entry deleted successfully')

  } catch (error) {
    logger.error('Entry deletion failed', error)

    if (error instanceof AuthenticationError) {
      return ResponseBuilder.authError(error.message)
    }

    throw error
  }
}

// User profile endpoint
async function handleUserProfileAPI(request: Request, env: Env, context: RequestContext): Promise<Response> {
  const logger = new Logger(context.requestId)

  try {
    // Only support GET method
    if (request.method !== 'GET') {
      return ResponseBuilder.error('Method not allowed', 405, 'METHOD_NOT_ALLOWED')
    }

    // Authenticate user
    const authMiddleware = new AuthMiddleware(env)
    const user = await authMiddleware.authenticate(request, context)
    context.user = user

    logger.info('Fetching user profile', { userId: user.sub })

    // Initialize database service and get user profile
    const databaseService = new DatabaseService(env)
    const userProfile = await databaseService.getUser(user.sub, context)

    if (!userProfile) {
      // Create user if not exists
      const newUser = await databaseService.createOrUpdateUser(user.sub, context)
      return ResponseBuilder.success(newUser, 'User profile retrieved')
    }

    return ResponseBuilder.success(userProfile, 'User profile retrieved')
  } catch (error) {
    if (error instanceof AuthenticationError) {
      logger.warn('Authentication failed for user profile request', error)
      return ResponseBuilder.error('Authentication required', 401, 'AUTHENTICATION_REQUIRED')
    }

    logger.error('Failed to get user profile', error)
    return ResponseBuilder.error('Failed to get user profile', 500, 'USER_PROFILE_ERROR')
  }
}


