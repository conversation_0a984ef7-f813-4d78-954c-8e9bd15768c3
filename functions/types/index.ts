// ===========================================
// CLOUDFLARE WORKERS TYPE DEFINITIONS (SIMPLIFIED)
// ===========================================

// Environment bindings interface for Cloudflare Workers
export interface Env {
  // Secrets (set via wrangler secret put)
  CLERK_SECRET_KEY: string
  GEMINI_API_KEY: string

  // Environment variables
  CLERK_PUBLISHABLE_KEY: string
  NODE_ENV: string

  // Bindings
  DB: any // D1Database binding
  UPLOADS: any // R2Bucket binding
}

// Entry interface (matches existing database schema)
export interface Entry {
  id: string
  userId: string
  createdAt: number
  updatedAt: number
  rawText?: string
  blocks: Block[]
}

// Block interface (from existing implementation)
export interface Block {
  id: string
  type: 'text' | 'todo' | 'calendar' | 'image'
  content: string
  metadata?: Record<string, any>
}

// AI Analysis result interface
export interface AIAnalysisResult {
  type: 'text' | 'todo' | 'calendar'
  title: string
  content: string
  metadata: Record<string, any>
}

// Import the official Clerk JWT payload type
import type { verifyToken } from '@clerk/backend'

// Type for the verified JWT payload from Clerk
export type ClerkJWTPayload = Awaited<ReturnType<typeof verifyToken>>

// API Response interfaces
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// Request context interface
export interface RequestContext {
  user?: ClerkJWTPayload
  requestId: string
  startTime: number
}

// Request interfaces
export interface CreateEntryRequest {
  rawText: string
}

export interface UpdateEntryRequest {
  blocks: Block[]
}

// Multimodal analysis interfaces
export interface ImageData {
  data: string // base64 encoded image
  mimeType: string // image/jpeg, image/png, etc.
}

export interface AudioData {
  data: string // base64 encoded audio
  mimeType: string // audio/wav, audio/mp3, audio/aac, etc.
}

export interface AnalyzeContentRequest {
  content: string
  context?: 'general' | 'notes' | 'todos' | 'calendar'
  image?: ImageData // Optional image for multimodal analysis
  audio?: AudioData // Optional audio for multimodal analysis
}

// Proxy service interfaces
export interface ProxyTextRequest {
  model: string
  prompt: string
  generationConfig: any
  safetySettings: any[]
  type: 'text'
}

export interface ProxyMultimodalRequest {
  model: string
  prompt: string
  image: ImageData
  generationConfig: any
  safetySettings: any[]
  type: 'multimodal'
}

// Error types
export class WorkersError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'WorkersError'
  }
}

export class AuthenticationError extends WorkersError {
  constructor(message: string = 'Authentication required') {
    super(message, 401, 'AUTHENTICATION_ERROR')
  }
}

export class ValidationError extends WorkersError {
  constructor(message: string = 'Validation failed') {
    super(message, 400, 'VALIDATION_ERROR')
  }
}
