// ===========================================
// GOOGLE GEMINI AI SERVICE (SIMPLIFIED)
// ===========================================

import { GeminiConfig, promptTemplates } from '../config/gemini'
import { ValidationError } from '../types'
import type {
  Env,
  RequestContext,
  ImageData,
  AnalyzeContentRequest
} from '../types'

// Audio data interface for multimodal analysis
export interface AudioData {
  data: string // base64 encoded audio
  mimeType: string // audio/wav, audio/mp3, audio/aac, etc.
}

// AI Analysis result interface - now supports multiple items
export interface AIAnalysisItem {
  type: 'text' | 'todo' | 'calendar'
  title: string
  content: string
  metadata: Record<string, any>
}

export interface AIAnalysisResult {
  items: AIAnalysisItem[]
}

export class GeminiService {
  private config: GeminiConfig
  private env: Env

  constructor(env: Env) {
    this.env = env
    this.config = new GeminiConfig(env)
  }

  // Make request to Gemini proxy service
  private async makeRequest(prompt: string, imageData?: ImageData, audioData?: AudioData, model?: string, context?: RequestContext): Promise<any> {
    if (!this.config.isConfigured()) {
      throw new ValidationError('Gemini AI is not properly configured')
    }

    const endpoint = this.config.getEndpoint(model)
    const headers = this.config.getHeaders()

    // Create appropriate payload based on media inputs
    const payload = (imageData || audioData)
      ? this.config.createMultimodalPayload(prompt, imageData, audioData)
      : this.config.createTextPayload(prompt)

    // Add request ID for tracing
    if (context?.requestId) {
      headers['X-Request-ID'] = context.requestId
    }

    // Log request in development
    if (this.env.NODE_ENV === 'development') {
      console.log('🤖 Gemini Proxy Request:', {
        requestId: context?.requestId,
        endpoint,
        model: model || this.config.getDefaultModel(),
        headers: { ...headers, 'Authorization': 'Bearer ***' },
        payloadType: (imageData || audioData) ? 'multimodal' : 'text',
        hasImage: !!imageData,
        hasAudio: !!audioData,
        payloadSize: JSON.stringify(payload).length
      })
    }

    try {
      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Gemini API Error Response:', {
          requestId: context?.requestId,
          status: response.status,
          statusText: response.statusText,
          body: errorText
        })
        throw new ValidationError(`Gemini API error: ${response.status} ${response.statusText}`)
      }

      const data: any = await response.json()

      // Log response in development
      if (this.env.NODE_ENV === 'development') {
        console.log('🤖 Gemini API Response:', {
          requestId: context?.requestId,
          candidates: data.candidates?.length || 0,
          hasContent: !!data.candidates?.[0]?.content
        })
      }

      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        console.error('Invalid Gemini API response structure:', {
          requestId: context?.requestId,
          error: 'Missing candidates or content'
        })
        throw new ValidationError('Invalid response from Gemini API')
      }

      const content = data.candidates[0].content

      // Handle truncated responses (MAX_TOKENS issue)
      if (!content.parts || content.parts.length === 0) {
        console.error('Invalid Gemini API response parts structure:', {
          requestId: context?.requestId,
          finishReason: data.candidates[0].finishReason,
          hasImageData: !!imageData,
          hasAudioData: !!audioData
        })

        // Check if response was truncated due to token limits
        if (data.candidates[0].finishReason === 'MAX_TOKENS') {
          throw new ValidationError('Response truncated due to token limit - please try with shorter content')
        }

        throw new ValidationError('Invalid response parts from Gemini API')
      }

      // Find the text part in multimodal responses
      let textResponse = ''
      const parts = data.candidates[0].content.parts

      for (const part of parts) {
        if (part.text) {
          textResponse = part.text
          break
        }
      }

      if (!textResponse) {
        console.error('No text part found in Gemini API response:', {
          requestId: context?.requestId,
          partsCount: parts.length
        })
        throw new ValidationError('No text content in Gemini API response')
      }

      if (this.env.NODE_ENV === 'development') {
        console.log('🤖 Gemini Raw Response:', {
          requestId: context?.requestId,
          responseLength: textResponse.length,
          preview: textResponse.substring(0, 200) + '...'
        })
      }

      try {
        // Clean the response in case it has markdown formatting
        let cleanedResponse = textResponse.trim()

        // Remove markdown code blocks if present
        if (cleanedResponse.startsWith('```json')) {
          cleanedResponse = cleanedResponse.replace(/^```json\s*/, '').replace(/\s*```$/, '')
        } else if (cleanedResponse.startsWith('```')) {
          cleanedResponse = cleanedResponse.replace(/^```\s*/, '').replace(/\s*```$/, '')
        }

        return JSON.parse(cleanedResponse)
      } catch (parseError) {
        console.error('Failed to parse Gemini response as JSON:', {
          requestId: context?.requestId,
          originalResponse: textResponse,
          parseError: parseError instanceof Error ? parseError.message : String(parseError)
        })
        throw new ValidationError('Invalid JSON response from Gemini API')
      }
    } catch (error) {
      console.error('Gemini API request failed:', {
        requestId: context?.requestId,
        error: error instanceof Error ? error.message : String(error)
      })

      if (error instanceof ValidationError) {
        throw error
      }

      throw new ValidationError(`Gemini API request failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  // Analyze content with AI (text only)
  async analyzeContent(content: string, context?: string, model?: string, requestContext?: RequestContext): Promise<AIAnalysisResult> {
    return this.analyzeContentWithMedia(content, undefined, undefined, context, model, requestContext)
  }

  // Analyze content with AI (text + optional image) - backward compatibility
  async analyzeContentWithImage(content: string, imageData?: ImageData, context?: string, model?: string, requestContext?: RequestContext): Promise<AIAnalysisResult> {
    return this.analyzeContentWithMedia(content, imageData, undefined, context, model, requestContext)
  }

  // Analyze content with AI (text + optional image + optional audio)
  async analyzeContentWithMedia(content: string, imageData?: ImageData, audioData?: AudioData, context?: string, model?: string, requestContext?: RequestContext): Promise<AIAnalysisResult> {
    if (!this.config.isConfigured()) {
      throw new ValidationError('AI processing is not enabled or configured')
    }

    try {
      let prompt: string

      // Determine analysis type based on available media inputs
      if (imageData || audioData) {
        // Use multimodal analysis for any media content
        prompt = this.config.createPromptWithDateContext(
          promptTemplates.multimodalAnalysis,
          content
        )
      } else if (context && context !== 'general') {
        // Use contextual analysis for specific contexts
        prompt = this.config.createPromptWithDateContext(
          promptTemplates.contextualAnalysis,
          content,
          context
        )
      } else {
        // Use text-only analysis for pure text content
        prompt = this.config.createPromptWithDateContext(
          promptTemplates.textOnlyAnalysis,
          content
        )
      }

      const result = await this.makeRequest(prompt, imageData, audioData, model, requestContext)

      // Standardized response handling - all recognition types use the same processing pipeline
      // Validate the result structure
      if (!result || typeof result !== 'object') {
        throw new ValidationError('Invalid analysis result structure')
      }

      // All templates now return JSON arrays, so we can use unified processing
      if (!Array.isArray(result)) {
        throw new ValidationError('Expected JSON array response from AI')
      }

      // Process each item in the array
      const processedItems = result.map((item: any) => ({
        type: item.type || 'text',
        title: item.title || item.content?.substring(0, 50) + (item.content?.length > 50 ? '...' : '') || 'Untitled',
        content: item.content || '',
        metadata: item.metadata || {}
      }))

      const analysisResult: AIAnalysisResult = {
        items: processedItems
      }

      if (this.env.NODE_ENV === 'development') {
        console.log('🤖 Unified Analysis Result:', {
          requestId: requestContext?.requestId,
          itemCount: analysisResult.items.length,
          hasImageContent: !!imageData,
          hasAudioContent: !!audioData,
          firstItemType: analysisResult.items[0]?.type,
          itemTypes: analysisResult.items.map(item => item.type)
        })
      }

      return analysisResult
    } catch (error) {
      console.error('Content analysis failed:', {
        requestId: requestContext?.requestId,
        error: error instanceof Error ? error.message : String(error),
        hasImage: !!imageData,
        hasAudio: !!audioData,
        contentLength: content?.length || 0
      })

      // Return more descriptive error message
      const errorMessage = error instanceof ValidationError
        ? error.message
        : `识别失败: ${error instanceof Error ? error.message : '未知错误'}`

      throw new ValidationError(errorMessage)
    }
  }

  // Convert AI result to Block format - returns array of blocks
  createBlocksFromAnalysis(analysis: AIAnalysisResult) {
    if (!analysis.items || analysis.items.length === 0) {
      throw new ValidationError('No analysis items found')
    }

    return analysis.items.map((item, index) => ({
      id: `${Date.now()}-${index + 1}`,
      type: item.type,
      content: item.content,
      metadata: item.metadata
    }))
  }

  // Legacy method for backward compatibility - returns first block only
  createBlockFromAnalysis(analysis: AIAnalysisResult) {
    const blocks = this.createBlocksFromAnalysis(analysis)
    return blocks[0]
  }

  // Health check method
  async healthCheck(requestContext?: RequestContext): Promise<boolean> {
    if (!this.config.isConfigured()) {
      return false
    }

    try {
      const result = await this.analyzeContent('Test message for health check', undefined, undefined, requestContext)

      // Validate that we get the expected array format
      if (!result.items || !Array.isArray(result.items) || result.items.length === 0) {
        console.error('Health check failed: Invalid response format', {
          requestId: requestContext?.requestId,
          result
        })
        return false
      }

      return true
    } catch (error) {
      console.error('Gemini health check failed:', {
        requestId: requestContext?.requestId,
        error: error instanceof Error ? error.message : String(error)
      })
      return false
    }
  }
}
