import type { Env, RequestContext } from '../types'

// User profile interface
export interface User {
  userId: string
  subscriptionStatus: 'free' | 'premium' | 'enterprise'
  subscriptionTier: string
  subscriptionExpiresAt?: number
  totalEntries: number
  totalItems: number
  createdAt: number
  updatedAt: number
  lastActiveAt?: number
  preferences?: Record<string, any>
}

// Individual data entry interface
export interface UserDataEntry {
  entryId: string
  userId: string
  sessionId: string
  type: 'todo' | 'calendar' | 'text'
  title: string
  content: string
  metadata?: Record<string, any>
  rawText?: string
  createdAt: number
  updatedAt: number
}

// Analysis session interface (groups related entries)
export interface AnalysisSession {
  sessionId: string
  userId: string
  rawText: string
  items: UserDataEntry[]
  createdAt: number
  updatedAt: number
}

export class DatabaseService {
  private env: Env

  constructor(env: Env) {
    this.env = env
  }

  // Health check
  async healthCheck(context?: RequestContext): Promise<boolean> {
    try {
      await this.env.DB.prepare('SELECT 1').first()
      return true
    } catch (error) {
      console.error('Database health check failed:', {
        requestId: context?.requestId,
        error: error instanceof Error ? error.message : String(error)
      })
      return false
    }
  }

  // User management methods
  async createOrUpdateUser(userId: string, context?: RequestContext): Promise<User> {
    const now = Date.now()
    
    try {
      // Try to get existing user
      const existingUser = await this.env.DB.prepare(
        'SELECT * FROM users WHERE userId = ?'
      ).bind(userId).first() as User | null

      if (existingUser) {
        // Update last active time
        await this.env.DB.prepare(
          'UPDATE users SET lastActiveAt = ?, updatedAt = ? WHERE userId = ?'
        ).bind(now, now, userId).run()

        return {
          ...existingUser,
          lastActiveAt: now,
          updatedAt: now
        }
      } else {
        // Create new user
        const newUser: User = {
          userId,
          subscriptionStatus: 'free',
          subscriptionTier: 'basic',
          totalEntries: 0,
          totalItems: 0,
          createdAt: now,
          updatedAt: now,
          lastActiveAt: now
        }

        await this.env.DB.prepare(`
          INSERT INTO users (userId, subscriptionStatus, subscriptionTier, totalEntries, totalItems, createdAt, updatedAt, lastActiveAt)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          newUser.userId,
          newUser.subscriptionStatus,
          newUser.subscriptionTier,
          newUser.totalEntries,
          newUser.totalItems,
          newUser.createdAt,
          newUser.updatedAt,
          newUser.lastActiveAt
        ).run()

        return newUser
      }
    } catch (error) {
      console.error('Failed to create or update user:', {
        requestId: context?.requestId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  async getUser(userId: string, context?: RequestContext): Promise<User | null> {
    try {
      const user = await this.env.DB.prepare(
        'SELECT * FROM users WHERE userId = ?'
      ).bind(userId).first() as User | null

      return user
    } catch (error) {
      console.error('Failed to get user:', {
        requestId: context?.requestId,
        userId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  // Data entry methods
  async createAnalysisSession(
    userId: string, 
    rawText: string, 
    items: Array<{type: string, title: string, content: string, metadata?: Record<string, any>}>,
    context?: RequestContext
  ): Promise<AnalysisSession> {
    const sessionId = crypto.randomUUID()
    const now = Date.now()

    try {
      // Start transaction-like operations
      const entries: UserDataEntry[] = []

      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        const entryId = `${sessionId}-${i + 1}`
        
        const entry: UserDataEntry = {
          entryId,
          userId,
          sessionId,
          type: item.type as 'todo' | 'calendar' | 'text',
          title: item.title,
          content: item.content,
          metadata: item.metadata,
          rawText,
          createdAt: now,
          updatedAt: now
        }

        // Insert individual entry
        await this.env.DB.prepare(`
          INSERT INTO user_data (entryId, userId, sessionId, type, title, content, metadata, rawText, createdAt, updatedAt)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `).bind(
          entry.entryId,
          entry.userId,
          entry.sessionId,
          entry.type,
          entry.title,
          entry.content,
          entry.metadata ? JSON.stringify(entry.metadata) : null,
          entry.rawText,
          entry.createdAt,
          entry.updatedAt
        ).run()

        entries.push(entry)
      }

      // Update user statistics
      await this.env.DB.prepare(`
        UPDATE users 
        SET totalEntries = totalEntries + 1, 
            totalItems = totalItems + ?, 
            updatedAt = ?, 
            lastActiveAt = ?
        WHERE userId = ?
      `).bind(items.length, now, now, userId).run()

      return {
        sessionId,
        userId,
        rawText,
        items: entries,
        createdAt: now,
        updatedAt: now
      }
    } catch (error) {
      console.error('Failed to create analysis session:', {
        requestId: context?.requestId,
        userId,
        sessionId,
        itemCount: items.length,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  async getUserEntries(userId: string, limit: number = 50, offset: number = 0, context?: RequestContext): Promise<UserDataEntry[]> {
    try {
      // Get individual entries directly from user_data table without aggregation
      const entries = await this.env.DB.prepare(`
        SELECT * FROM user_data
        WHERE userId = ?
        ORDER BY createdAt DESC
        LIMIT ? OFFSET ?
      `).bind(userId, limit, offset).all()

      // Parse metadata for each entry and return as individual records
      const result: UserDataEntry[] = entries.results.map((entry: any) => ({
        ...entry,
        metadata: entry.metadata ? JSON.parse(entry.metadata) : undefined
      }))

      return result
    } catch (error) {
      console.error('Failed to get user entries:', {
        requestId: context?.requestId,
        userId,
        limit,
        offset,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  async deleteSession(userId: string, sessionId: string, context?: RequestContext): Promise<boolean> {
    try {
      // Get item count for this session
      const itemCount = await this.env.DB.prepare(
        'SELECT COUNT(*) as count FROM user_data WHERE userId = ? AND sessionId = ?'
      ).bind(userId, sessionId).first() as { count: number }

      if (!itemCount || itemCount.count === 0) {
        return false // Session not found
      }

      // Delete all items in the session
      await this.env.DB.prepare(
        'DELETE FROM user_data WHERE userId = ? AND sessionId = ?'
      ).bind(userId, sessionId).run()

      // Update user statistics
      const now = Date.now()
      await this.env.DB.prepare(`
        UPDATE users 
        SET totalEntries = totalEntries - 1, 
            totalItems = totalItems - ?, 
            updatedAt = ?
        WHERE userId = ?
      `).bind(itemCount.count, now, userId).run()

      return true
    } catch (error) {
      console.error('Failed to delete session:', {
        requestId: context?.requestId,
        userId,
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  async deleteUserEntry(userId: string, entryId: string, context?: RequestContext): Promise<boolean> {
    try {
      // First, verify the entry exists and belongs to the user
      const existingEntry = await this.env.DB.prepare(
        'SELECT * FROM user_data WHERE userId = ? AND entryId = ?'
      ).bind(userId, entryId).first() as UserDataEntry | null

      if (!existingEntry) {
        return false // Entry not found
      }

      // Delete the specific entry
      const result = await this.env.DB.prepare(
        'DELETE FROM user_data WHERE userId = ? AND entryId = ?'
      ).bind(userId, entryId).run()

      if (!result.success || result.changes === 0) {
        return false
      }

      // Update user statistics (decrease totalItems by 1)
      const now = Date.now()
      await this.env.DB.prepare(`
        UPDATE users
        SET totalItems = totalItems - 1,
            updatedAt = ?
        WHERE userId = ?
      `).bind(now, userId).run()

      return true
    } catch (error) {
      console.error('Failed to delete user entry:', {
        requestId: context?.requestId,
        userId,
        entryId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  async updateSessionEntry(
    userId: string,
    sessionId: string,
    entryId: string,
    updates: Partial<Pick<UserDataEntry, 'title' | 'content' | 'metadata'>>,
    context?: RequestContext
  ): Promise<UserDataEntry | null> {
    try {
      const now = Date.now()

      // First, verify the entry exists and belongs to the user
      const existingEntry = await this.env.DB.prepare(
        'SELECT * FROM user_data WHERE userId = ? AND sessionId = ? AND entryId = ?'
      ).bind(userId, sessionId, entryId).first() as UserDataEntry | null

      if (!existingEntry) {
        return null
      }

      // Build update query dynamically based on provided fields
      const updateFields: string[] = []
      const updateValues: any[] = []

      if (updates.title !== undefined) {
        updateFields.push('title = ?')
        updateValues.push(updates.title)
      }

      if (updates.content !== undefined) {
        updateFields.push('content = ?')
        updateValues.push(updates.content)
      }

      if (updates.metadata !== undefined) {
        updateFields.push('metadata = ?')
        updateValues.push(JSON.stringify(updates.metadata))
      }

      if (updateFields.length === 0) {
        // No updates provided, return existing entry
        return existingEntry
      }

      // Add updatedAt timestamp
      updateFields.push('updatedAt = ?')
      updateValues.push(now)

      // Add WHERE clause parameters
      updateValues.push(userId, sessionId, entryId)

      const updateQuery = `
        UPDATE user_data
        SET ${updateFields.join(', ')}
        WHERE userId = ? AND sessionId = ? AND entryId = ?
      `

      const result = await this.env.DB.prepare(updateQuery).bind(...updateValues).run()

      if (!result.success || result.changes === 0) {
        return null
      }

      // Return updated entry
      const updatedEntry = await this.env.DB.prepare(
        'SELECT * FROM user_data WHERE userId = ? AND sessionId = ? AND entryId = ?'
      ).bind(userId, sessionId, entryId).first() as UserDataEntry | null

      if (updatedEntry && updatedEntry.metadata) {
        try {
          updatedEntry.metadata = typeof updatedEntry.metadata === 'string'
            ? JSON.parse(updatedEntry.metadata)
            : updatedEntry.metadata
        } catch {
          updatedEntry.metadata = {}
        }
      }

      return updatedEntry

    } catch (error) {
      console.error('Database update entry failed:', {
        requestId: context?.requestId,
        userId,
        sessionId,
        entryId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }
}
