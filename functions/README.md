# Synapse AI Notes - Cloudflare Workers 后端

## 项目概述

Synapse AI Notes 是一个基于 Cloudflare 边缘计算平台构建的现代化、AI 驱动的笔记和任务管理系统。后端利用 Cloudflare Workers 进行无服务器执行，D1 用于数据库存储，R2 用于文件存储，Clerk 用于身份验证，Google Gemini AI 用于智能内容分析。

## 核心功能

- **AI 驱动的内容分析**：自动将用户输入分类并结构化为待办事项、日历事件和笔记
- **多模态输入支持**：处理文本、图像和音频内容
- **实时处理**：基于边缘的执行，实现低延迟响应
- **安全身份验证**：通过 Clerk 进行基于 JWT 的身份验证
- **可扩展存储**：Cloudflare D1 数据库和 R2 对象存储
- **RESTful API**：清晰、文档完善的 API 端点
- **CORS 支持**：支持跨域资源共享的 Web 应用程序

## 架构概览

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   前端          │    │  Cloudflare      │    │  外部           │
│   应用程序      │◄──►│  Workers         │◄──►│  服务           │
│                 │    │  (边缘运行时)    │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
                    ┌──────────────────┐    ┌─────────────────┐
                    │  Cloudflare D1   │    │  Google Gemini  │
                    │  (数据库)        │    │  AI 服务        │
                    └──────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
                    ┌──────────────────┐    ┌─────────────────┐
                    │  Cloudflare R2   │    │  Clerk Auth     │
                    │  (对象存储)      │    │  服务           │
                    └──────────────────┘    └─────────────────┘
```

## 技术栈

### 核心基础设施
- **运行时**：Cloudflare Workers (V8 Isolates)
- **语言**：TypeScript
- **数据库**：Cloudflare D1 (基于 SQLite)
- **文件存储**：Cloudflare R2 (S3 兼容)
- **CDN/边缘**：Cloudflare 全球网络

### 外部服务
- **身份验证**：Clerk (基于 JWT)
- **AI 处理**：Google Gemini AI
- **开发工具**：Wrangler CLI

### 核心库
- **@clerk/backend**：身份验证中间件
- **@google/generative-ai**：Gemini AI 集成
- **zod**：运行时类型验证

## 项目结构

```
functions/
├── index.ts                 # 主入口点和请求路由
├── config/
│   └── gemini.ts           # Gemini AI 配置
├── middleware/
│   └── auth.ts             # 身份验证和 CORS 中间件
├── services/
│   ├── databaseService.ts  # D1 数据库操作
│   └── geminiService.ts    # AI 内容分析
├── types/
│   └── index.ts            # TypeScript 类型定义
├── utils/
│   └── response.ts         # 响应构建器和验证器
├── schema.sql              # 数据库模式定义
├── package.json            # 依赖项和脚本
├── tsconfig.json           # TypeScript 配置
└── wrangler.toml           # Cloudflare Workers 配置
```

## 核心组件

### 1. 请求路由器 (`index.ts`)
- 处理传入的 HTTP 请求
- 路由到适当的 API 处理器
- 应用中间件（CORS、身份验证）
- 错误处理和日志记录

### 2. 数据库服务 (`services/databaseService.ts`)
- 用户管理和会话处理
- 条目 CRUD 操作
- 数据规范化和验证
- 事务管理

### 3. AI 服务 (`services/geminiService.ts`)
- 内容分析和分类
- 多模态处理（文本、图像、音频）
- 结构化数据提取
- 错误处理和重试

### 4. 身份验证中间件 (`middleware/auth.ts`)
- JWT 令牌验证
- 用户上下文提取
- CORS 头管理
- 请求/响应日志记录

## API 端点

### 核心端点
- `POST /api/analyze` - AI 驱动的内容分析
- `GET /api/entries` - 检索用户条目
- `PUT/PATCH /api/entries/{id}` - 更新特定条目
- `DELETE /api/entries/{id}` - 删除条目
- `GET /api/health` - 系统健康检查

### 数据流
1. **内容输入** → AI 分析 → 结构化数据
2. **身份验证** → 用户上下文 → 数据库操作
3. **响应** → JSON 格式 → 前端消费

## 安全功能

- **JWT 身份验证**：安全的基于令牌的身份验证
- **请求验证**：输入清理和大小限制
- **速率限制**：内置 Cloudflare 保护
- **CORS 配置**：受控的跨域访问
- **错误处理**：安全的错误消息，无数据泄露

## 性能特征

- **冷启动**：~10-20ms (V8 Isolates)
- **响应时间**：~100-500ms (包括 AI 处理)
- **可扩展性**：Cloudflare Workers 自动扩展
- **全球分布**：全球边缘部署
- **并发请求**：无限制（受 Cloudflare 限制约束）

## 开发工作流

1. **本地开发**：使用 `wrangler dev` 进行本地测试
2. **数据库迁移**：通过 D1 进行 SQL 模式更新
3. **部署**：使用 `wrangler deploy` 进行生产部署
4. **监控**：Cloudflare 仪表板和日志
5. **测试**：内置健康检查和验证

## 环境要求

- 开发需要 Node.js 18+
- 启用了 Workers、D1 和 R2 的 Cloudflare 账户
- 用于身份验证的 Clerk 账户
- 用于 Gemini AI 访问的 Google Cloud 账户

## 文档

- [部署指南](./DEPLOYMENT.md) - 完整的部署说明
- [API 文档](./API.md) - 后端 API 参考和使用示例

## 许可证

本项目为专有软件。保留所有权利。
