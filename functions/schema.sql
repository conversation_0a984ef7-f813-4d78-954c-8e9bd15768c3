-- ===========================================
-- SYNAPSE AI NOTES DATABASE SCHEMA (NORMALIZED)
-- ===========================================
-- Execute this in Cloudflare Dashboard > D1 > Your Database > Console

-- Users table for storing user profile information
CREATE TABLE IF NOT EXISTS users (
  userId TEXT PRIMARY KEY,
  subscriptionStatus TEXT DEFAULT 'free',
  subscriptionTier TEXT DEFAULT 'basic',
  subscriptionExpiresAt INTEGER,
  totalEntries INTEGER DEFAULT 0,
  totalItems INTEGER DEFAULT 0,
  createdAt INTEGER NOT NULL,
  updatedAt INTEGER NOT NULL,
  lastActiveAt INTEGER,
  preferences TEXT -- JSON string for user preferences
);

-- User data table for storing individual analyzed items
CREATE TABLE IF NOT EXISTS user_data (
  entryId TEXT PRIMARY KEY,
  userId TEXT NOT NULL,
  sessionId TEXT, -- Groups items from the same analysis session
  type TEXT NOT NULL CHECK (type IN ('todo', 'calendar', 'text')),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  metadata TEXT, -- JSON string for item metadata
  rawText TEXT, -- Original input text for this session
  createdAt INTEGER NOT NULL,
  updatedAt INTEGER NOT NULL,
  FOREIGN KEY (userId) REFERENCES users(userId) ON DELETE CASCADE
);

-- Indexes for users table
CREATE INDEX IF NOT EXISTS idx_users_subscription ON users(subscriptionStatus);
CREATE INDEX IF NOT EXISTS idx_users_createdAt ON users(createdAt DESC);
CREATE INDEX IF NOT EXISTS idx_users_lastActive ON users(lastActiveAt DESC);

-- Indexes for user_data table
CREATE INDEX IF NOT EXISTS idx_user_data_userId ON user_data(userId);
CREATE INDEX IF NOT EXISTS idx_user_data_sessionId ON user_data(sessionId);
CREATE INDEX IF NOT EXISTS idx_user_data_type ON user_data(type);
CREATE INDEX IF NOT EXISTS idx_user_data_createdAt ON user_data(createdAt DESC);
CREATE INDEX IF NOT EXISTS idx_user_data_userId_createdAt ON user_data(userId, createdAt DESC);
CREATE INDEX IF NOT EXISTS idx_user_data_userId_type ON user_data(userId, type);

-- Note: Migration from old 'entries' table should be done manually if needed
-- The old 'entries' table can coexist with the new schema for backward compatibility
