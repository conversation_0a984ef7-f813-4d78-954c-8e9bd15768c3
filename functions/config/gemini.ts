// ===========================================
// GOOGLE GEMINI AI CONFIGURATION (SIMPLIFIED)
// ===========================================

import type { Env } from '../types'

// Gemini AI configuration for Workers with dynamic model support
export class GeminiConfig {
  private env: Env
  private defaultModel: string = 'gemini-2.5-flash-lite-preview-06-17'

  constructor(env: Env) {
    this.env = env
  }

  get apiKey(): string {
    return this.env.GEMINI_API_KEY
  }

  get proxyUrl(): string {
    // Use Google Cloud Function proxy to resolve geographic restrictions
    return 'https://gemini-proxy-24490303142.us-central1.run.app'
  }

  // Get model with dynamic support
  getModel(model?: string): string {
    return model || this.defaultModel
  }

  // Get default model
  getDefaultModel(): string {
    return this.defaultModel
  }

  // Check if Gemini is properly configured
  isConfigured(): boolean {
    return !!(this.env.GEMINI_API_KEY)
  }

  // Get the proxy endpoint URL with dynamic model support
  getEndpoint(model?: string): string {
    const selectedModel = this.getModel(model)
    const baseUrl = this.proxyUrl

    // Add model parameter to the proxy URL
    const url = new URL(baseUrl)
    url.searchParams.set('model', selectedModel)

    return url.toString()
  }

  // Get headers for API requests
  getHeaders(): Record<string, string> {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`
    }
  }

  get generationConfig() {
    return {
      temperature: 0.7,
      topK: 40,
      topP: 0.95,
      maxOutputTokens: 1024,
      responseMimeType: 'application/json'
    }
  }

  get safetySettings() {
    return [
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE'
      },
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE'
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE'
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE'
      }
    ]
  }



  // Create request payload for proxy service (standard Gemini API format)
  createTextPayload(prompt: string) {
    return {
      contents: [
        {
          parts: [
            {
              text: prompt
            }
          ]
        }
      ],
      generationConfig: this.generationConfig,
      safetySettings: this.safetySettings
    }
  }

  // Create request payload for multimodal analysis (text + image + audio)
  createMultimodalPayload(prompt: string, imageData?: {data: string, mimeType: string}, audioData?: {data: string, mimeType: string}) {
    const parts: any[] = [
      {
        text: prompt
      }
    ]

    // Add image data if provided
    if (imageData) {
      parts.push({
        inline_data: {
          mime_type: imageData.mimeType,
          data: imageData.data
        }
      })
    }

    // Add audio data if provided
    if (audioData) {
      parts.push({
        inline_data: {
          mime_type: audioData.mimeType,
          data: audioData.data
        }
      })
    }

    return {
      contents: [
        {
          parts: parts
        }
      ],
      generationConfig: this.generationConfig,
      safetySettings: this.safetySettings
    }
  }

  // Get current date/time context for prompt templates
  getDateTimeContext(): Record<string, string> {
    const now = new Date()

    // Format date as YYYY-MM-DD
    const formatDate = (date: Date): string => {
      const isoString = date.toISOString().split('T')[0]
      return isoString || ''
    }

    // Format time as HH:MM
    const formatTime = (date: Date): string => {
      const timeString = date.toTimeString().split(' ')[0]
      return timeString ? timeString.substring(0, 5) : ''
    }

    return {
      currentDate: formatDate(now),
      currentTime: formatTime(now)
    }
  }

  // Create enhanced prompt with date context
  createPromptWithDateContext(template: string, content: string, context?: string): string {
    const dateContext = this.getDateTimeContext()

    let prompt = template
      .replace('{content}', content)
      .replace('{currentDate}', dateContext['currentDate'] || '')
      .replace('{currentTime}', dateContext['currentTime'] || '')

    if (context) {
      prompt = prompt.replace('{context}', context)
    }

    return prompt
  }
}

// Unified content analysis prompt templates for aggregated information analysis
export const promptTemplates = {
  // Template for text-only content analysis
  textOnlyAnalysis: `
AGGREGATED INFORMATION ANALYSIS - Extract, organize, and categorize information into structured items.

Current: {currentDate} {currentTime}
Content: "{content}"

TASK: Analyze content and identify ALL distinct information items. Return JSON array (preserve input language).

[{"type":"text|todo|calendar","title":"max 50 chars","content":"cleaned content","metadata":{"priority":"low|medium|high","dueDate":"YYYY-MM-DD","time":"HH:MM","tags":["relevant","tags"],"category":"work|personal|health|finance|other"}}]

CLASSIFICATION:
- todo: tasks, reminders, action items, things to do
- calendar: meetings, appointments, events, scheduled activities
- text: notes, information, thoughts, general content

DATE PROCESSING: Calculate from {currentDate} - today=current, tomorrow=+1 day, "Monday"=next occurrence, time-only=today's date.

STRUCTURE: Multiple items=separate objects, single item=one-object array.
`,

  // Template for multimodal content analysis (text + image + audio)
  multimodalAnalysis: `
MULTIMODAL AGGREGATED ANALYSIS - Extract and merge content from all sources, preserve original languages.

Current: {currentDate} {currentTime}
User text: "{content}"

PROCESSING STEPS:
1) AUDIO: Transcribe speech to text (preserve original language)
2) IMAGE: OCR extract text (preserve original language)
3) MERGE: Combine ALL extracted content + user text
4) ANALYZE: Create separate items for each distinct piece of information

CRITICAL CONTENT PRIORITY:
- AUDIO transcription has HIGHEST priority - include ALL audio content
- IMAGE text has MEDIUM priority - include ALL image content
- USER text has LOWEST priority - only if meaningful (ignore "screenshot", "analyze")

LANGUAGE PRESERVATION:
- Keep audio transcription in original language (English audio → English output)
- Keep image text in original language (Chinese image → Chinese output)
- Keep user text in original language
- DO NOT translate between languages

MERGE STRATEGY:
- Audio content: Create separate items for each audio task/event/note
- Image content: Create separate items for each image task/event/note
- User content: Create separate items if meaningful
- NEVER combine different language content into single item

[{"type":"text|todo|calendar","title":"max 50 chars","content":"preserve original language","metadata":{"priority":"low|medium|high","dueDate":"YYYY-MM-DD","time":"HH:MM","tags":["relevant"],"category":"work|personal|health|finance|other"}}]

CLASSIFICATION: todo=tasks/reminders, calendar=meetings/appointments, text=notes/info
DATE PROCESSING: Calculate from {currentDate} - today=current, tomorrow=+1 day, relative dates
STRUCTURE: Multiple sources = multiple separate objects, preserve all content sources
`,

  // Template for contextual analysis (when user specifies context)
  contextualAnalysis: `
AGGREGATED INFORMATION ANALYSIS - Analyze content with context guidance, organize into structured items.

Current: {currentDate} {currentTime}
Content: "{content}"
Context hint: {context}

TASK: Analyze actual content (context is guidance only). Return JSON array (preserve input language).

[{"type":"text|todo|calendar","title":"max 50 chars","content":"cleaned content","metadata":{"priority":"low|medium|high","dueDate":"YYYY-MM-DD","time":"HH:MM","tags":["relevant","tags"],"category":"work|personal|health|finance|other"}}]

CLASSIFICATION:
- todo: tasks, reminders, action items, things to do
- calendar: meetings, appointments, events, scheduled activities
- text: notes, information, thoughts, general content

DATE PROCESSING: Calculate from {currentDate} - today=current, tomorrow=+1 day, "Monday"=next occurrence, time-only=today's date.

STRUCTURE: Multiple items=separate objects, single item=one-object array.
`
}





