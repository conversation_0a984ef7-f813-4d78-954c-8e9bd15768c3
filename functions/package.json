{"name": "synapse-workers", "version": "1.0.0", "description": "Cloudflare Workers backend for Synapse AI Notes (Simplified)", "main": "index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "deploy:staging": "wrangler deploy --env preview", "deploy:production": "wrangler deploy --env production", "db:migrate": "wrangler d1 execute synapse-dev --file=./schema.sql", "db:migrate:staging": "wrangler d1 execute synapse-db-preview --file=./schema.sql --env preview", "db:migrate:production": "wrangler d1 execute synapse-db-prod --file=./schema.sql --env production", "secrets:put": "wrangler secret put CLERK_SECRET_KEY && wrangler secret put GEMINI_API_KEY", "secrets:put:staging": "wrangler secret put CLERK_SECRET_KEY --env preview && wrangler secret put GEMINI_API_KEY --env preview", "secrets:put:production": "wrangler secret put CLERK_SECRET_KEY --env production && wrangler secret put GEMINI_API_KEY --env production", "type-check": "tsc --noEmit"}, "dependencies": {"@cloudflare/workers-types": "^4.20241218.0", "@clerk/backend": "^2.5.0"}, "devDependencies": {"typescript": "^5.3.3", "wrangler": "^3.78.12"}, "engines": {"node": ">=18.0.0"}, "keywords": ["cloudflare", "workers", "ai", "notes", "typescript", "gemini", "clerk"], "author": "Synapse AI Notes", "license": "MIT"}