// ===========================================
// AUTHENTICATION MIDDLEWARE (Workers)
// ===========================================

import { verifyToken } from '@clerk/backend'
import { AuthenticationError } from '../types'
import { Logger } from '../utils/response'
import type { Env, RequestContext } from '../types'

// Type for the verified JWT payload from Clerk
type VerifiedJWTPayload = Awaited<ReturnType<typeof verifyToken>>

export class AuthMiddleware {
  private env: Env

  constructor(env: Env) {
    this.env = env
  }

  // Extract JWT token from request
  private extractToken(request: Request): string | null {
    // Check Authorization header
    const authHeader = request.headers.get('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7)
    }

    // Check cookie (for browser requests)
    const cookieHeader = request.headers.get('Cookie')
    if (cookieHeader) {
      const cookies = this.parseCookies(cookieHeader)
      // Try multiple possible cookie names used by Clerk
      return cookies['__session'] ||
             cookies['__clerk_session'] ||
             cookies['clerk-session'] ||
             cookies['session'] ||
             null
    }

    return null
  }

  // Parse cookies from header
  private parseCookies(cookieHeader: string): Record<string, string> {
    const cookies: Record<string, string> = {}
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=')
      if (name && value) {
        cookies[name] = decodeURIComponent(value)
      }
    })
    return cookies
  }

  // Verify authentication and extract user info using official Clerk SDK
  async authenticate(request: Request, context: RequestContext): Promise<VerifiedJWTPayload> {
    // Check if Clerk is configured
    if (!this.env.CLERK_SECRET_KEY) {
      throw new AuthenticationError('Clerk secret key not configured')
    }

    const token = this.extractToken(request)
    if (!token) {
      throw new AuthenticationError('No authentication token provided')
    }

    try {
      // Use official Clerk SDK for token verification with environment-based authorized parties
      const authorizedParties = this.env.NODE_ENV === 'development'
        ? ['http://localhost:5173', 'http://127.0.0.1:5173']
        : ['https://synapse-ai-notes.pages.dev', 'https://synapse-ai-notes.com']

      const payload = await verifyToken(token, {
        secretKey: this.env.CLERK_SECRET_KEY,
        authorizedParties
      })

      // Log successful authentication
      const logger = new Logger(context.requestId)
      logger.info('Authentication successful', {
        userId: payload.sub,
        issuer: payload.iss
      })

      return payload
    } catch (error) {
      const logger = new Logger(context.requestId)
      logger.error('Authentication failed', error)
      throw new AuthenticationError('Invalid authentication token')
    }
  }

  // Optional authentication (doesn't throw if no token)
  async optionalAuthenticate(request: Request, context: RequestContext): Promise<VerifiedJWTPayload | null> {
    try {
      return await this.authenticate(request, context)
    } catch (error) {
      // Return null for optional auth failures
      return null
    }
  }

  // Create authentication middleware function
  createMiddleware(required: boolean = true) {
    return async (request: Request, context: RequestContext): Promise<VerifiedJWTPayload | null> => {
      if (required) {
        return await this.authenticate(request, context)
      } else {
        return await this.optionalAuthenticate(request, context)
      }
    }
  }
}

// CORS middleware for handling preflight requests
export class CORSMiddleware {
  private allowedOrigins: string[]
  private allowedMethods: string[]
  private allowedHeaders: string[]

  constructor(
    allowedOrigins: string[] = ['*'],
    allowedMethods: string[] = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: string[] = ['Content-Type', 'Authorization', 'X-Requested-With']
  ) {
    this.allowedOrigins = allowedOrigins
    this.allowedMethods = allowedMethods
    this.allowedHeaders = allowedHeaders
  }

  // Handle CORS preflight request
  handlePreflight(request: Request): Response {
    const origin = request.headers.get('Origin')
    const headers: Record<string, string> = {}

    // Set allowed origin
    if (this.allowedOrigins.includes('*') || (origin && this.allowedOrigins.includes(origin))) {
      headers['Access-Control-Allow-Origin'] = origin || '*'
    }

    // Set allowed methods
    headers['Access-Control-Allow-Methods'] = this.allowedMethods.join(', ')

    // Set allowed headers
    headers['Access-Control-Allow-Headers'] = this.allowedHeaders.join(', ')

    // Set max age for preflight cache
    headers['Access-Control-Max-Age'] = '86400'

    // Allow credentials
    headers['Access-Control-Allow-Credentials'] = 'true'

    return new Response(null, {
      status: 204,
      headers
    })
  }

  // Add CORS headers to response
  addCORSHeaders(response: Response, request: Request): Response {
    const origin = request.headers.get('Origin')
    const headers = new Headers(response.headers)

    // Set allowed origin
    if (this.allowedOrigins.includes('*') || (origin && this.allowedOrigins.includes(origin))) {
      headers.set('Access-Control-Allow-Origin', origin || '*')
    }

    // Allow credentials
    headers.set('Access-Control-Allow-Credentials', 'true')

    // Expose headers that the client can access
    headers.set('Access-Control-Expose-Headers', 'X-Request-ID')

    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers
    })
  }
}

// Request context middleware
export class RequestContextMiddleware {
  constructor() {
    // No environment needed for context creation
  }

  // Create request context with unique ID and timing
  createContext(request: Request): RequestContext {
    const requestId = crypto.randomUUID()
    const startTime = Date.now()

    // Log request start
    const logger = new Logger(requestId)
    logger.info('Request started', {
      method: request.method,
      url: request.url,
      userAgent: request.headers.get('User-Agent')
    })

    return {
      requestId,
      startTime
    }
  }

  // Log request completion
  logCompletion(context: RequestContext, response: Response): void {
    const duration = Date.now() - context.startTime
    const logger = new Logger(context.requestId)

    logger.info('Request completed', {
      status: response.status,
      duration: `${duration}ms`
    })
  }
}
