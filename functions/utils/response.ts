// ===========================================
// RESPONSE UTILITIES (Workers)
// ===========================================

import type { APIResponse } from '../types'

// Response builder class for consistent API responses
export class ResponseBuilder {
  // Success response
  static success<T>(data: T, message?: string, status: number = 200): Response {
    const response: APIResponse<T> = {
      success: true,
      data,
      ...(message && { message })
    }

    return new Response(JSON.stringify(response), {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  // Error response
  static error(error: string, status: number = 500, code?: string): Response {
    const response: APIResponse = {
      success: false,
      error,
      ...(code && { code })
    }

    return new Response(JSON.stringify(response), {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  }

  // Validation error response
  static validationError(message: string = 'Validation failed'): Response {
    return this.error(message, 400, 'VALIDATION_ERROR')
  }

  // Authentication error response
  static authError(message: string = 'Authentication required'): Response {
    return this.error(message, 401, 'AUTHENTICATION_ERROR')
  }

  // Authorization error response
  static authzError(message: string = 'Insufficient permissions'): Response {
    return this.error(message, 403, 'AUTHORIZATION_ERROR')
  }

  // Not found error response
  static notFound(message: string = 'Resource not found'): Response {
    return this.error(message, 404, 'NOT_FOUND')
  }

  // Method not allowed response
  static methodNotAllowed(allowedMethods: string[] = []): Response {
    const response = this.error('Method not allowed', 405, 'METHOD_NOT_ALLOWED')
    
    if (allowedMethods.length > 0) {
      response.headers.set('Allow', allowedMethods.join(', '))
    }
    
    return response
  }

  // Rate limit error response
  static rateLimitError(message: string = 'Rate limit exceeded'): Response {
    return this.error(message, 429, 'RATE_LIMIT_EXCEEDED')
  }

  // Internal server error response
  static internalError(message: string = 'Internal server error'): Response {
    return this.error(message, 500, 'INTERNAL_ERROR')
  }

  // Service unavailable response
  static serviceUnavailable(message: string = 'Service temporarily unavailable'): Response {
    return this.error(message, 503, 'SERVICE_UNAVAILABLE')
  }
}

// Request validation utilities
export class RequestValidator {
  // Validate JSON body
  static async validateJSON(request: Request): Promise<any> {
    const contentType = request.headers.get('Content-Type')
    
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error('Content-Type must be application/json')
    }

    try {
      const body = await request.json()
      return body
    } catch (error) {
      throw new Error('Invalid JSON in request body')
    }
  }

  // Validate required fields
  static validateRequiredFields(data: any, requiredFields: string[]): void {
    const missingFields: string[] = []

    for (const field of requiredFields) {
      if (data[field] === undefined || data[field] === null || data[field] === '') {
        missingFields.push(field)
      }
    }

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`)
    }
  }

  // Validate string length
  static validateStringLength(value: string, fieldName: string, minLength: number = 0, maxLength: number = Infinity): void {
    if (typeof value !== 'string') {
      throw new Error(`${fieldName} must be a string`)
    }

    if (value.length < minLength) {
      throw new Error(`${fieldName} must be at least ${minLength} characters long`)
    }

    if (value.length > maxLength) {
      throw new Error(`${fieldName} must be no more than ${maxLength} characters long`)
    }
  }

  // Validate email format
  static validateEmail(email: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format')
    }
  }

  // Validate URL format
  static validateURL(url: string): void {
    try {
      new URL(url)
    } catch {
      throw new Error('Invalid URL format')
    }
  }

  // Validate enum value
  static validateEnum<T>(value: T, allowedValues: T[], fieldName: string): void {
    if (!allowedValues.includes(value)) {
      throw new Error(`${fieldName} must be one of: ${allowedValues.join(', ')}`)
    }
  }

  // Validate request size
  static validateRequestSize(request: Request, maxSize: number = 10 * 1024 * 1024): void {
    const contentLength = request.headers.get('Content-Length')
    if (contentLength && parseInt(contentLength) > maxSize) {
      throw new Error(`Request too large. Maximum size: ${maxSize} bytes`)
    }
  }

  // Validate content type
  static validateContentType(request: Request, allowedTypes: string[]): void {
    const contentType = request.headers.get('Content-Type')
    if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
      throw new Error(`Invalid content type. Allowed: ${allowedTypes.join(', ')}`)
    }
  }
}

// Logging utilities
export class Logger {
  private requestId?: string

  constructor(requestId?: string) {
    this.requestId = requestId
  }

  private formatMessage(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString()
    const logData = {
      timestamp,
      level,
      message,
      requestId: this.requestId,
      ...data
    }
    return JSON.stringify(logData)
  }

  info(message: string, data?: any): void {
    console.log(this.formatMessage('INFO', message, data))
  }

  warn(message: string, data?: any): void {
    console.warn(this.formatMessage('WARN', message, data))
  }

  error(message: string, error?: Error | any, data?: any): void {
    const errorData = {
      ...data,
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack
      } : error
    }
    console.error(this.formatMessage('ERROR', message, errorData))
  }

  debug(message: string, data?: any): void {
    // Debug logging only in development (using globalThis for Workers compatibility)
    if ((globalThis as any).NODE_ENV === 'development') {
      console.debug(this.formatMessage('DEBUG', message, data))
    }
  }
}
