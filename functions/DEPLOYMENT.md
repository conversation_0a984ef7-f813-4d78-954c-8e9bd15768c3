# 部署指南 - Synapse AI Notes 后端

本指南提供了部署 Synapse AI Notes Cloudflare Workers 后端的全面说明。

## 前置条件

### 必需账户
- **Cloudflare 账户**，启用 Workers、D1 和 R2
- **Clerk 账户**，用于身份验证服务
- **Google Cloud 账户**，用于 Gemini AI API 访问

### 开发环境
- **Node.js 18+**（推荐 LTS 版本）
- **npm** 或 **yarn** 包管理器
- **Git** 用于版本控制

### 必需工具
- **Wrangler CLI**（Cloudflare Workers CLI）
- **代码编辑器**（推荐 VS Code）

## 环境设置

### 1. 安装 Wrangler CLI

```bash
# 全局安装
npm install -g wrangler

# 或使用 npx（推荐）
npx wrangler --version
```

### 2. 使用 Cloudflare 进行身份验证

```bash
# 登录 Cloudflare
wrangler auth login

# 验证身份验证
wrangler whoami
```

### 3. 克隆和设置项目

```bash
# 克隆仓库
git clone <repository-url>
cd synapse-ai-notes

# 导航到 functions 目录
cd functions

# 安装依赖项
npm install
```

## Cloudflare 配置

### 1. 创建 Cloudflare D1 数据库

```bash
# 创建 D1 数据库
wrangler d1 create synapse-ai-notes

# 记录输出中的数据库 ID
# 使用数据库 ID 更新 wrangler.toml
```

### 2. 创建 Cloudflare R2 存储桶

```bash
# 为文件存储创建 R2 存储桶
wrangler r2 bucket create synapse-uploads

# 验证存储桶创建
wrangler r2 bucket list
```

### 3. 更新 wrangler.toml 配置

编辑 `functions/wrangler.toml`：

```toml
name = "synapse-ai-notes"
main = "index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[env.production]
name = "synapse-ai-notes"

[env.development]
name = "synapse-ai-notes-dev"

[[d1_databases]]
binding = "DB"
database_name = "synapse-ai-notes"
database_id = "your-database-id-here"

[[r2_buckets]]
binding = "UPLOADS"
bucket_name = "synapse-uploads"

[vars]
NODE_ENV = "production"
CLERK_PUBLISHABLE_KEY = "your-clerk-publishable-key"

[secrets]
CLERK_SECRET_KEY = "your-clerk-secret-key"
GEMINI_API_KEY = "your-gemini-api-key"
```

## 数据库设置

### 1. 初始化数据库模式

```bash
# 应用数据库模式
wrangler d1 execute synapse-ai-notes --file=./schema.sql

# 验证表已创建
wrangler d1 execute synapse-ai-notes --command="SELECT name FROM sqlite_master WHERE type='table';"
```

### 2. 数据库模式概览

数据库包含以下表：
- `users` - 用户账户信息
- `user_data` - 个人条目记录
- `user_sessions` - 会话跟踪（可选）

## 环境变量配置

### 1. 设置 Cloudflare 密钥

```bash
# 设置 Clerk 密钥
wrangler secret put CLERK_SECRET_KEY

# 设置 Gemini API 密钥
wrangler secret put GEMINI_API_KEY

# 验证密钥已设置
wrangler secret list
```

### 2. 设置公共变量

```bash
# 设置 Clerk 可发布密钥
wrangler vars put CLERK_PUBLISHABLE_KEY "pk_test_..."

# 设置环境
wrangler vars put NODE_ENV "production"

# 列出所有变量
wrangler vars list
```

## 外部服务配置

### 1. Clerk 身份验证设置

1. **创建 Clerk 应用程序**：
   - 前往 [Clerk 仪表板](https://dashboard.clerk.com)
   - 创建新应用程序
   - 选择身份验证方法（邮箱、社交等）

2. **配置 JWT 设置**：
   - 在 Clerk 仪表板中前往 JWT 模板
   - 创建自定义模板或使用默认模板
   - 记录发行者 URL 和 JWKS 端点

3. **获取 API 密钥**：
   - 复制可发布密钥（以 `pk_` 开头）
   - 复制密钥（以 `sk_` 开头）

### 2. Google Gemini AI 设置

1. **启用 Gemini API**：
   - 前往 [Google AI Studio](https://aistudio.google.com)
   - 创建新项目或使用现有项目
   - 启用 Gemini API

2. **创建 API 密钥**：
   - 前往 API 密钥部分
   - 创建新 API 密钥
   - 仅限制密钥到 Gemini API（推荐）

3. **配置配额**：
   - 设置适当的速率限制
   - 在 Google Cloud 控制台中监控使用情况

## 部署过程

### 1. 开发部署

```bash
# 启动本地开发服务器
wrangler dev

# 本地测试端点
curl http://localhost:8787/api/health
```

### 2. 生产部署

```bash
# 部署到生产环境
wrangler deploy

# 使用特定环境部署
wrangler deploy --env production

# 验证部署
wrangler tail --format pretty
```

### 3. 部署验证

```bash
# 测试健康端点
curl https://your-worker.your-subdomain.workers.dev/api/health

# 检查日志
wrangler tail

# 监控指标
wrangler metrics
```

## 故障排除

### 常见问题

1. **数据库连接错误**：
   ```bash
   # 检查数据库绑定
   wrangler d1 info synapse-ai-notes
   
   # 测试数据库连接
   wrangler d1 execute synapse-ai-notes --command="SELECT 1;"
   ```

2. **身份验证失败**：
   ```bash
   # 验证 Clerk 配置
   wrangler secret list
   
   # 检查 JWT 令牌格式
   # 确保 CLERK_SECRET_KEY 正确设置
   ```

3. **AI 服务错误**：
   ```bash
   # 验证 Gemini API 密钥
   curl -H "Authorization: Bearer $GEMINI_API_KEY" \
        "https://generativelanguage.googleapis.com/v1/models"
   ```

### 调试命令

```bash
# 查看实时日志
wrangler tail --format pretty

# 检查 worker 状态
wrangler status

# 查看部署历史
wrangler deployments list

# 回滚部署
wrangler rollback [deployment-id]
```

## 安全考虑

### 1. API 安全

- 始终验证输入数据
- 仅使用 HTTPS
- 实施适当的 CORS 头
- 对 API 端点进行速率限制

### 2. 密钥管理

- 永远不要将密钥提交到版本控制
- 对敏感数据使用 Wrangler 密钥
- 定期轮换密钥
- 监控密钥访问

## 支持和资源

- **Cloudflare Workers 文档**：https://developers.cloudflare.com/workers/
- **Clerk 文档**：https://clerk.com/docs
- **Gemini AI 文档**：https://ai.google.dev/docs
- **Wrangler CLI 参考**：https://developers.cloudflare.com/workers/wrangler/

如需额外支持，请查看项目的问题跟踪器或联系开发团队。
