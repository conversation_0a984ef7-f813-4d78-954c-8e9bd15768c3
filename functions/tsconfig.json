{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "WebWorker"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": true, "noEmit": true, "preserveValueImports": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": true, "forceConsistentCasingInFileNames": true, "noUncheckedIndexedAccess": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "noPropertyAccessFromIndexSignature": true, "types": ["@cloudflare/workers-types"], "baseUrl": ".", "paths": {"@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules", "dist"]}