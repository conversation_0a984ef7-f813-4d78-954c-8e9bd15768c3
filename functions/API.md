# API 文档 - Synapse AI Notes 后端

本文档为 Synapse AI Notes Cloudflare Workers 后端提供全面的 API 文档。

## 基础 URL

```
生产环境: https://your-worker.your-subdomain.workers.dev
开发环境: http://localhost:8787
```

## 身份验证

所有 API 端点（除健康检查外）都需要使用 Clerk JWT 令牌进行身份验证。

### 身份验证头

```http
Authorization: Bearer <jwt_token>
```

### 获取 JWT 令牌

JWT 令牌从前端的 Clerk 身份验证中获取：

```javascript
const token = await window.Clerk.session.getToken();
```

## 内容类型

除非另有说明，所有 API 端点都接受并返回 JSON 数据。

```http
Content-Type: application/json
```

## 响应格式

所有 API 响应都遵循一致的格式：

### 成功响应

```json
{
  "success": true,
  "data": <response_data>,
  "message": "操作成功完成"
}
```

### 错误响应

```json
{
  "success": false,
  "error": "错误描述",
  "code": "ERROR_CODE"
}
```

## API 端点

### 1. 健康检查

检查 API 服务的健康状态。

**端点：** `GET /api/health`

**身份验证：** 不需要

**响应：**

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-01-01T00:00:00.000Z",
    "version": "1.0.0",
    "services": {
      "database": "healthy",
      "ai": "healthy"
    }
  },
  "message": "服务健康"
}
```

### 2. 分析内容

使用 AI 处理和分析内容以创建结构化条目。

**端点：** `POST /api/analyze`

**身份验证：** 可选（如果已验证则保存到数据库）

**请求体：**

```json
{
  "content": "明天下午2点我要和团队开会。别忘了买杂货。",
  "context": "general"
}
```

**参数：**

- `content` (string, 必需)：要分析的文本内容
- `context` (string, 可选)：分析上下文 (`general`, `notes`, `todos`, `calendar`)

**响应：**

```json
{
  "success": true,
  "data": {
    "blocks": [
      {
        "id": "session-id-1",
        "type": "calendar",
        "content": "明天下午2点与团队开会",
        "metadata": {
          "priority": "high",
          "dueDate": "2024-01-02",
          "time": "14:00",
          "tags": ["会议", "团队"],
          "category": "work"
        }
      },
      {
        "id": "session-id-2",
        "type": "todo",
        "content": "买杂货",
        "metadata": {
          "priority": "medium",
          "dueDate": "2024-01-02",
          "tags": ["购物", "杂货"],
          "category": "personal"
        }
      }
    ]
  },
  "message": "内容分析完成并成功创建条目"
}
```

### 3. 获取用户条目

检索已验证用户的所有条目。返回个人条目的扁平数组，每个条目包含其 ID、类型、标题、内容、元数据和时间戳。

**端点：** `GET /api/entries`

**身份验证：** 必需

**查询参数：**

- `limit` (number, 可选)：返回的最大条目数（默认：50）
- `offset` (number, 可选)：跳过的条目数（默认：0）

**响应：**

```json
{
  "success": true,
  "data": [
    {
      "id": "entry-id-1",
      "type": "calendar",
      "title": "团队会议",
      "content": "明天下午2点与团队开会",
      "metadata": {
        "priority": "high",
        "dueDate": "2024-01-02",
        "time": "14:00",
        "tags": ["会议", "团队"],
        "category": "work"
      },
      "createdAt": 1704067200000,
      "updatedAt": 1704067200000
    },
    {
      "id": "entry-id-2",
      "type": "todo",
      "title": "购物清单",
      "content": "买杂货",
      "metadata": {
        "priority": "medium",
        "dueDate": "2024-01-02",
        "tags": ["购物", "杂货"],
        "category": "personal"
      },
      "createdAt": 1704067180000,
      "updatedAt": 1704067180000
    }
  ]
}
```

### 4. 更新条目

通过 ID 更新特定条目。

**端点：** `PUT /api/entries/{id}` 或 `PATCH /api/entries/{id}`

**身份验证：** 必需

**请求体：**

```json
{
  "title": "更新的标题",
  "content": "更新的内容",
  "metadata": {
    "priority": "high",
    "tags": ["已更新", "重要"],
    "category": "work"
  }
}
```

**参数：**

- `title` (string, 可选)：条目的新标题
- `content` (string, 可选)：条目的新内容
- `metadata` (object, 可选)：条目的新元数据

**响应：**

```json
{
  "success": true,
  "data": {
    "id": "entry-id",
    "sessionId": "session-id",
    "type": "todo",
    "title": "更新的标题",
    "content": "更新的内容",
    "metadata": {
      "priority": "high",
      "tags": ["已更新", "重要"],
      "category": "work"
    },
    "createdAt": 1704067200000,
    "updatedAt": 1704067260000
  },
  "message": "条目更新成功"
}
```

### 5. 删除条目

通过条目 ID 删除特定的个人条目。此操作只删除指定的条目，不会影响同一会话中的其他条目。

**端点：** `DELETE /api/entries/{id}`

**身份验证：** 必需

**路径参数：**
- `id` (string, 必需)：要删除的条目 ID（如 `"entry-id-1"`）

**响应：**

```json
{
  "success": true,
  "data": {
    "id": "entry-id-1"
  },
  "message": "条目删除成功"
}
```

**错误响应：**

```json
{
  "success": false,
  "error": "条目未找到",
  "code": "NOT_FOUND"
}
```

## 多模态内容分析

`/api/analyze` 端点支持包括文本、图像和音频在内的多模态输入。

### 文本 + 图像分析

**请求 (multipart/form-data)：**

```http
POST /api/analyze
Content-Type: multipart/form-data

content: "分析这张图片"
image: <image_file>
context: "general"
```

### 文本 + 音频分析

**请求 (multipart/form-data)：**

```http
POST /api/analyze
Content-Type: multipart/form-data

content: "转录并分析这段音频"
audio: <audio_file>
context: "general"
```

### JSON 格式（Base64 编码）

**请求体：**

```json
{
  "content": "分析这张图片",
  "image": {
    "data": "base64_encoded_image_data",
    "mimeType": "image/jpeg"
  },
  "audio": {
    "data": "base64_encoded_audio_data",
    "mimeType": "audio/wav"
  },
  "context": "general"
}
```

## 错误代码

### HTTP 状态码

- `200` - 成功
- `201` - 已创建
- `400` - 错误请求
- `401` - 未授权
- `403` - 禁止访问
- `404` - 未找到
- `405` - 方法不允许
- `413` - 请求实体过大
- `429` - 请求过多
- `500` - 内部服务器错误

### 应用程序错误代码

- `VALIDATION_ERROR` - 无效的请求数据
- `AUTHENTICATION_ERROR` - 无效或缺失的身份验证
- `AUTHORIZATION_ERROR` - 权限不足
- `NOT_FOUND` - 资源未找到
- `METHOD_NOT_ALLOWED` - 不支持的 HTTP 方法
- `REQUEST_TOO_LARGE` - 请求大小超过限制
- `RATE_LIMIT_EXCEEDED` - 请求过多
- `AI_SERVICE_ERROR` - AI 处理失败
- `DATABASE_ERROR` - 数据库操作失败

## 速率限制

API 实施速率限制以防止滥用：

- **分析端点**：每用户每分钟 60 次请求
- **其他端点**：每用户每分钟 120 次请求
- **未验证请求**：每 IP 每分钟 10 次请求

速率限制头包含在响应中：

```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1704067260
```

## 请求大小限制

- **标准请求**：最大 10MB
- **媒体上传**：最大 50MB
- **文本内容**：最大 10,000 字符
- **标题**：最大 200 字符

## CORS 配置

API 支持跨域资源共享 (CORS)，配置如下：

- **允许的源**：可配置（开发环境默认允许所有源）
- **允许的方法**：GET, POST, PUT, PATCH, DELETE, OPTIONS
- **允许的头**：Authorization, Content-Type, X-Requested-With
- **凭据**：支持

## 使用示例

### JavaScript/TypeScript

```javascript
const API_BASE_URL = 'https://your-worker.your-subdomain.workers.dev';

// 从 Clerk 获取 JWT 令牌
const token = await window.Clerk.session.getToken();

// 分析内容
const analyzeResponse = await fetch(`${API_BASE_URL}/api/analyze`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    content: '明天下午2点我要和团队开会。'
  })
});

const analyzeData = await analyzeResponse.json();

// 获取条目
const entriesResponse = await fetch(`${API_BASE_URL}/api/entries`, {
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

const entriesData = await entriesResponse.json();

// 更新条目
const updateResponse = await fetch(`${API_BASE_URL}/api/entries/entry-id`, {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    content: '更新的内容',
    metadata: { priority: 'high' }
  })
});

const updateData = await updateResponse.json();
```

### cURL 示例

```bash
# 健康检查
curl https://your-worker.your-subdomain.workers.dev/api/health

# 分析内容（已验证）
curl -X POST https://your-worker.your-subdomain.workers.dev/api/analyze \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"content": "明天下午2点我要和团队开会。"}'

# 获取条目
curl https://your-worker.your-subdomain.workers.dev/api/entries \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"

# 更新条目
curl -X PUT https://your-worker.your-subdomain.workers.dev/api/entries/entry-id \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"content": "更新的内容", "metadata": {"priority": "high"}}'

# 删除条目
curl -X DELETE https://your-worker.your-subdomain.workers.dev/api/entries/entry-id-1 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 数据类型

### 条目类型

- `todo` - 任务或行动项
- `calendar` - 事件或约会
- `text` - 一般笔记或信息

### 优先级级别

- `low` - 低优先级
- `medium` - 中等优先级
- `high` - 高优先级

### 类别

- `work` - 工作相关内容
- `personal` - 个人内容
- `other` - 未分类内容

## 支持

如需 API 支持和问题：

- **文档**：本文档
- **问题**：GitHub 仓库问题
- **邮箱**：<EMAIL>

## 更新日志

### v1.0.0（当前版本）

- 初始 API 发布
- 使用 Gemini AI 进行内容分析
- 条目的 CRUD 操作
- 多模态输入支持
- 通过 Clerk 进行 JWT 身份验证
