{"name": "synapse-ai-notes", "version": "1.0.0", "description": "AI-powered note-taking app with automatic organization", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "deploy": "npm run build && wrangler pages deploy dist", "deploy:preview": "npm run build && wrangler pages deploy dist --env preview", "db:init": "wrangler d1 execute synapse-db-prod --file=./database/schema.sql", "db:init:preview": "wrangler d1 execute synapse-db-preview --file=./database/schema.sql", "functions:dev": "wrangler pages dev dist --compatibility-date=2024-01-15"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "zustand": "^4.4.7", "@clerk/clerk-react": "^4.30.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.4", "workbox-window": "^7.0.0", "wrangler": "^3.22.1"}, "keywords": ["ai", "notes", "pwa", "react", "typescript", "cloudflare"], "author": "Synapse Team", "license": "MIT"}